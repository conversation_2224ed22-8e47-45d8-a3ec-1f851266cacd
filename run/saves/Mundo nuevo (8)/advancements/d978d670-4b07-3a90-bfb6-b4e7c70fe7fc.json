{"minecraft:recipes/food/baked_potato_from_campfire_cooking": {"criteria": {"has_potato": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/food/cooked_porkchop": {"criteria": {"has_porkchop": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/building_blocks/oak_slab": {"criteria": {"has_planks": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/decorations/oak_sign": {"criteria": {"has_planks": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/redstone/oak_trapdoor": {"criteria": {"has_planks": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/misc/white_dye": {"criteria": {"has_bone_meal": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/redstone/oak_fence_gate": {"criteria": {"has_planks": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/food/baked_potato_from_smoking": {"criteria": {"has_potato": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/food/baked_potato": {"criteria": {"has_potato": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/redstone/oak_pressure_plate": {"criteria": {"has_planks": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/redstone/oak_door": {"criteria": {"has_planks": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/transportation/carrot_on_a_stick": {"criteria": {"has_carrot": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_lots_of_items": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/misc/stick": {"criteria": {"has_planks": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/building_blocks/oak_stairs": {"criteria": {"has_planks": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/decorations/oak_fence": {"criteria": {"has_planks": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/food/cooked_porkchop_from_campfire_cooking": {"criteria": {"has_porkchop": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:husbandry/obtain_netherite_hoe": {"criteria": {"netherite_hoe": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/food/cooked_porkchop_from_smoking": {"criteria": {"has_porkchop": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:plains": "2025-07-23 18:53:58 +0200"}, "done": false}, "minecraft:recipes/decorations/barrel": {"criteria": {"has_planks": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/redstone/oak_button": {"criteria": {"has_planks": "2025-07-23 18:53:57 +0200"}, "done": true}, "minecraft:recipes/building_blocks/bone_block": {"criteria": {"has_bone_meal": "2025-07-23 18:53:57 +0200"}, "done": true}, "DataVersion": 4440}