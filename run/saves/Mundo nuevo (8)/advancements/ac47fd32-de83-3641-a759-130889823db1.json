{"minecraft:recipes/food/baked_potato_from_campfire_cooking": {"criteria": {"has_potato": "2025-07-23 18:33:48 +0200"}, "done": true}, "minecraft:recipes/food/cooked_porkchop": {"criteria": {"has_porkchop": "2025-07-23 18:33:48 +0200"}, "done": true}, "minecraft:recipes/misc/white_dye": {"criteria": {"has_bone_meal": "2025-07-23 18:33:48 +0200"}, "done": true}, "minecraft:recipes/food/baked_potato_from_smoking": {"criteria": {"has_potato": "2025-07-23 18:33:48 +0200"}, "done": true}, "minecraft:recipes/food/baked_potato": {"criteria": {"has_potato": "2025-07-23 18:33:48 +0200"}, "done": true}, "minecraft:recipes/transportation/carrot_on_a_stick": {"criteria": {"has_carrot": "2025-07-23 18:33:48 +0200"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-23 18:33:48 +0200"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_lots_of_items": "2025-07-23 18:33:48 +0200"}, "done": true}, "minecraft:adventure/root": {"criteria": {"killed_something": "2025-07-23 18:35:55 +0200"}, "done": true}, "minecraft:adventure/kill_all_mobs": {"criteria": {"minecraft:zombie": "2025-07-23 18:35:55 +0200"}, "done": false}, "minecraft:recipes/food/cooked_porkchop_from_campfire_cooking": {"criteria": {"has_porkchop": "2025-07-23 18:33:48 +0200"}, "done": true}, "minecraft:husbandry/obtain_netherite_hoe": {"criteria": {"netherite_hoe": "2025-07-23 18:33:48 +0200"}, "done": true}, "minecraft:recipes/food/cooked_porkchop_from_smoking": {"criteria": {"has_porkchop": "2025-07-23 18:33:48 +0200"}, "done": true}, "minecraft:adventure/kill_a_mob": {"criteria": {"minecraft:zombie": "2025-07-23 18:35:55 +0200"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:plains": "2025-07-23 18:33:49 +0200"}, "done": false}, "minecraft:recipes/building_blocks/bone_block": {"criteria": {"has_bone_meal": "2025-07-23 18:33:48 +0200"}, "done": true}, "DataVersion": 4440}