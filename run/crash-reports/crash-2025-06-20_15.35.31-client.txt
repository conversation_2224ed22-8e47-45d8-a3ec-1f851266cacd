---- Minecraft Crash Report ----
// You should try our sister game, <PERSON><PERSON>aft!

Time: 2025-06-20 15:35:31
Description: Client shutdown

java.lang.Error: Watchdog
	at java.base@23.0.2/java.lang.Thread.sleepNanos0(Native Method)
	at java.base@23.0.2/java.lang.Thread.sleepNanos(Thread.java:496)
	at java.base@23.0.2/java.lang.Thread.sleep(Thread.java:527)
	at knot//net.minecraft.client.MinecraftClient.startIntegratedServer(MinecraftClient.java:2150)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:411)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:406)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.method_57777(IntegratedServerLoader.java:373)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader$$Lambda/0x0000012d3d2376e0.accept(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$UniAccept.tryFire(CompletableFuture.java:762)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:526)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:106)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture.uniAcceptNow(CompletableFuture.java:798)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture.uniAcceptStage(CompletableFuture.java:779)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture.thenAcceptAsync(CompletableFuture.java:2267)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:371)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.checkBackupAndStart(IntegratedServerLoader.java:360)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:342)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:314)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:279)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:245)
	at knot//net.minecraft.client.gui.screen.world.WorldListWidget$WorldEntry.play(WorldListWidget.java:432)
	at knot//net.minecraft.client.gui.screen.world.WorldListWidget$WorldEntry.mouseClicked(WorldListWidget.java:409)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.gui.widget.ContainerWidget.mouseClicked(ContainerWidget.java:57)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.Mouse.mixinextras$bridge$mouseClicked$38(Mouse.java)
	at knot//net.minecraft.client.Mouse$$Lambda/0x0000012d3d082168.call(Unknown Source)
	at knot//net.minecraft.client.Mouse.wrapOperation$zml000$fabric-screen-api-v1$invokeMouseClickedEvents(Mouse.java:545)
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.client.Mouse$$Lambda/0x0000012d3d081f40.run(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:106)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//net.minecraft.client.Mouse$$Lambda/0x0000012d3ce4c000.invoke(Unknown Source)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:179)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1383)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:947)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000012d3c019800.invokeStaticInit(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000012d3c002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at app//net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at app//net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000012d3c002400.invokeStatic(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000012d3c002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.devlaunchinjector.Main.main(Main.java:86)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Client shutdown watchdog
Stacktrace:
	at java.base@23.0.2/java.lang.Thread.sleepNanos0(Native Method)
	at java.base@23.0.2/java.lang.Thread.sleepNanos(Thread.java:496)
	at java.base@23.0.2/java.lang.Thread.sleep(Thread.java:527)
	at knot//net.minecraft.client.MinecraftClient.startIntegratedServer(MinecraftClient.java:2150)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:411)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:406)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.method_57777(IntegratedServerLoader.java:373)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader$$Lambda/0x0000012d3d2376e0.accept(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$UniAccept.tryFire(CompletableFuture.java:762)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:526)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:106)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture.uniAcceptNow(CompletableFuture.java:798)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture.uniAcceptStage(CompletableFuture.java:779)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture.thenAcceptAsync(CompletableFuture.java:2267)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:371)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.checkBackupAndStart(IntegratedServerLoader.java:360)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:342)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:314)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:279)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:245)
	at knot//net.minecraft.client.gui.screen.world.WorldListWidget$WorldEntry.play(WorldListWidget.java:432)
	at knot//net.minecraft.client.gui.screen.world.WorldListWidget$WorldEntry.mouseClicked(WorldListWidget.java:409)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.gui.widget.ContainerWidget.mouseClicked(ContainerWidget.java:57)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.Mouse.mixinextras$bridge$mouseClicked$38(Mouse.java)
	at knot//net.minecraft.client.Mouse$$Lambda/0x0000012d3d082168.call(Unknown Source)
	at knot//net.minecraft.client.Mouse.wrapOperation$zml000$fabric-screen-api-v1$invokeMouseClickedEvents(Mouse.java:545)
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.client.Mouse$$Lambda/0x0000012d3d081f40.run(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:106)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//net.minecraft.client.Mouse$$Lambda/0x0000012d3ce4c000.invoke(Unknown Source)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:179)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1383)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:947)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000012d3c019800.invokeStaticInit(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000012d3c002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at app//net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at app//net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)

-- Thread Dump --
Details:
	Threads: "Render thread" prio=10 Id=1 TIMED_WAITING
	at java.base@23.0.2/java.lang.Thread.sleepNanos0(Native Method)
	at java.base@23.0.2/java.lang.Thread.sleepNanos(Thread.java:496)
	at java.base@23.0.2/java.lang.Thread.sleep(Thread.java:527)
	at knot//net.minecraft.client.MinecraftClient.startIntegratedServer(MinecraftClient.java:2150)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:411)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:406)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.method_57777(IntegratedServerLoader.java:373)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader$$Lambda/0x0000012d3d2376e0.accept(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$UniAccept.tryFire(CompletableFuture.java:762)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:526)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:106)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture.uniAcceptNow(CompletableFuture.java:798)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture.uniAcceptStage(CompletableFuture.java:779)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture.thenAcceptAsync(CompletableFuture.java:2267)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:371)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.checkBackupAndStart(IntegratedServerLoader.java:360)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:342)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:314)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:279)
	at knot//net.minecraft.server.integrated.IntegratedServerLoader.start(IntegratedServerLoader.java:245)
	at knot//net.minecraft.client.gui.screen.world.WorldListWidget$WorldEntry.play(WorldListWidget.java:432)
	at knot//net.minecraft.client.gui.screen.world.WorldListWidget$WorldEntry.mouseClicked(WorldListWidget.java:409)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.gui.widget.ContainerWidget.mouseClicked(ContainerWidget.java:57)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.Mouse.mixinextras$bridge$mouseClicked$38(Mouse.java)
	at knot//net.minecraft.client.Mouse$$Lambda/0x0000012d3d082168.call(Unknown Source)
	at knot//net.minecraft.client.Mouse.wrapOperation$zml000$fabric-screen-api-v1$invokeMouseClickedEvents(Mouse.java:545)
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.client.Mouse$$Lambda/0x0000012d3d081f40.run(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:106)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//net.minecraft.client.Mouse$$Lambda/0x0000012d3ce4c000.invoke(Unknown Source)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:179)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1383)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:947)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000012d3c019800.invokeStaticInit(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000012d3c002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at app//net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at app//net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000012d3c002400.invokeStatic(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000012d3c002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.devlaunchinjector.Main.main(Main.java:86)


"Reference Handler" daemon prio=10 Id=9 RUNNABLE
	at java.base@23.0.2/java.lang.ref.Reference.waitForReferencePendingList(Native Method)
	at java.base@23.0.2/java.lang.ref.Reference.processPendingReferences(Reference.java:246)
	at java.base@23.0.2/java.lang.ref.Reference$ReferenceHandler.run(Reference.java:208)


"Finalizer" daemon prio=8 Id=10 WAITING on java.lang.ref.NativeReferenceQueue$Lock@5fbb4188
	at java.base@23.0.2/java.lang.Object.wait0(Native Method)
	-  waiting on java.lang.ref.NativeReferenceQueue$Lock@5fbb4188
	at java.base@23.0.2/java.lang.Object.wait(Object.java:378)
	at java.base@23.0.2/java.lang.Object.wait(Object.java:352)
	at java.base@23.0.2/java.lang.ref.NativeReferenceQueue.await(NativeReferenceQueue.java:48)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:166)
	at java.base@23.0.2/java.lang.ref.NativeReferenceQueue.remove(NativeReferenceQueue.java:89)
	at java.base@23.0.2/java.lang.ref.Finalizer$FinalizerThread.run(Finalizer.java:173)


"Signal Dispatcher" daemon prio=9 Id=11 RUNNABLE


"Attach Listener" daemon prio=5 Id=12 RUNNABLE


"Notification Thread" daemon prio=9 Id=19 RUNNABLE


"Common-Cleaner" daemon prio=8 Id=20 TIMED_WAITING on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@4075f17f
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@4075f17f
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:79)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:151)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:229)
	at java.base@23.0.2/jdk.internal.ref.CleanerImpl.run(CleanerImpl.java:140)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)
	at java.base@23.0.2/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)


"JNA Cleaner" daemon prio=5 Id=44 TIMED_WAITING on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@31a795f3
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@31a795f3
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:79)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:151)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:229)
	at knot//com.sun.jna.internal.Cleaner$CleanerThread.run(Cleaner.java:154)


"Timer hack thread" daemon prio=5 Id=45 TIMED_WAITING
	at java.base@23.0.2/java.lang.Thread.sleepNanos0(Native Method)
	at java.base@23.0.2/java.lang.Thread.sleepNanos(Thread.java:496)
	at java.base@23.0.2/java.lang.Thread.sleep(Thread.java:527)
	at knot//net.minecraft.util.Util$9.run(Util.java)


"Yggdrasil Key Fetcher" daemon prio=5 Id=47 TIMED_WAITING on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@25b2c1a9
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@25b2c1a9
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
	at java.base@23.0.2/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
	at java.base@23.0.2/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Sound engine" daemon prio=10 Id=63 WAITING on java.lang.String@20806396
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.lang.String@20806396
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221)
	at knot//net.minecraft.client.sound.SoundExecutor.waitForTasks(SoundExecutor.java:49)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:140)
	at knot//net.minecraft.client.sound.SoundExecutor.waitForStop(SoundExecutor.java:42)
	at knot//net.minecraft.client.sound.SoundExecutor$$Lambda/0x0000012d3ce6fc98.run(Unknown Source)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Server thread" prio=8 Id=64 TIMED_WAITING on java.lang.String@20806396
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.lang.String@20806396
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at knot//net.minecraft.util.thread.ThreadExecutor.waitForTasks(ThreadExecutor.java:149)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:140)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//AiVillagers.OutpostNearVillageGenerator.lambda$onChunkLoad$0(OutpostNearVillageGenerator.java:54)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3d3077a8.get(Unknown Source)
	at knot//net.minecraft.util.thread.ThreadExecutor.submit(ThreadExecutor.java:68)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:53)
	at knot//AiVillagers.OutpostNearVillageGenerator$$Lambda/0x0000012d3cd1ae68.onChunkLoad(Unknown Source)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents$$Lambda/0x0000012d3cd18e98.onChunkLoad(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$bae000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at knot//net.minecraft.world.chunk.ChunkGenerating$$Lambda/0x0000012d3d307120.get(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.executeQueuedTasks(ServerChunkManager.java:266)
	at knot//net.minecraft.server.MinecraftServer.runOneTask(MinecraftServer.java:859)
	at knot//net.minecraft.server.MinecraftServer.runTask(MinecraftServer.java)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.MinecraftServer.runTasks(MinecraftServer.java:812)
	at knot//net.minecraft.server.MinecraftServer.runTasksTillTickEnd(MinecraftServer.java:819)
	at knot//net.minecraft.server.MinecraftServer.prepareStartRegion(MinecraftServer.java:511)
	at knot//net.minecraft.server.MinecraftServer.loadWorld(MinecraftServer.java:385)
	at knot//net.minecraft.server.integrated.IntegratedServer.setupServer(IntegratedServer.java:73)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:675)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java)
	at knot//net.minecraft.server.MinecraftServer$$Lambda/0x0000012d3d240468.run(Unknown Source)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Client shutdown watchdog" daemon prio=10 Id=68 RUNNABLE
	at java.management@23.0.2/sun.management.ThreadImpl.dumpThreads0(Native Method)
	at java.management@23.0.2/sun.management.ThreadImpl.dumpAllThreads(ThreadImpl.java:518)
	at java.management@23.0.2/sun.management.ThreadImpl.dumpAllThreads(ThreadImpl.java:506)
	at knot//net.minecraft.server.dedicated.DedicatedServerWatchdog.createCrashReport(DedicatedServerWatchdog.java:70)
	at knot//net.minecraft.client.ClientWatchdog.method_61935(ClientWatchdog.java:26)
	at knot//net.minecraft.client.ClientWatchdog$$Lambda/0x0000012d3d1b33b0.run(Unknown Source)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)



Stacktrace:
	at knot//net.minecraft.server.dedicated.DedicatedServerWatchdog.createCrashReport(DedicatedServerWatchdog.java:81)
	at knot//net.minecraft.client.ClientWatchdog.method_61935(ClientWatchdog.java:26)
	at java.base/java.lang.Thread.run(Thread.java:1575)

-- System Details --
Details:
	Minecraft Version: 1.21.6
	Minecraft Version ID: 1.21.6
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 92943864 bytes (88 MiB) / 587202560 bytes (560 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6874
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 11429.69
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 4009.10
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 175231.66, total: 389296.00
	Space in storage for workdir (MiB): available: 175231.66, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: AiVillagersFabric 1.0.0
		fabric-api: Fabric API 0.127.0+1.21.6
		fabric-api-base: Fabric API Base 0.4.63+9ec45cd89c
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.99+9ec45cd89c
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.10+fa6cb72b9c
		fabric-block-api-v1: Fabric Block API (v1) 1.1.2+78dbe4fb9c
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.30+d32f812d9c
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.3+458b8c9a9c
		fabric-command-api-v2: Fabric Command API (v2) 2.2.52+b39a696a9c
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.17+fa6cb72b9c
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.37+7f945d5b9c
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.2+d9a896309c
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.14+fa6cb72b9c
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.8+d9a896309c
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 23.2.0+a12c79229c
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.18+75fa737a9c
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.0+c9e472739c
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.22+0d4d74479c
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.71+9ec45cd89c
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.8+39ce47f59c
		fabric-item-api-v1: Fabric Item API (v1) 11.4.2+5e29f1899c
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.12+9ec45cd89c
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.64+9ec45cd89c
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.2+db4dfd859c
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.54+3f89f5a59c
		fabric-loot-api-v3: Fabric Loot API (v3) 2.0.1+f40817309c
		fabric-message-api-v1: Fabric Message API (v1) 6.1.0+8efa0e499c
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.4+ae8be2b89c
		fabric-networking-api-v1: Fabric Networking API (v1) 5.0.0+d32f812d9c
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.5+946cf7899c
		fabric-particles-v1: Fabric Particles (v1) 4.1.6+c1dce2189c
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.13+39ce47f59c
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.25+9ec45cd89c
		fabric-renderer-api-v1: Fabric Renderer API (v1) 7.0.1+a0cfcc829c
		fabric-renderer-indigo: Fabric Renderer - Indigo 4.0.1+2516f2229c
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.29+fa6cb72b9c
		fabric-rendering-v1: Fabric Rendering (v1) 12.3.0+ac3e15d19c
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.23+908cbc919c
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.10+fa6cb72b9c
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.50+908cbc919c
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.134+d32f812d9c
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.41+d32f812d9c
		fabric-tag-api-v1: Fabric Tag API (v1) 1.2.0+75110b049c
		fabric-transfer-api-v1: Fabric Transfer API (v1) 6.0.4+074c84ee9c
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.4.0+ac3e15d19c
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.6
		mixinextras: MixinExtras 0.4.1