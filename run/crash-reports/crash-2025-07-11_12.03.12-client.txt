---- Minecraft Crash Report ----
// Daisy, daisy...

Time: 2025-07-11 12:03:12
Description: Initializing game

java.lang.RuntimeException: Could not execute entrypoint stage 'main' due to errors, provided by 'ai_villagers' at 'AiVillagers.ExampleMod'!
	at net.fabricmc.loader.impl.FabricLoaderImpl.lambda$invokeEntrypoints$2(FabricLoaderImpl.java:403)
	at net.fabricmc.loader.impl.util.ExceptionUtil.gatherExceptions(ExceptionUtil.java:33)
	at net.fabricmc.loader.impl.FabricLoaderImpl.invokeEntrypoints(FabricLoaderImpl.java:401)
	at net.fabricmc.loader.impl.game.minecraft.Hooks.startClient(Hooks.java:52)
	at knot//net.minecraft.client.MinecraftClient.<init>(MinecraftClient.java:481)
	at knot//net.minecraft.client.main.Main.main(Main.java:249)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)
Caused by: java.lang.NullPointerException: Item id not set
	at java.base/java.util.Objects.requireNonNull(Objects.java:246)
	at knot//net.minecraft.item.Item$Settings.getTranslationKey(Item.java)
	at knot//net.minecraft.item.Item.<init>(Item.java:169)
	at knot//AiVillagers.items.AiVillagersItems$InternalVillagerItem.<init>(AiVillagersItems.java:35)
	at knot//AiVillagers.items.AiVillagersItems.registerItems(AiVillagersItems.java:21)
	at knot//AiVillagers.ExampleMod.onInitialize(ExampleMod.java:23)
	at net.fabricmc.loader.impl.FabricLoaderImpl.invokeEntrypoints(FabricLoaderImpl.java:399)
	... 7 more


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Render thread
Stacktrace:
	at net.fabricmc.loader.impl.FabricLoaderImpl.lambda$invokeEntrypoints$2(FabricLoaderImpl.java:403)
	at net.fabricmc.loader.impl.util.ExceptionUtil.gatherExceptions(ExceptionUtil.java:33)
	at net.fabricmc.loader.impl.FabricLoaderImpl.invokeEntrypoints(FabricLoaderImpl.java:401)
	at net.fabricmc.loader.impl.game.minecraft.Hooks.startClient(Hooks.java:52)
	at knot//net.minecraft.client.MinecraftClient.<init>(MinecraftClient.java:481)

-- Initialization --
Details:
	Modules: 
		ADVAPI32.dll:API base de Windows 32 avanzado:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		COMCTL32.dll:Biblioteca de controles de la experiencia del usuario:6.10 (WinBuild.160101.0800):Microsoft Corporation
		CRYPT32.dll:Crypto API32:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		CRYPTBASE.dll:Base cryptographic API DLL:10.0.26100.4061 (WinBuild.160101.0800):Microsoft Corporation
		CRYPTSP.dll:Cryptographic Service Provider API:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		DBGHELP.DLL:Windows Image Helper:10.0.26100.4202 (WinBuild.160101.0800):Microsoft Corporation
		DNSAPI.dll:DLL de API de cliente DNS:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		GDI32.dll:GDI Client DLL:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		IMM32.DLL:Multi-User Windows IMM32 API Client DLL:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		IPHLPAPI.DLL:API auxiliar para IP:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		KERNEL32.DLL:Archivo DLL de cliente API BASE de Windows NT:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		KERNELBASE.dll:Archivo DLL de cliente API BASE de Windows NT:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		MpOav.dll:IOfficeAntiVirus Module:4.18.25050.5 (bcf51ab773be21957c5713cae9cb3adf2fd75bf5):Microsoft Corporation
		NSI.dll:NSI User-mode interface DLL:10.0.26100.4061 (WinBuild.160101.0800):Microsoft Corporation
		NTASN1.dll:Microsoft ASN.1 API:10.0.26100.1 (WinBuild.160101.0800):Microsoft Corporation
		OLEAUT32.dll:OLEAUT32.DLL:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		POWRPROF.dll:Archivo DLL auxiliar del perfil de energía:10.0.26100.4202 (WinBuild.160101.0800):Microsoft Corporation
		PSAPI.DLL:Process Status Helper:10.0.26100.1 (WinBuild.160101.0800):Microsoft Corporation
		Pdh.dll:Ayudante de los datos de rendimiento DLL de Windows:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		RPCRT4.dll:Tiempo de ejecución de llamada a procedimiento remoto:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		SHCORE.dll:SHCORE:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		SHELL32.dll:Archivo DLL común del shell de Windows:10.0.26100.4202 (WinBuild.160101.0800):Microsoft Corporation
		UMPDC.dll:User Mode Power Dependency Coordinator:10.0.26100.1301 (WinBuild.160101.0800):Microsoft Corporation
		USER32.dll:Archivo DLL de cliente API USER de Windows multiusuario:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		USERENV.dll:Userenv:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		VCRUNTIME140.dll:Microsoft® C Runtime Library:14.36.32532.0:Microsoft Corporation
		VERSION.dll:Version Checking and File Installation Libraries:10.0.26100.1150 (WinBuild.160101.0800):Microsoft Corporation
		WINMM.dll:MCI API DLL:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		WS2_32.dll:DLL de 32 bits de Windows Socket 2.0:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		amsi.dll:Anti-Malware Scan Interface:10.0.26100.1150 (WinBuild.160101.0800):Microsoft Corporation
		bcrypt.dll:Biblioteca de primitivas criptográficas de Windows:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		bcryptPrimitives.dll:Windows Cryptographic Primitives Library:10.0.26100.4202 (WinBuild.160101.0800):Microsoft Corporation
		clbcatq.dll:COM+ Configuration Catalog:2001.12.10941.16384 (WinBuild.160101.0800):Microsoft Corporation
		combase.dll:Microsoft COM para Windows:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		dbgcore.DLL:Windows Core Debugging Helpers:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		extnet.dll:Java(TM) Platform SE binary:********:N/A
		fwpuclnt.dll:API de modo usuario de FWP/IPsec:10.0.26100.3915 (WinBuild.160101.0800):Microsoft Corporation
		gdi32full.dll:GDI Client DLL:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		java.dll:Java(TM) Platform SE binary:********:N/A
		java.exe:Java(TM) Platform SE binary:********:N/A
		jimage.dll:Java(TM) Platform SE binary:********:N/A
		jli.dll:Java(TM) Platform SE binary:********:N/A
		jna8763787303825096943.dll:JNA native library:7.0.2:Java(TM) Native Access (JNA)
		jsvml.dll:Java(TM) Platform SE binary:********:N/A
		jvm.dll:Java HotSpot(TM) 64-Bit server VM:********:N/A
		kernel.appcore.dll:AppModel API Host:10.0.26100.4202 (WinBuild.160101.0800):Microsoft Corporation
		management.dll:Java(TM) Platform SE binary:********:N/A
		management_ext.dll:Java(TM) Platform SE binary:********:N/A
		msvcp140.dll:Microsoft® C Runtime Library:14.36.32532.0:Microsoft Corporation
		msvcp_win.dll:Microsoft® C Runtime Library:10.0.26100.4202 (WinBuild.160101.0800):Microsoft Corporation
		msvcrt.dll:Windows NT CRT DLL:7.0.26100.4202 (WinBuild.160101.0800):Microsoft Corporation
		mswsock.dll:Proveedor de servicios de Microsoft Windows Sockets 2.0:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		napinsp.dll:Proveedor de correcciones de compatibilidad (shim) de nomenclaturas de correo electrónico:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		ncrypt.dll:Enrutador de Windows NCrypt:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		net.dll:Java(TM) Platform SE binary:********:N/A
		nio.dll:Java(TM) Platform SE binary:********:N/A
		nlansp_c.dll:NLA Namespace Service Provider DLL:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		ntdll.dll:DLL del nivel de Windows NT:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		ole32.dll:Microsoft OLE para Windows:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		perfos.dll:DLL de objetos de rendimiento del sistema Windows:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		pfclient.dll:SysMain Client:10.0.26100.4202 (WinBuild.160101.0800):Microsoft Corporation
		profapi.dll:User Profile Basic API:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		rasadhlp.dll:Remote Access AutoDial Helper:10.0.26100.1150 (WinBuild.160101.0800):Microsoft Corporation
		rsaenh.dll:Microsoft Enhanced Cryptographic Provider:10.0.26100.1 (WinBuild.160101.0800):Microsoft Corporation
		sechost.dll:Host for SCM/SDDL/LSA Lookup APIs:10.0.26100.1 (WinBuild.160101.0800):Microsoft Corporation
		shlwapi.dll:Biblioteca de utilidades de Shell:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		sunmscapi.dll:Java(TM) Platform SE binary:********:N/A
		ucrtbase.dll:Microsoft® C Runtime Library:10.0.26100.4202 (WinBuild.160101.0800):Microsoft Corporation
		vcruntime140_1.dll:Microsoft® C Runtime Library:14.36.32532.0:Microsoft Corporation
		verify.dll:Java(TM) Platform SE binary:********:N/A
		win32u.dll:Win32u:10.0.26100.4484 (WinBuild.160101.0800):Microsoft Corporation
		windows.storage.dll:API de almacenamiento de Microsoft WinRT:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		winrnr.dll:LDAP RnR Provider DLL:10.0.26100.1882 (WinBuild.160101.0800):Microsoft Corporation
		wintypes.dll:DLL de tipos basados en Windows:10.0.26100.2308 (WinBuild.160101.0800):Microsoft Corporation
		wshbth.dll:Windows Sockets Helper DLL:10.0.26100.4061 (WinBuild.160101.0800):Microsoft Corporation
		zip.dll:Java(TM) Platform SE binary:********:N/A
Stacktrace:
	at knot//net.minecraft.client.main.Main.main(Main.java:249)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- System Details --
Details:
	Minecraft Version: 1.21.6
	Minecraft Version ID: 1.21.6
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 179760960 bytes (171 MiB) / 412090368 bytes (393 MiB) up to ********** bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6913
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 11219.17
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 3576.98
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 183310.25, total: 389296.00
	Space in storage for workdir (MiB): available: 183310.25, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: AiVillagersFabric 1.0.0
		fabric-api: Fabric API 0.127.0+1.21.6
		fabric-api-base: Fabric API Base 0.4.63+9ec45cd89c
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.99+9ec45cd89c
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.10+fa6cb72b9c
		fabric-block-api-v1: Fabric Block API (v1) 1.1.2+78dbe4fb9c
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.30+d32f812d9c
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.3+458b8c9a9c
		fabric-command-api-v2: Fabric Command API (v2) 2.2.52+b39a696a9c
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.17+fa6cb72b9c
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.37+7f945d5b9c
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.2+d9a896309c
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.14+fa6cb72b9c
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.8+d9a896309c
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 23.2.0+a12c79229c
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.18+75fa737a9c
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.0+c9e472739c
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.22+0d4d74479c
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.71+9ec45cd89c
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.8+39ce47f59c
		fabric-item-api-v1: Fabric Item API (v1) 11.4.2+5e29f1899c
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.12+9ec45cd89c
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.64+9ec45cd89c
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.2+db4dfd859c
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.54+3f89f5a59c
		fabric-loot-api-v3: Fabric Loot API (v3) 2.0.1+f40817309c
		fabric-message-api-v1: Fabric Message API (v1) 6.1.0+8efa0e499c
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.4+ae8be2b89c
		fabric-networking-api-v1: Fabric Networking API (v1) 5.0.0+d32f812d9c
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.5+946cf7899c
		fabric-particles-v1: Fabric Particles (v1) 4.1.6+c1dce2189c
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.13+39ce47f59c
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.25+9ec45cd89c
		fabric-renderer-api-v1: Fabric Renderer API (v1) 7.0.1+a0cfcc829c
		fabric-renderer-indigo: Fabric Renderer - Indigo 4.0.1+2516f2229c
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.29+fa6cb72b9c
		fabric-rendering-v1: Fabric Rendering (v1) 12.3.0+ac3e15d19c
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.23+908cbc919c
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.10+fa6cb72b9c
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.50+908cbc919c
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.134+d32f812d9c
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.41+d32f812d9c
		fabric-tag-api-v1: Fabric Tag API (v1) 1.2.0+75110b049c
		fabric-transfer-api-v1: Fabric Transfer API (v1) 6.0.4+074c84ee9c
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.4.0+ac3e15d19c
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.6
		mixinextras: MixinExtras 0.4.1
	Launched Version: Fabric
	Backend library: LWJGL version 3.3.3-snapshot
	Backend API: Unknown
	Window size: <not initialized>
	GFLW Platform: <error>
	Render Extensions: ERR
	GL debug messages: <no renderer available>
	Is Modded: Definitely; Client brand changed to 'fabric'
	Universe: 404
	Type: Client (map_client.txt)
	Locale: es_ES
	System encoding: Cp1252
	File encoding: UTF-8
	CPU: 8x Intel(R) Core(TM) i3-N305