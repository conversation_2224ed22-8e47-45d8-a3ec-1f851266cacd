---- Minecraft Crash Report ----
// I bet <PERSON><PERSON> wouldn't have this problem.

Time: 2025-06-20 16:50:31
Description: Exception chunk generation/loading

java.lang.NullPointerException: Cannot invoke "net.minecraft.block.BlockState.isAir()" because the return value of "net.minecraft.block.pattern.CachedBlockPosition.getBlockState()" is null
	at knot//net.minecraft.block.CarvedPumpkinBlock.method_51167(CarvedPumpkinBlock.java:148)
	at knot//net.minecraft.block.pattern.BlockPattern.testTransform(BlockPattern.java:66)
	at knot//net.minecraft.block.pattern.BlockPattern.searchAround(BlockPattern.java:80)
	at knot//net.minecraft.block.CarvedPumpkinBlock.trySpawnEntity(CarvedPumpkinBlock.java:79)
	at knot//net.minecraft.block.CarvedPumpkinBlock.onBlockAdded(CarvedPumpkinBlock.java:62)
	at knot//net.minecraft.block.AbstractBlock$AbstractBlockState.onBlockAdded(AbstractBlock.java:1134)
	at knot//net.minecraft.world.chunk.WorldChunk.setBlockState(WorldChunk.java:292)
	at knot//net.minecraft.world.World.setBlockState(World.java:259)
	at knot//net.minecraft.world.World.setBlockState(World.java:247)
	at knot//net.minecraft.structure.StructureTemplate.place(StructureTemplate.java:275)
	at knot//net.minecraft.structure.pool.SinglePoolElement.generate(SinglePoolElement.java:130)
	at knot//net.minecraft.structure.PoolStructurePiece.generate(PoolStructurePiece.java:93)
	at knot//net.minecraft.structure.PoolStructurePiece.generate(PoolStructurePiece.java:89)
	at knot//net.minecraft.structure.StructureStart.place(StructureStart.java:105)
	at knot//AiVillagers.OutpostNearVillageGenerator.onChunkLoad(OutpostNearVillageGenerator.java:128)
	at knot//net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents.lambda$static$0(ServerChunkEvents.java:44)
	at knot//net.minecraft.world.chunk.ChunkGenerating.handler$zgl000$fabric-lifecycle-events-v1$onChunkLoad(ChunkGenerating.java:546)
	at knot//net.minecraft.world.chunk.ChunkGenerating.method_60553(ChunkGenerating.java:157)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1812)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.executeTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTask(ServerChunkManager.java:569)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.world.ServerChunkManager$MainThreadExecutor.runTasks(ServerChunkManager.java:569)
	at knot//net.minecraft.server.world.ServerChunkManager.getChunk(ServerChunkManager.java:158)
	at knot//net.minecraft.world.World.getChunk(World.java:239)
	at knot//net.minecraft.world.WorldView.getChunk(WorldView.java:181)
	at knot//net.minecraft.world.World.getChunk(World.java:232)
	at knot//net.minecraft.world.World.getBlockState(World.java:381)
	at knot//net.minecraft.entity.Entity.getVelocityMultiplier(Entity.java:1269)
	at knot//net.minecraft.entity.LivingEntity.getVelocityMultiplier(LivingEntity.java:481)
	at knot//net.minecraft.entity.player.PlayerEntity.getVelocityMultiplier(PlayerEntity.java:1860)
	at knot//net.minecraft.entity.Entity.move(Entity.java:1050)
	at knot//net.minecraft.server.network.ServerPlayNetworkHandler.onPlayerMove(ServerPlayNetworkHandler.java:1064)
	at knot//net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket.apply(PlayerMoveC2SPacket.java:64)
	at knot//net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket$Full.apply(PlayerMoveC2SPacket.java)
	at knot//net.minecraft.network.NetworkThreadUtils.method_11072(NetworkThreadUtils.java:30)
	at knot//net.minecraft.server.ServerTask.run(ServerTask.java:22)
	at knot//net.minecraft.util.thread.ThreadExecutor.executeTask(ThreadExecutor.java:155)
	at knot//net.minecraft.util.thread.ReentrantThreadExecutor.executeTask(ReentrantThreadExecutor.java:29)
	at knot//net.minecraft.server.MinecraftServer.executeTask(MinecraftServer.java:869)
	at knot//net.minecraft.server.MinecraftServer.executeTask(MinecraftServer.java:2031)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTask(ThreadExecutor.java:131)
	at knot//net.minecraft.server.MinecraftServer.runOneTask(MinecraftServer.java:854)
	at knot//net.minecraft.server.MinecraftServer.runTask(MinecraftServer.java)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:139)
	at knot//net.minecraft.server.MinecraftServer.runTasks(MinecraftServer.java:812)
	at knot//net.minecraft.server.MinecraftServer.runTasksTillTickEnd(MinecraftServer.java:819)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:711)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java)
	at java.base/java.lang.Thread.run(Thread.java:1575)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- System Details --
Details:
	Minecraft Version: 1.21.6
	Minecraft Version ID: 1.21.6
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 152452080 bytes (145 MiB) / 625999872 bytes (597 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6874
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 11773.36
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 3381.70
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 175270.50, total: 389296.00
	Space in storage for workdir (MiB): available: 175270.50, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: AiVillagersFabric 1.0.0
		fabric-api: Fabric API 0.127.0+1.21.6
		fabric-api-base: Fabric API Base 0.4.63+9ec45cd89c
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.99+9ec45cd89c
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.10+fa6cb72b9c
		fabric-block-api-v1: Fabric Block API (v1) 1.1.2+78dbe4fb9c
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.30+d32f812d9c
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.3+458b8c9a9c
		fabric-command-api-v2: Fabric Command API (v2) 2.2.52+b39a696a9c
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.17+fa6cb72b9c
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.37+7f945d5b9c
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.2+d9a896309c
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.14+fa6cb72b9c
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.8+d9a896309c
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 23.2.0+a12c79229c
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.18+75fa737a9c
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.0+c9e472739c
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.22+0d4d74479c
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.71+9ec45cd89c
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.8+39ce47f59c
		fabric-item-api-v1: Fabric Item API (v1) 11.4.2+5e29f1899c
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.12+9ec45cd89c
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.64+9ec45cd89c
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.2+db4dfd859c
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.54+3f89f5a59c
		fabric-loot-api-v3: Fabric Loot API (v3) 2.0.1+f40817309c
		fabric-message-api-v1: Fabric Message API (v1) 6.1.0+8efa0e499c
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.4+ae8be2b89c
		fabric-networking-api-v1: Fabric Networking API (v1) 5.0.0+d32f812d9c
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.5+946cf7899c
		fabric-particles-v1: Fabric Particles (v1) 4.1.6+c1dce2189c
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.13+39ce47f59c
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.25+9ec45cd89c
		fabric-renderer-api-v1: Fabric Renderer API (v1) 7.0.1+a0cfcc829c
		fabric-renderer-indigo: Fabric Renderer - Indigo 4.0.1+2516f2229c
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.29+fa6cb72b9c
		fabric-rendering-v1: Fabric Rendering (v1) 12.3.0+ac3e15d19c
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.23+908cbc919c
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.10+fa6cb72b9c
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.50+908cbc919c
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.134+d32f812d9c
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.41+d32f812d9c
		fabric-tag-api-v1: Fabric Tag API (v1) 1.2.0+75110b049c
		fabric-transfer-api-v1: Fabric Transfer API (v1) 6.0.4+074c84ee9c
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.4.0+ac3e15d19c
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.6
		mixinextras: MixinExtras 0.4.1
	Server Running: true
	Player Count: 1 / 8; [ServerPlayerEntity['Player618'/1, l='ServerLevel[Mundo nuevo]', x=272.50, y=-60.00, z=192.50]]
	Active Data Packs: vanilla, fabric, ai_villagers, fabric-convention-tags-v2, fabric-gametest-api-v1
	Available Data Packs: ai_villagers, fabric, fabric-convention-tags-v2, fabric-gametest-api-v1, minecart_improvements, redstone_experiments, trade_rebalance, vanilla
	Enabled Feature Flags: minecraft:vanilla
	World Generation: Stable
	World Seed: 2757544444209118116
	Suppressed Exceptions: 
		Latest entries:
			packet/serverbound/minecraft:move_player_pos_rot:class net.minecraft.util.crash.CrashException: Exception chunk generation/loading (14ms ago)
		Entry counts:
			packet/serverbound/minecraft:move_player_pos_rot:class net.minecraft.util.crash.CrashException x 1

	Type: Integrated Server (map_client.txt)
	Is Modded: Definitely; Client brand changed to 'fabric'; Server brand changed to 'fabric'
	Launched Version: Fabric