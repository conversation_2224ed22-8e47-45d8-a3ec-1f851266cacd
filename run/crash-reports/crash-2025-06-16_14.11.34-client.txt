---- Minecraft Crash Report ----
// There are four lights!

Time: 2025-06-16 14:11:34
Description: Registry Loading

java.lang.IllegalStateException: Failed to load registries due to errors
	at knot//net.minecraft.registry.RegistryLoader.createLoadingException(RegistryLoader.java:277)
	at knot//net.minecraft.registry.RegistryLoader.writeAndCreateLoadingException(RegistryLoader.java:258)
	at knot//net.minecraft.registry.RegistryLoader.load(RegistryLoader.java:225)
	at knot//net.minecraft.registry.RegistryLoader.mixinextras$bridge$load$39(RegistryLoader.java)
	at knot//net.minecraft.registry.RegistryLoader.wrapOperation$zfa000$fabric-registry-sync-v0$wrapIsServerCall(RegistryLoader.java:555)
	at knot//net.minecraft.registry.RegistryLoader.loadFromResource(RegistryLoader.java:190)
	at knot//net.minecraft.server.SaveLoading.load(SaveLoading.java:41)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:357)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:321)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:308)
	at knot//net.minecraft.client.gui.screen.world.SelectWorldScreen.method_19944(SelectWorldScreen.java:54)
	at knot//net.minecraft.client.gui.widget.ButtonWidget.onPress(ButtonWidget.java:96)
	at knot//net.minecraft.client.gui.widget.PressableWidget.onClick(PressableWidget.java:43)
	at knot//net.minecraft.client.gui.widget.ClickableWidget.mouseClicked(ClickableWidget.java:141)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.Mouse.mixinextras$bridge$mouseClicked$38(Mouse.java)
	at knot//net.minecraft.client.Mouse.wrapOperation$zbd000$fabric-screen-api-v1$invokeMouseClickedEvents(Mouse.java:545)
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:110)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwPollEvents(GLFW.java:3438)
	at knot//com.mojang.blaze3d.systems.RenderSystem.pollEvents(RenderSystem.java:135)
	at knot//com.mojang.blaze3d.systems.RenderSystem.flipFrame(RenderSystem.java:155)
	at knot//net.minecraft.client.util.Window.swapBuffers(Window.java:308)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1372)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:936)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Render thread
Stacktrace:
	at knot//net.minecraft.registry.RegistryLoader.createLoadingException(RegistryLoader.java:277)
	at knot//net.minecraft.registry.RegistryLoader.writeAndCreateLoadingException(RegistryLoader.java:258)
	at knot//net.minecraft.registry.RegistryLoader.load(RegistryLoader.java:225)
	at knot//net.minecraft.registry.RegistryLoader.mixinextras$bridge$load$39(RegistryLoader.java)
	at knot//net.minecraft.registry.RegistryLoader.wrapOperation$zfa000$fabric-registry-sync-v0$wrapIsServerCall(RegistryLoader.java:555)
	at knot//net.minecraft.registry.RegistryLoader.loadFromResource(RegistryLoader.java:190)
	at knot//net.minecraft.server.SaveLoading.load(SaveLoading.java:41)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:357)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:321)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:308)
	at knot//net.minecraft.client.gui.screen.world.SelectWorldScreen.method_19944(SelectWorldScreen.java:54)
	at knot//net.minecraft.client.gui.widget.ButtonWidget.onPress(ButtonWidget.java:96)
	at knot//net.minecraft.client.gui.widget.PressableWidget.onClick(PressableWidget.java:43)
	at knot//net.minecraft.client.gui.widget.ClickableWidget.mouseClicked(ClickableWidget.java:141)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.Mouse.mixinextras$bridge$mouseClicked$38(Mouse.java)
	at knot//net.minecraft.client.Mouse.wrapOperation$zbd000$fabric-screen-api-v1$invokeMouseClickedEvents(Mouse.java:545)
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:110)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwPollEvents(GLFW.java:3438)
	at knot//com.mojang.blaze3d.systems.RenderSystem.pollEvents(RenderSystem.java:135)
	at knot//com.mojang.blaze3d.systems.RenderSystem.flipFrame(RenderSystem.java:155)

-- Loading info --
Details:
	Errors: 
		minecraft:root/minecraft:worldgen/structure_set: Unbound values in registry ResourceKey[minecraft:root / minecraft:worldgen/structure_set]: [minecraft:village]
Stacktrace:
	at knot//net.minecraft.registry.RegistryLoader.createLoadingException(RegistryLoader.java:277)
	at knot//net.minecraft.registry.RegistryLoader.writeAndCreateLoadingException(RegistryLoader.java:258)
	at knot//net.minecraft.registry.RegistryLoader.load(RegistryLoader.java:225)
	at knot//net.minecraft.registry.RegistryLoader.mixinextras$bridge$load$39(RegistryLoader.java)
	at knot//net.minecraft.registry.RegistryLoader.wrapOperation$zfa000$fabric-registry-sync-v0$wrapIsServerCall(RegistryLoader.java:555)
	at knot//net.minecraft.registry.RegistryLoader.loadFromResource(RegistryLoader.java:190)
	at knot//net.minecraft.server.SaveLoading.load(SaveLoading.java:41)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:357)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:321)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:308)
	at knot//net.minecraft.client.gui.screen.world.SelectWorldScreen.method_19944(SelectWorldScreen.java:54)
	at knot//net.minecraft.client.gui.widget.ButtonWidget.onPress(ButtonWidget.java:96)
	at knot//net.minecraft.client.gui.widget.PressableWidget.onClick(PressableWidget.java:43)
	at knot//net.minecraft.client.gui.widget.ClickableWidget.mouseClicked(ClickableWidget.java:141)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.Mouse.mixinextras$bridge$mouseClicked$38(Mouse.java)
	at knot//net.minecraft.client.Mouse.wrapOperation$zbd000$fabric-screen-api-v1$invokeMouseClickedEvents(Mouse.java:545)
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:110)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwPollEvents(GLFW.java:3438)
	at knot//com.mojang.blaze3d.systems.RenderSystem.pollEvents(RenderSystem.java:135)
	at knot//com.mojang.blaze3d.systems.RenderSystem.flipFrame(RenderSystem.java:155)
	at knot//net.minecraft.client.util.Window.swapBuffers(Window.java:308)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1372)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:936)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Affected screen --
Details:
	Screen name: net.minecraft.client.gui.screen.world.SelectWorldScreen
Stacktrace:
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:110)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwPollEvents(GLFW.java:3438)
	at knot//com.mojang.blaze3d.systems.RenderSystem.pollEvents(RenderSystem.java:135)
	at knot//com.mojang.blaze3d.systems.RenderSystem.flipFrame(RenderSystem.java:155)
	at knot//net.minecraft.client.util.Window.swapBuffers(Window.java:308)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1372)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:936)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Mouse --
Details:
	Mouse location: Scaled: (283.000000, 198.500000). Absolute: (566.000000, 397.000000)
	Screen size: Scaled: (427, 240). Absolute: (854, 480). Scale factor of 2.000000
	Button: 0
Stacktrace:
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:110)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwPollEvents(GLFW.java:3438)
	at knot//com.mojang.blaze3d.systems.RenderSystem.pollEvents(RenderSystem.java:135)
	at knot//com.mojang.blaze3d.systems.RenderSystem.flipFrame(RenderSystem.java:155)
	at knot//net.minecraft.client.util.Window.swapBuffers(Window.java:308)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1372)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:936)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Uptime --
Details:
	JVM uptime: 51.492s
	Wall uptime: 32.038s
	High-res time: 30.590s
	Client ticks: 504 ticks / 25.200s
Stacktrace:
	at knot//net.minecraft.client.MinecraftClient.addDetailsToCrashReport(MinecraftClient.java:2382)
	at knot//net.minecraft.client.MinecraftClient.printCrashReport(MinecraftClient.java:1005)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:957)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Last reload --
Details:
	Reload number: 1
	Reload reason: initial
	Finished: Yes
	Packs: vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-blockrenderlayer-v1, fabric-client-gametest-api-v1, fabric-client-tags-api-v1, fabric-command-api-v1, fabric-command-api-v2, fabric-commands-v0, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-keybindings-v0, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-data-attachment-v1, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader

-- System Details --
Details:
	Minecraft Version: 1.21.5
	Minecraft Version ID: 1.21.5
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 150470656 bytes (143 MiB) / 899678208 bytes (858 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6874
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 9876.40
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 2153.78
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 173656.72, total: 389296.00
	Space in storage for workdir (MiB): available: 173656.72, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: AiVillagersFabric 1.0.0
		fabric-api: Fabric API 0.126.0+1.21.5
		fabric-api-base: Fabric API Base 0.4.62+73a52b4b49
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.96+86c3a9f149
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.7+2dd063df49
		fabric-block-api-v1: Fabric Block API (v1) 1.1.0+ed91556f49
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.26+aa6d566c49
		fabric-blockrenderlayer-v1: Fabric BlockRenderLayer Registration (v1) 2.0.16+86c3a9f149
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.0+dac2d6e349
		fabric-client-tags-api-v1: Fabric Client Tags 1.1.37+86c3a9f149
		fabric-command-api-v1: Fabric Command API (v1) 1.2.70+f71b366f49
		fabric-command-api-v2: Fabric Command API (v2) 2.2.49+73a52b4b49
		fabric-commands-v0: Fabric Commands (v0) 0.2.87+df3654b349
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.14+3e6c1f7d49
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.32+7f945d5b49
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.1+0570995b49
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.12+86c3a9f149
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.3+bc514c4549
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 22.4.2+ea72995749
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.17+3e6c1f7d49
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.0+3ce7866349
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.15+64e3057949
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.70+c327076a49
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.3+2a6ec84b49
		fabric-item-api-v1: Fabric Item API (v1) 11.4.1+e46fd76a49
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.9+3459fc6149
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.63+ecf51cdc49
		fabric-keybindings-v0: Fabric Key Bindings (v0) 0.2.61+df3654b349
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.0+230071a049
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.48+3f89f5a549
		fabric-loot-api-v3: Fabric Loot API (v3) 1.0.36+86c3a9f149
		fabric-message-api-v1: Fabric Message API (v1) 6.1.0+fe971bba49
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.1+5281b90749
		fabric-networking-api-v1: Fabric Networking API (v1) 4.5.0+775be32c49
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.1+b8d6ba7049
		fabric-particles-v1: Fabric Particles (v1) 4.1.2+112e550e49
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.8+3235ab3249
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.21+b556383249
		fabric-renderer-api-v1: Fabric Renderer API (v1) 6.1.0+5281b90749
		fabric-renderer-indigo: Fabric Renderer - Indigo 3.1.0+5281b90749
		fabric-rendering-data-attachment-v1: Fabric Rendering Data Attachment (v1) 0.3.64+73761d2e49
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.27+86c3a9f149
		fabric-rendering-v1: Fabric Rendering (v1) 11.1.11+5490746649
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.21+73a52b4b49
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.7+847e5f5c49
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.46+86c3a9f149
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.128+c327076a49
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.38+86c3a9f149
		fabric-tag-api-v1: Fabric Tag API (v1) 1.0.17+ecf51cdc49
		fabric-transfer-api-v1: Fabric Transfer API (v1) 5.4.24+7b20cbb049
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.3.17+f17a180c49
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.5
		mixinextras: MixinExtras 0.4.1
	Launched Version: Fabric
	Backend library: LWJGL version 3.3.3-snapshot
	Backend API: Intel(R) UHD Graphics GL version 3.2.0 - Build 32.0.101.6874, Intel
	Window size: 854x480
	GFLW Platform: win32
	Render Extensions: GL_KHR_debug, GL_ARB_vertex_attrib_binding, GL_ARB_direct_state_access
	GL debug messages: id=1282, source=API, type=ERROR, severity=HIGH, message='Error has been generated. GL error GL_INVALID_OPERATION in (null): (ID: 173538523) Generic error' x 9
	Is Modded: Definitely; Client brand changed to 'fabric'
	Universe: 400921fb54442d18
	Type: Client (map_client.txt)
	Graphics mode: fancy
	Render Distance: 4/4 chunks
	Resource Packs: vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-blockrenderlayer-v1, fabric-client-gametest-api-v1, fabric-client-tags-api-v1, fabric-command-api-v1, fabric-command-api-v2, fabric-commands-v0, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-keybindings-v0, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-data-attachment-v1, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader
	Current Language: es_es
	Locale: es_ES
	System encoding: Cp1252
	File encoding: UTF-8
	CPU: 8x Intel(R) Core(TM) i3-N305