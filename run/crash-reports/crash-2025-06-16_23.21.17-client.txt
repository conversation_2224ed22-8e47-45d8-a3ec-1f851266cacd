---- Minecraft Crash Report ----
// Uh... Did I do that?

Time: 2025-06-16 23:21:17
Description: Client shutdown

java.lang.Error: Watchdog
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:164)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1375)
	at knot//net.minecraft.client.MinecraftClient.disconnect(MinecraftClient.java:2196)
	at knot//net.minecraft.client.network.ClientCommonNetworkHandler.onDisconnected(ClientCommonNetworkHandler.java:314)
	at knot//net.minecraft.network.ClientConnection.handleDisconnection(ClientConnection.java:668)
	at knot//net.minecraft.client.network.ClientPlayerInteractionManager.tick(ClientPlayerInteractionManager.java:295)
	at knot//net.minecraft.client.MinecraftClient.tick(MinecraftClient.java:1836)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1314)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:936)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000023601019800.invokeStaticInit(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000023601002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at app//net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at app//net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000023601002400.invokeStatic(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000023601002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.devlaunchinjector.Main.main(Main.java:86)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Client shutdown watchdog
Stacktrace:
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:164)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1375)
	at knot//net.minecraft.client.MinecraftClient.disconnect(MinecraftClient.java:2196)
	at knot//net.minecraft.client.network.ClientCommonNetworkHandler.onDisconnected(ClientCommonNetworkHandler.java:314)
	at knot//net.minecraft.network.ClientConnection.handleDisconnection(ClientConnection.java:668)
	at knot//net.minecraft.client.network.ClientPlayerInteractionManager.tick(ClientPlayerInteractionManager.java:295)
	at knot//net.minecraft.client.MinecraftClient.tick(MinecraftClient.java:1836)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1314)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:936)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000023601019800.invokeStaticInit(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000023601002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at app//net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at app//net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)

-- Thread Dump --
Details:
	Threads: "Render thread" prio=10 Id=1 RUNNABLE
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:164)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1375)
	at knot//net.minecraft.client.MinecraftClient.disconnect(MinecraftClient.java:2196)
	at knot//net.minecraft.client.network.ClientCommonNetworkHandler.onDisconnected(ClientCommonNetworkHandler.java:314)
	at knot//net.minecraft.network.ClientConnection.handleDisconnection(ClientConnection.java:668)
	at knot//net.minecraft.client.network.ClientPlayerInteractionManager.tick(ClientPlayerInteractionManager.java:295)
	at knot//net.minecraft.client.MinecraftClient.tick(MinecraftClient.java:1836)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1314)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:936)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000023601019800.invokeStaticInit(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000023601002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at app//net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at app//net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000023601002400.invokeStatic(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000023601002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.devlaunchinjector.Main.main(Main.java:86)


"Reference Handler" daemon prio=10 Id=9 RUNNABLE
	at java.base@23.0.2/java.lang.ref.Reference.waitForReferencePendingList(Native Method)
	at java.base@23.0.2/java.lang.ref.Reference.processPendingReferences(Reference.java:246)
	at java.base@23.0.2/java.lang.ref.Reference$ReferenceHandler.run(Reference.java:208)


"Finalizer" daemon prio=8 Id=10 WAITING on java.lang.ref.NativeReferenceQueue$Lock@588ef128
	at java.base@23.0.2/java.lang.Object.wait0(Native Method)
	-  waiting on java.lang.ref.NativeReferenceQueue$Lock@588ef128
	at java.base@23.0.2/java.lang.Object.wait(Object.java:378)
	at java.base@23.0.2/java.lang.Object.wait(Object.java:352)
	at java.base@23.0.2/java.lang.ref.NativeReferenceQueue.await(NativeReferenceQueue.java:48)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:166)
	at java.base@23.0.2/java.lang.ref.NativeReferenceQueue.remove(NativeReferenceQueue.java:89)
	at java.base@23.0.2/java.lang.ref.Finalizer$FinalizerThread.run(Finalizer.java:173)


"Signal Dispatcher" daemon prio=9 Id=11 RUNNABLE


"Attach Listener" daemon prio=5 Id=12 RUNNABLE


"Notification Thread" daemon prio=9 Id=19 RUNNABLE


"Common-Cleaner" daemon prio=8 Id=20 TIMED_WAITING on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@346f5883
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@346f5883
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:79)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:151)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:229)
	at java.base@23.0.2/jdk.internal.ref.CleanerImpl.run(CleanerImpl.java:140)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)
	at java.base@23.0.2/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)


"JNA Cleaner" daemon prio=5 Id=44 TIMED_WAITING on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@35ac31f0
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@35ac31f0
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:79)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:151)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:229)
	at knot//com.sun.jna.internal.Cleaner$CleanerThread.run(Cleaner.java:154)


"Timer hack thread" daemon prio=5 Id=45 TIMED_WAITING
	at java.base@23.0.2/java.lang.Thread.sleepNanos0(Native Method)
	at java.base@23.0.2/java.lang.Thread.sleepNanos(Thread.java:496)
	at java.base@23.0.2/java.lang.Thread.sleep(Thread.java:527)
	at knot//net.minecraft.util.Util$9.run(Util.java)


"Yggdrasil Key Fetcher" daemon prio=5 Id=47 TIMED_WAITING on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@9c14321
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@9c14321
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
	at java.base@23.0.2/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
	at java.base@23.0.2/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Download-1" daemon prio=5 Id=48 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@26e9f480
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@26e9f480
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Worker-Main-1" daemon prio=5 Id=54 WAITING on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Worker-Main-2" daemon prio=5 Id=56 WAITING on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Worker-Main-3" daemon prio=5 Id=57 WAITING on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Worker-Main-4" daemon prio=5 Id=58 WAITING on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Worker-Main-5" daemon prio=5 Id=59 WAITING on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Worker-Main-6" daemon prio=5 Id=60 WAITING on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@680c1b9e
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Worker-Main-7" daemon prio=5 Id=61 WAITING on net.minecraft.client.main.Main$2@c60c151
	at java.base@23.0.2/java.lang.Object.wait0(Native Method)
	-  waiting on net.minecraft.client.main.Main$2@c60c151
	at java.base@23.0.2/java.lang.Object.wait(Object.java:378)
	at java.base@23.0.2/java.lang.Thread.join(Thread.java:2017)
	at java.base@23.0.2/java.lang.Thread.join(Thread.java:2093)
	at java.base@23.0.2/java.lang.ApplicationShutdownHooks.runHooks(ApplicationShutdownHooks.java:114)
	at java.base@23.0.2/java.lang.ApplicationShutdownHooks$1.run(ApplicationShutdownHooks.java:47)
	at java.base@23.0.2/java.lang.Shutdown.runHooks(Shutdown.java:130)
	at java.base@23.0.2/java.lang.Shutdown.exit(Shutdown.java:167)
	-  locked java.lang.Class@3cfb84de
	at java.base@23.0.2/java.lang.Runtime.exit(Runtime.java:188)
	at java.base@23.0.2/java.lang.System.exit(System.java:1923)
	at knot//net.minecraft.util.Util.uncaughtExceptionHandler(Util.java:320)
	at knot//net.minecraft.util.Util$$Lambda/0x000002360136a930.uncaughtException(Unknown Source)
	at java.base@23.0.2/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.onAuxExceptionSet(ForkJoinTask.java:1733)
	at java.base@23.0.2/java.util.concurrent.ForkJoinTask.trySetException(ForkJoinTask.java:380)
	at java.base@23.0.2/java.util.concurrent.ForkJoinTask$InterruptibleTask.exec(ForkJoinTask.java:1643)
	at java.base@23.0.2/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:507)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1460)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2036)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"IO-Worker-1" prio=10 Id=62 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"ForkJoinPool.commonPool-worker-1" daemon prio=10 Id=63 TIMED_WAITING on java.util.concurrent.ForkJoinPool@5edc8f0e
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@5edc8f0e
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Server thread" prio=8 Id=65 TIMED_WAITING on java.lang.String@3e613d7f
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.lang.String@3e613d7f
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at knot//net.minecraft.server.MinecraftServer.waitForTasks(MinecraftServer.java:829)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:140)
	at knot//net.minecraft.server.MinecraftServer.runTasks(MinecraftServer.java:811)
	at knot//net.minecraft.server.MinecraftServer.runTasksTillTickEnd(MinecraftServer.java:818)
	at knot//net.minecraft.server.MinecraftServer.shutdown(MinecraftServer.java:614)
	at knot//net.minecraft.server.integrated.IntegratedServer.shutdown(IntegratedServer.java:223)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:739)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java)
	at knot//net.minecraft.server.MinecraftServer$$Lambda/0x00000236021d30d8.run(Unknown Source)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-2" prio=5 Id=66 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-4" prio=5 Id=68 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-3" prio=10 Id=67 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-5" prio=5 Id=69 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-6" prio=10 Id=70 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-7" prio=10 Id=71 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-8" prio=10 Id=72 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@5604aebb
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Netty Server IO #0" daemon prio=5 Id=73 RUNNABLE (in native)
	at java.base@23.0.2/sun.nio.ch.WEPoll.wait(Native Method)
	at java.base@23.0.2/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
	at java.base@23.0.2/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
	-  locked io.netty.channel.nio.SelectedSelectionKeySet@1ff53c0
	-  locked sun.nio.ch.WEPollSelectorImpl@35f11b82
	at java.base@23.0.2/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:147)
	at knot//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at knot//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:887)
	at knot//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at knot//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at knot//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Netty Local Client IO #0" daemon prio=5 Id=74 WAITING on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@727d5432
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@727d5432
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4023)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3969)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
	at java.base@23.0.2/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
	at knot//io.netty.util.concurrent.SingleThreadEventExecutor.takeTask(SingleThreadEventExecutor.java:243)
	at knot//io.netty.channel.DefaultEventLoop.run(DefaultEventLoop.java:52)
	at knot//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at knot//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Netty Server IO #1" daemon prio=5 Id=75 RUNNABLE (in native)
	at java.base@23.0.2/sun.nio.ch.WEPoll.wait(Native Method)
	at java.base@23.0.2/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
	at java.base@23.0.2/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
	-  locked io.netty.channel.nio.SelectedSelectionKeySet@48e3118c
	-  locked sun.nio.ch.WEPollSelectorImpl@77e97138
	at java.base@23.0.2/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:147)
	at knot//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at knot//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:887)
	at knot//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at knot//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at knot//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Client Shutdown Thread" prio=5 Id=46 WAITING on java.lang.Thread@7fde472a
	at java.base@23.0.2/java.lang.Object.wait0(Native Method)
	-  waiting on java.lang.Thread@7fde472a
	at java.base@23.0.2/java.lang.Object.wait(Object.java:378)
	at java.base@23.0.2/java.lang.Thread.join(Thread.java:2017)
	at java.base@23.0.2/java.lang.Thread.join(Thread.java:2093)
	at knot//net.minecraft.server.MinecraftServer.stop(MinecraftServer.java:659)
	at knot//net.minecraft.server.integrated.IntegratedServer.stop(IntegratedServer.java:243)
	at knot//net.minecraft.client.main.Main$2.run(Main.java:237)


"Sound engine" daemon prio=10 Id=77 WAITING on java.lang.String@3e613d7f
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.lang.String@3e613d7f
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221)
	at knot//net.minecraft.client.sound.SoundExecutor.waitForTasks(SoundExecutor.java:49)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:140)
	at knot//net.minecraft.client.sound.SoundExecutor.waitForStop(SoundExecutor.java:42)
	at knot//net.minecraft.client.sound.SoundExecutor$$Lambda/0x0000023601e18558.run(Unknown Source)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"LanServerDetector #1" daemon prio=10 Id=78 RUNNABLE (in native)
	at java.base@23.0.2/sun.nio.ch.Net.poll(Native Method)
	at java.base@23.0.2/sun.nio.ch.DatagramChannelImpl.park(DatagramChannelImpl.java:511)
	at java.base@23.0.2/sun.nio.ch.DatagramChannelImpl.tryBlockingReceive(DatagramChannelImpl.java:763)
	at java.base@23.0.2/sun.nio.ch.DatagramChannelImpl.blockingReceive(DatagramChannelImpl.java:693)
	at java.base@23.0.2/sun.nio.ch.DatagramSocketAdaptor.receive(DatagramSocketAdaptor.java:204)
	at java.base@23.0.2/java.net.DatagramSocket.receive(DatagramSocket.java:718)
	at knot//net.minecraft.client.network.LanServerQueryManager$LanServerDetector.run(LanServerQueryManager.java:87)

	Number of locked synchronizers = 1
	- java.util.concurrent.locks.ReentrantLock$NonfairSync@4447a42


"Client shutdown watchdog" daemon prio=10 Id=79 RUNNABLE
	at java.management@23.0.2/sun.management.ThreadImpl.dumpThreads0(Native Method)
	at java.management@23.0.2/sun.management.ThreadImpl.dumpAllThreads(ThreadImpl.java:518)
	at java.management@23.0.2/sun.management.ThreadImpl.dumpAllThreads(ThreadImpl.java:506)
	at knot//net.minecraft.server.dedicated.DedicatedServerWatchdog.createCrashReport(DedicatedServerWatchdog.java:70)
	at knot//net.minecraft.client.ClientWatchdog.method_61935(ClientWatchdog.java:26)
	at knot//net.minecraft.client.ClientWatchdog$$Lambda/0x0000023602532518.run(Unknown Source)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)



Stacktrace:
	at knot//net.minecraft.server.dedicated.DedicatedServerWatchdog.createCrashReport(DedicatedServerWatchdog.java:81)
	at knot//net.minecraft.client.ClientWatchdog.method_61935(ClientWatchdog.java:26)
	at java.base/java.lang.Thread.run(Thread.java:1575)

-- System Details --
Details:
	Minecraft Version: 1.21.5
	Minecraft Version ID: 1.21.5
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 120267456 bytes (114 MiB) / 849346560 bytes (810 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6874
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 10661.61
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 2473.50
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 178477.53, total: 389296.00
	Space in storage for workdir (MiB): available: 178477.53, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: AiVillagersFabric 1.0.0
		fabric-api: Fabric API 0.126.0+1.21.5
		fabric-api-base: Fabric API Base 0.4.62+73a52b4b49
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.96+86c3a9f149
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.7+2dd063df49
		fabric-block-api-v1: Fabric Block API (v1) 1.1.0+ed91556f49
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.26+aa6d566c49
		fabric-blockrenderlayer-v1: Fabric BlockRenderLayer Registration (v1) 2.0.16+86c3a9f149
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.0+dac2d6e349
		fabric-client-tags-api-v1: Fabric Client Tags 1.1.37+86c3a9f149
		fabric-command-api-v1: Fabric Command API (v1) 1.2.70+f71b366f49
		fabric-command-api-v2: Fabric Command API (v2) 2.2.49+73a52b4b49
		fabric-commands-v0: Fabric Commands (v0) 0.2.87+df3654b349
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.14+3e6c1f7d49
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.32+7f945d5b49
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.1+0570995b49
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.12+86c3a9f149
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.3+bc514c4549
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 22.4.2+ea72995749
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.17+3e6c1f7d49
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.0+3ce7866349
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.15+64e3057949
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.70+c327076a49
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.3+2a6ec84b49
		fabric-item-api-v1: Fabric Item API (v1) 11.4.1+e46fd76a49
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.9+3459fc6149
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.63+ecf51cdc49
		fabric-keybindings-v0: Fabric Key Bindings (v0) 0.2.61+df3654b349
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.0+230071a049
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.48+3f89f5a549
		fabric-loot-api-v3: Fabric Loot API (v3) 1.0.36+86c3a9f149
		fabric-message-api-v1: Fabric Message API (v1) 6.1.0+fe971bba49
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.1+5281b90749
		fabric-networking-api-v1: Fabric Networking API (v1) 4.5.0+775be32c49
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.1+b8d6ba7049
		fabric-particles-v1: Fabric Particles (v1) 4.1.2+112e550e49
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.8+3235ab3249
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.21+b556383249
		fabric-renderer-api-v1: Fabric Renderer API (v1) 6.1.0+5281b90749
		fabric-renderer-indigo: Fabric Renderer - Indigo 3.1.0+5281b90749
		fabric-rendering-data-attachment-v1: Fabric Rendering Data Attachment (v1) 0.3.64+73761d2e49
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.27+86c3a9f149
		fabric-rendering-v1: Fabric Rendering (v1) 11.1.11+5490746649
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.21+73a52b4b49
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.7+847e5f5c49
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.46+86c3a9f149
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.128+c327076a49
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.38+86c3a9f149
		fabric-tag-api-v1: Fabric Tag API (v1) 1.0.17+ecf51cdc49
		fabric-transfer-api-v1: Fabric Transfer API (v1) 5.4.24+7b20cbb049
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.3.17+f17a180c49
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.5
		mixinextras: MixinExtras 0.4.1