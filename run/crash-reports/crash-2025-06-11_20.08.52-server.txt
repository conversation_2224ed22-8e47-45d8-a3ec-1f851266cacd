---- Minecraft Crash Report ----
// There are four lights!

Time: 2025-06-11 20:08:52
Description: Saving entity NBT

java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because "this.feedingPair" is null
	at knot//net.minecraft.entity.passive.VillagerEntity.handler$zza000$ai_villagers$onWriteCustomDataToNbt(VillagerEntity.java:2379)
	at knot//net.minecraft.entity.passive.VillagerEntity.writeCustomDataToNbt(VillagerEntity.java:536)
	at knot//net.minecraft.entity.Entity.writeNbt(Entity.java:2091)
	at knot//net.minecraft.entity.Entity.saveSelfNbt(Entity.java:2027)
	at knot//net.minecraft.entity.Entity.saveNbt(Entity.java:2035)
	at knot//net.minecraft.world.storage.EntityChunkDataAccess.method_31734(EntityChunkDataAccess.java:99)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:807)
	at knot//net.minecraft.world.storage.EntityChunkDataAccess.writeChunkData(EntityChunkDataAccess.java:97)
	at knot//net.minecraft.server.world.ServerEntityManager.trySave(ServerEntityManager.java:303)
	at knot//net.minecraft.server.world.ServerEntityManager.unload(ServerEntityManager.java:320)
	at knot//net.minecraft.server.world.ServerEntityManager.method_31849(ServerEntityManager.java:347)
	at knot//it.unimi.dsi.fastutil.longs.LongCollection.removeIf(LongCollection.java:274)
	at knot//net.minecraft.server.world.ServerEntityManager.unloadChunks(ServerEntityManager.java:342)
	at knot//net.minecraft.server.world.ServerEntityManager.tick(ServerEntityManager.java:362)
	at knot//net.minecraft.server.world.ServerWorld.tick(ServerWorld.java:412)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1062)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:946)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:706)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:290)
	at java.base/java.lang.Thread.run(Thread.java:1575)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Server thread
Stacktrace:
	at knot//net.minecraft.entity.passive.VillagerEntity.handler$zza000$ai_villagers$onWriteCustomDataToNbt(VillagerEntity.java:2379)
	at knot//net.minecraft.entity.passive.VillagerEntity.writeCustomDataToNbt(VillagerEntity.java:536)
	at knot//net.minecraft.entity.Entity.writeNbt(Entity.java:2091)
	at knot//net.minecraft.entity.Entity.saveSelfNbt(Entity.java:2027)
	at knot//net.minecraft.entity.Entity.saveNbt(Entity.java:2035)
	at knot//net.minecraft.world.storage.EntityChunkDataAccess.method_31734(EntityChunkDataAccess.java:99)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:807)
	at knot//net.minecraft.world.storage.EntityChunkDataAccess.writeChunkData(EntityChunkDataAccess.java:97)
	at knot//net.minecraft.server.world.ServerEntityManager.trySave(ServerEntityManager.java:303)
	at knot//net.minecraft.server.world.ServerEntityManager.unload(ServerEntityManager.java:320)
	at knot//net.minecraft.server.world.ServerEntityManager.method_31849(ServerEntityManager.java:347)
	at knot//it.unimi.dsi.fastutil.longs.LongCollection.removeIf(LongCollection.java:274)
	at knot//net.minecraft.server.world.ServerEntityManager.unloadChunks(ServerEntityManager.java:342)
	at knot//net.minecraft.server.world.ServerEntityManager.tick(ServerEntityManager.java:362)

-- Entity being saved --
Details:
	Entity Type: minecraft:villager (net.minecraft.entity.passive.VillagerEntity)
	Entity ID: 123
	Entity Name: Pastor
	Entity's Exact location: -233.42, -60.00, 400.49
	Entity's Block location: World: (-234,-60,400), Section: (at 6,4,0 in -15,-4,25; chunk contains blocks -240,-64,400 to -225,319,415), Region: (-1,0; contains chunks -32,0 to -1,31, blocks -512,-64,0 to -1,319,511)
	Entity's Momentum: -0.07, -0.08, 0.00
	Entity's Passengers: []
	Entity's Vehicle: null
Stacktrace:
	at knot//net.minecraft.entity.Entity.writeNbt(Entity.java:2091)
	at knot//net.minecraft.entity.Entity.saveSelfNbt(Entity.java:2027)
	at knot//net.minecraft.entity.Entity.saveNbt(Entity.java:2035)
	at knot//net.minecraft.world.storage.EntityChunkDataAccess.method_31734(EntityChunkDataAccess.java:99)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:807)
	at knot//net.minecraft.world.storage.EntityChunkDataAccess.writeChunkData(EntityChunkDataAccess.java:97)
	at knot//net.minecraft.server.world.ServerEntityManager.trySave(ServerEntityManager.java:303)
	at knot//net.minecraft.server.world.ServerEntityManager.unload(ServerEntityManager.java:320)
	at knot//net.minecraft.server.world.ServerEntityManager.method_31849(ServerEntityManager.java:347)
	at knot//it.unimi.dsi.fastutil.longs.LongCollection.removeIf(LongCollection.java:274)
	at knot//net.minecraft.server.world.ServerEntityManager.unloadChunks(ServerEntityManager.java:342)
	at knot//net.minecraft.server.world.ServerEntityManager.tick(ServerEntityManager.java:362)
	at knot//net.minecraft.server.world.ServerWorld.tick(ServerWorld.java:412)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1062)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:946)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:706)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:290)
	at java.base/java.lang.Thread.run(Thread.java:1575)

-- Affected level --
Details:
	All players: 1 total; ServerPlayerEntity{name=Player385, id=1, pos=(-336.23820656074395, -54.0, 387.7981635958853), mode=CREATIVE, permission=4}
	Chunk stats: 2013
	Level dimension: minecraft:overworld
	Level spawn location: World: (0,-60,0), Section: (at 0,4,0 in 0,-4,0; chunk contains blocks 0,-64,0 to 15,319,15), Region: (0,0; contains chunks 0,0 to 31,31, blocks 0,-64,0 to 511,319,511)
	Level time: 241169 game time, 9155 day time
	Level name: Mundo nuevo
	Level game mode: Game mode: creative (ID 1). Hardcore: false. Commands: true
	Level weather: Rain time: 43740 (now: false), thunder time: 24534 (now: false)
	Known server brands: fabric
	Removed feature flags: 
	Level was modded: true
	Level storage version: 0x04ABD - Anvil
	Loaded entity count: 124
Stacktrace:
	at knot//net.minecraft.server.world.ServerWorld.addDetailsToCrashReport(ServerWorld.java:1734)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1065)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:946)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:706)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:290)
	at java.base/java.lang.Thread.run(Thread.java:1575)

-- System Details --
Details:
	Minecraft Version: 1.21.5
	Minecraft Version ID: 1.21.5
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 214108416 bytes (204 MiB) / 989855744 bytes (944 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6874
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 8957.57
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 1603.00
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 184014.47, total: 389296.00
	Space in storage for workdir (MiB): available: 184014.47, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: AiVillagersFabric 1.0.0
		fabric-api: Fabric API 0.125.3+1.21.5
		fabric-api-base: Fabric API Base 0.4.62+73a52b4b49
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.96+86c3a9f149
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.7+2dd063df49
		fabric-block-api-v1: Fabric Block API (v1) 1.1.0+ed91556f49
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.26+aa6d566c49
		fabric-blockrenderlayer-v1: Fabric BlockRenderLayer Registration (v1) 2.0.16+86c3a9f149
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.0+dac2d6e349
		fabric-client-tags-api-v1: Fabric Client Tags 1.1.37+86c3a9f149
		fabric-command-api-v1: Fabric Command API (v1) 1.2.70+f71b366f49
		fabric-command-api-v2: Fabric Command API (v2) 2.2.49+73a52b4b49
		fabric-commands-v0: Fabric Commands (v0) 0.2.87+df3654b349
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.14+3e6c1f7d49
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.32+7f945d5b49
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.1+0570995b49
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.12+86c3a9f149
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.3+bc514c4549
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 22.4.2+ea72995749
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.17+3e6c1f7d49
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.0+3ce7866349
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.15+64e3057949
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.70+c327076a49
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.3+2a6ec84b49
		fabric-item-api-v1: Fabric Item API (v1) 11.4.1+e46fd76a49
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.9+3459fc6149
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.63+ecf51cdc49
		fabric-keybindings-v0: Fabric Key Bindings (v0) 0.2.61+df3654b349
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.0+230071a049
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.48+3f89f5a549
		fabric-loot-api-v3: Fabric Loot API (v3) 1.0.36+86c3a9f149
		fabric-message-api-v1: Fabric Message API (v1) 6.1.0+fe971bba49
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.0+c982b95149
		fabric-networking-api-v1: Fabric Networking API (v1) 4.5.0+775be32c49
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.1+b8d6ba7049
		fabric-particles-v1: Fabric Particles (v1) 4.1.2+112e550e49
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.8+3235ab3249
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.21+b556383249
		fabric-renderer-api-v1: Fabric Renderer API (v1) 6.0.2+c982b95149
		fabric-renderer-indigo: Fabric Renderer - Indigo 3.0.3+3e6c1f7d49
		fabric-rendering-data-attachment-v1: Fabric Rendering Data Attachment (v1) 0.3.64+73761d2e49
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.27+86c3a9f149
		fabric-rendering-v1: Fabric Rendering (v1) 11.1.11+5490746649
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.21+73a52b4b49
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.7+847e5f5c49
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.46+86c3a9f149
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.128+c327076a49
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.38+86c3a9f149
		fabric-tag-api-v1: Fabric Tag API (v1) 1.0.17+ecf51cdc49
		fabric-transfer-api-v1: Fabric Transfer API (v1) 5.4.24+7b20cbb049
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.3.17+f17a180c49
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.5
		mixinextras: MixinExtras 0.4.1
	Server Running: true
	Player Count: 1 / 8; [ServerPlayerEntity['Player385'/1, l='ServerLevel[Mundo nuevo]', x=-336.24, y=-54.00, z=387.80]]
	Active Data Packs: vanilla, fabric, ai_villagers, fabric-convention-tags-v2, fabric-gametest-api-v1
	Available Data Packs: ai_villagers, fabric, fabric-convention-tags-v2, fabric-gametest-api-v1, minecart_improvements, redstone_experiments, trade_rebalance, vanilla
	Enabled Feature Flags: minecraft:vanilla
	World Generation: Stable
	World Seed: 4436523174824612725
	Suppressed Exceptions: ~~NONE~~
	Type: Integrated Server (map_client.txt)
	Is Modded: Definitely; Client brand changed to 'fabric'; Server brand changed to 'fabric'
	Launched Version: Fabric