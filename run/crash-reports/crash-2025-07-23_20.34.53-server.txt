---- Minecraft Crash Report ----
// Don't be sad, have a hug! <3

Time: 2025-07-23 20:34:53
Description: Ticking entity

java.lang.UnsupportedOperationException
	at java.base/java.util.AbstractMap$SimpleImmutableEntry.setValue(AbstractMap.java:796)
	at knot//AiVillagers.util.BedCooldownManager.lambda$updateCooldowns$0(BedCooldownManager.java:32)
	at java.base/java.util.concurrent.ConcurrentHashMap.removeEntryIf(ConcurrentHashMap.java:1645)
	at java.base/java.util.concurrent.ConcurrentHashMap$EntrySetView.removeIf(ConcurrentHashMap.java:4844)
	at knot//AiVillagers.util.BedCooldownManager.updateCooldowns(BedCooldownManager.java:26)
	at knot//net.minecraft.entity.passive.VillagerEntity.handler$zpd000$ai_villagers$checkBedOccupancy(VillagerEntity.java:9805)
	at knot//net.minecraft.entity.passive.VillagerEntity.tick(VillagerEntity.java)
	at knot//net.minecraft.server.world.ServerWorld.tickEntity(ServerWorld.java:777)
	at knot//net.minecraft.world.World.tickEntity(World.java:510)
	at knot//net.minecraft.server.world.ServerWorld.method_31420(ServerWorld.java:408)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:54)
	at knot//net.minecraft.server.world.ServerWorld.tick(ServerWorld.java:378)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1063)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:947)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:707)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:291)
	at java.base/java.lang.Thread.run(Thread.java:1575)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Server thread
Stacktrace:
	at java.base/java.util.AbstractMap$SimpleImmutableEntry.setValue(AbstractMap.java:796)
	at knot//AiVillagers.util.BedCooldownManager.lambda$updateCooldowns$0(BedCooldownManager.java:32)
	at java.base/java.util.concurrent.ConcurrentHashMap.removeEntryIf(ConcurrentHashMap.java:1645)
	at java.base/java.util.concurrent.ConcurrentHashMap$EntrySetView.removeIf(ConcurrentHashMap.java:4844)
	at knot//AiVillagers.util.BedCooldownManager.updateCooldowns(BedCooldownManager.java:26)
	at knot//net.minecraft.entity.passive.VillagerEntity.handler$zpd000$ai_villagers$checkBedOccupancy(VillagerEntity.java:9805)
	at knot//net.minecraft.entity.passive.VillagerEntity.tick(VillagerEntity.java)
	at knot//net.minecraft.server.world.ServerWorld.tickEntity(ServerWorld.java:777)
	at knot//net.minecraft.world.World.tickEntity(World.java:510)
	at knot//net.minecraft.server.world.ServerWorld.method_31420(ServerWorld.java:408)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:54)

-- Entity being ticked --
Details:
	Entity Type: minecraft:villager (net.minecraft.entity.passive.VillagerEntity)
	Entity ID: 8
	Entity Name: Peletero
	Entity's Exact location: -320.50, -58.44, -291.50
	Entity's Block location: World: (-321,-59,-292), Section: (at 15,5,12 in -21,-4,-19; chunk contains blocks -336,-64,-304 to -321,319,-289), Region: (-1,-1; contains chunks -32,-32 to -1,-1, blocks -512,-64,-512 to -1,319,-1)
	Entity's Momentum: 0.00, -0.05, 0.00
	Entity's Passengers: []
	Entity's Vehicle: null
Stacktrace:
	at knot//net.minecraft.world.World.tickEntity(World.java:510)
	at knot//net.minecraft.server.world.ServerWorld.method_31420(ServerWorld.java:408)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:54)
	at knot//net.minecraft.server.world.ServerWorld.tick(ServerWorld.java:378)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1063)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:947)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:707)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:291)
	at java.base/java.lang.Thread.run(Thread.java:1575)

-- Affected level --
Details:
	All players: 1 total; ServerPlayerEntity{name=Player840, id=1, pos=(-368.5, -55.0, -269.5), mode=CREATIVE, permission=4}
	Chunk stats: 2175
	Level dimension: minecraft:overworld
	Level spawn location: World: (0,-60,0), Section: (at 0,4,0 in 0,-4,0; chunk contains blocks 0,-64,0 to 15,319,15), Region: (0,0; contains chunks 0,0 to 31,31, blocks 0,-64,0 to 511,319,511)
	Level time: 40966 game time, 14819 day time
	Level name: Mundo nuevo
	Level game mode: Game mode: creative (ID 1). Hardcore: false. Commands: true
	Level weather: Rain time: 92191 (now: false), thunder time: 71780 (now: false)
	Known server brands: fabric
	Removed feature flags: 
	Level was modded: true
	Level storage version: 0x04ABD - Anvil
	Loaded entity count: 32
Stacktrace:
	at knot//net.minecraft.server.world.ServerWorld.addDetailsToCrashReport(ServerWorld.java:1788)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1066)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:947)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:707)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:291)
	at java.base/java.lang.Thread.run(Thread.java:1575)

-- System Details --
Details:
	Minecraft Version: 1.21.8
	Minecraft Version ID: 1.21.8
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 124762432 bytes (118 MiB) / 580911104 bytes (554 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6913
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 19152.23
	Virtual memory used (MiB): 17171.85
	Swap memory total (MiB): 11286.74
	Swap memory used (MiB): 8479.64
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 171604.50, total: 389296.00
	Space in storage for workdir (MiB): available: 171604.50, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: ai_villagers 1.0.0
		fabric-api: Fabric API 0.129.0+1.21.8
		fabric-api-base: Fabric API Base 0.4.64+9ec45cd8f3
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.100+946bf4c3f3
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.11+946bf4c3f3
		fabric-block-api-v1: Fabric Block API (v1) 1.1.3+946bf4c3f3
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.31+946bf4c3f3
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.5+8a98c3fcf3
		fabric-command-api-v2: Fabric Command API (v2) 2.2.53+946bf4c3f3
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.18+946bf4c3f3
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.40+7f945d5bf3
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.5+eb5df52ff3
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.15+946bf4c3f3
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.10+946bf4c3f3
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 23.2.4+55e55d29f3
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.19+946bf4c3f3
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.1+c9e47273f3
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.23+946bf4c3f3
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.73+c64c9c5bf3
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.9+39ce47f5f3
		fabric-item-api-v1: Fabric Item API (v1) 11.4.3+946bf4c3f3
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.13+946bf4c3f3
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.65+946bf4c3f3
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.3+db4dfd85f3
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.55+3f89f5a5f3
		fabric-loot-api-v3: Fabric Loot API (v3) 2.0.2+946bf4c3f3
		fabric-message-api-v1: Fabric Message API (v1) 6.1.1+946bf4c3f3
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.5+946bf4c3f3
		fabric-networking-api-v1: Fabric Networking API (v1) 5.0.1+946bf4c3f3
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.7+946bf4c3f3
		fabric-particles-v1: Fabric Particles (v1) 4.1.7+946bf4c3f3
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.14+946bf4c3f3
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.27+946bf4c3f3
		fabric-renderer-api-v1: Fabric Renderer API (v1) 7.0.2+946bf4c3f3
		fabric-renderer-indigo: Fabric Renderer - Indigo 4.0.2+946bf4c3f3
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.30+fa6cb72bf3
		fabric-rendering-v1: Fabric Rendering (v1) 12.4.0+e8d43c76f3
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.24+946bf4c3f3
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.11+946bf4c3f3
		fabric-screen-api-v1: Fabric Screen API (v1) 2.1.0+277ecf7df3
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.136+946bf4c3f3
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.42+946bf4c3f3
		fabric-tag-api-v1: Fabric Tag API (v1) 1.2.1+946bf4c3f3
		fabric-transfer-api-v1: Fabric Transfer API (v1) 6.0.5+946bf4c3f3
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.4.1+ac3e15d1f3
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.8
		mixinextras: MixinExtras 0.4.1
	Server Running: true
	Player Count: 1 / 8; [ServerPlayerEntity['Player840'/1, l='ServerLevel[Mundo nuevo]', x=-368.50, y=-55.00, z=-269.50]]
	Active Data Packs: vanilla, fabric, ai_villagers, fabric-convention-tags-v2, fabric-gametest-api-v1
	Available Data Packs: ai_villagers, fabric, fabric-convention-tags-v2, fabric-gametest-api-v1, minecart_improvements, redstone_experiments, trade_rebalance, vanilla
	Enabled Feature Flags: minecraft:vanilla
	World Generation: Stable
	World Seed: 5438051965867635904
	Suppressed Exceptions: ~~NONE~~
	Type: Integrated Server (map_client.txt)
	Is Modded: Definitely; Client brand changed to 'fabric'; Server brand changed to 'fabric'
	Launched Version: Fabric