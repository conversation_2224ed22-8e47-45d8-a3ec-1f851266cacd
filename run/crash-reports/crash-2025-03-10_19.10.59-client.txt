---- Minecraft Crash Report ----
// There are four lights!

Time: 2025-03-10 19:10:59
Description: Ticking entity

java.lang.NullPointerException: Cannot invoke "java.util.function.Predicate.test(Object)" because "predicate" is null
	at knot//net.minecraft.world.World.method_47576(World.java:711)
	at knot//net.minecraft.world.entity.EntityTrackingSection.forEach(EntityTrackingSection.java:51)
	at knot//net.minecraft.world.entity.SectionedEntityCache.method_31778(SectionedEntityCache.java:127)
	at knot//net.minecraft.world.entity.SectionedEntityCache.forEachInBox(SectionedEntityCache.java:66)
	at knot//net.minecraft.world.entity.SectionedEntityCache.forEachIntersects(SectionedEntityCache.java:127)
	at knot//net.minecraft.world.entity.SimpleEntityLookup.forEachIntersects(SimpleEntityLookup.java:48)
	at knot//net.minecraft.world.World.collectEntitiesByType(World.java:710)
	at knot//net.minecraft.world.World.collectEntitiesByType(World.java:704)
	at knot//net.minecraft.world.World.getEntitiesByType(World.java:699)
	at knot//net.minecraft.world.EntityView.getEntitiesByClass(EntityView.java:24)
	at knot//net.minecraft.entity.passive.VillagerEntity.stopAllChasing(VillagerEntity.java:4374)
	at knot//net.minecraft.entity.passive.VillagerEntity.handleAnimalChasing(VillagerEntity.java:4325)
	at knot//net.minecraft.entity.passive.VillagerEntity.handler$znm000$modid$handleAnimalChasingInjection(VillagerEntity.java:4317)
	at knot//net.minecraft.entity.passive.VillagerEntity.tick(VillagerEntity.java:326)
	at knot//net.minecraft.client.world.ClientWorld.tickEntity(ClientWorld.java:284)
	at knot//net.minecraft.world.World.tickEntity(World.java:502)
	at knot//net.minecraft.client.world.ClientWorld.method_32124(ClientWorld.java:263)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:54)
	at knot//net.minecraft.client.world.ClientWorld.tickEntities(ClientWorld.java:259)
	at knot//net.minecraft.client.MinecraftClient.tick(MinecraftClient.java:1879)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1302)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:922)
	at knot//net.minecraft.client.main.Main.main(Main.java:267)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Render thread
Stacktrace:
	at knot//net.minecraft.world.World.method_47576(World.java:711)
	at knot//net.minecraft.world.entity.EntityTrackingSection.forEach(EntityTrackingSection.java:51)
	at knot//net.minecraft.world.entity.SectionedEntityCache.method_31778(SectionedEntityCache.java:127)
	at knot//net.minecraft.world.entity.SectionedEntityCache.forEachInBox(SectionedEntityCache.java:66)
	at knot//net.minecraft.world.entity.SectionedEntityCache.forEachIntersects(SectionedEntityCache.java:127)
	at knot//net.minecraft.world.entity.SimpleEntityLookup.forEachIntersects(SimpleEntityLookup.java:48)
	at knot//net.minecraft.world.World.collectEntitiesByType(World.java:710)
	at knot//net.minecraft.world.World.collectEntitiesByType(World.java:704)
	at knot//net.minecraft.world.World.getEntitiesByType(World.java:699)
	at knot//net.minecraft.world.EntityView.getEntitiesByClass(EntityView.java:24)
	at knot//net.minecraft.entity.passive.VillagerEntity.stopAllChasing(VillagerEntity.java:4374)
	at knot//net.minecraft.entity.passive.VillagerEntity.handleAnimalChasing(VillagerEntity.java:4325)
	at knot//net.minecraft.entity.passive.VillagerEntity.handler$znm000$modid$handleAnimalChasingInjection(VillagerEntity.java:4317)
	at knot//net.minecraft.entity.passive.VillagerEntity.tick(VillagerEntity.java:326)
	at knot//net.minecraft.client.world.ClientWorld.tickEntity(ClientWorld.java:284)
	at knot//net.minecraft.world.World.tickEntity(World.java:502)
	at knot//net.minecraft.client.world.ClientWorld.method_32124(ClientWorld.java:263)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:54)
	at knot//net.minecraft.client.world.ClientWorld.tickEntities(ClientWorld.java:259)

-- Entity being ticked --
Details:
	Entity Type: minecraft:villager (net.minecraft.entity.passive.VillagerEntity)
	Entity ID: 12
	Entity Name: Aldeano
	Entity's Exact location: 1083.46, 74.00, 484.09
	Entity's Block location: World: (1083,74,484), Section: (at 11,10,4 in 67,4,30; chunk contains blocks 1072,-64,480 to 1087,319,495), Region: (2,0; contains chunks 64,0 to 95,31, blocks 1024,-64,0 to 1535,319,511)
	Entity's Momentum: 0.00, -0.08, 0.00
	Entity's Passengers: []
	Entity's Vehicle: null
Stacktrace:
	at knot//net.minecraft.world.World.tickEntity(World.java:502)
	at knot//net.minecraft.client.world.ClientWorld.method_32124(ClientWorld.java:263)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:54)
	at knot//net.minecraft.client.world.ClientWorld.tickEntities(ClientWorld.java:259)
	at knot//net.minecraft.client.MinecraftClient.tick(MinecraftClient.java:1879)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1302)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:922)
	at knot//net.minecraft.client.main.Main.main(Main.java:267)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Uptime --
Details:
	JVM uptime: 92.279s
	Wall uptime: 71.941s
	High-res time: 70.366s
	Client ticks: 1074 ticks / 53.700s
Stacktrace:
	at knot//net.minecraft.client.MinecraftClient.addDetailsToCrashReport(MinecraftClient.java:2371)
	at knot//net.minecraft.client.MinecraftClient.printCrashReport(MinecraftClient.java:991)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:943)
	at knot//net.minecraft.client.main.Main.main(Main.java:267)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Affected level --
Details:
	All players: 1 total; [ClientPlayerEntity['Player71'/6, l='ClientLevel', x=1091.51, y=72.61, z=439.57]]
	Chunk stats: 225, 49
	Level dimension: minecraft:overworld
	Level spawn location: World: (0,84,0), Section: (at 0,4,0 in 0,5,0; chunk contains blocks 0,-64,0 to 15,319,15), Region: (0,0; contains chunks 0,0 to 31,31, blocks 0,-64,0 to 511,319,511)
	Level time: 49594 game time, 4644 day time
	Server brand: fabric
	Server type: Integrated singleplayer server
	Tracked entity count: 36

-- Last reload --
Details:
	Reload number: 1
	Reload reason: initial
	Finished: Yes
	Packs: vanilla, fabric, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-blockrenderlayer-v1, fabric-client-tags-api-v1, fabric-command-api-v1, fabric-command-api-v2, fabric-commands-v0, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-keybindings-v0, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-data-attachment-v1, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader, modid

-- System Details --
Details:
	Minecraft Version: 1.21.4
	Minecraft Version ID: 1.21.4
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 21.0.6, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 163887848 bytes (156 MiB) / 586153984 bytes (559 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 1024.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 31.0.101.4032
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 10738.26
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 2118.81
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 249295.63, total: 389296.00
	Space in storage for workdir (MiB): available: 249295.63, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		fabric-api: Fabric API 0.111.0+1.21.4
		fabric-api-base: Fabric API Base 0.4.53+453d4f9104
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.84+7feeb73304
		fabric-biome-api-v1: Fabric Biome API (v1) 15.0.4+466a140b04
		fabric-block-api-v1: Fabric Block API (v1) 1.0.31+7feeb73304
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.19+7feeb73304
		fabric-blockrenderlayer-v1: Fabric BlockRenderLayer Registration (v1) 2.0.7+7feeb73304
		fabric-client-tags-api-v1: Fabric Client Tags 1.1.27+7feeb73304
		fabric-command-api-v1: Fabric Command API (v1) 1.2.61+f71b366f04
		fabric-command-api-v2: Fabric Command API (v2) 2.2.40+e496eb1504
		fabric-commands-v0: Fabric Commands (v0) 0.2.78+df3654b304
		fabric-content-registries-v0: Fabric Content Registries (v0) 9.1.12+7feeb73304
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.6+7f945d5b04
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.9.6+466a140b04
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.6+7feeb73304
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.4.3+9aea556b04
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 22.1.2+466a140b04
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.10+7feeb73304
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.0.11+60fccc7604
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.1+a4eebcf004
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.62+7feeb73304
		fabric-gametest-api-v1: Fabric Game Test API (v1) 2.0.19+7feeb73304
		fabric-item-api-v1: Fabric Item API (v1) 11.1.12+203e6b2304
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.1.21+7feeb73304
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.56+7feeb73304
		fabric-keybindings-v0: Fabric Key Bindings (v0) 0.2.54+df3654b304
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.5.3+bf2a60eb04
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.31+3f89f5a504
		fabric-loot-api-v3: Fabric Loot API (v3) 1.0.19+203e6b2304
		fabric-message-api-v1: Fabric Message API (v1) 6.0.24+7feeb73304
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 4.1.0+7b6b225304
		fabric-networking-api-v1: Fabric Networking API (v1) 4.3.8+cc0fa2fe04
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 18.0.5+e604fe7f04
		fabric-particles-v1: Fabric Particles (v1) 4.0.13+7feeb73304
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.0.3+fa62a02304
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.0.1+cc0fa2fe04
		fabric-renderer-api-v1: Fabric Renderer API (v1) 5.0.1+84404cdd04
		fabric-renderer-indigo: Fabric Renderer - Indigo 2.0.1+84404cdd04
		fabric-rendering-data-attachment-v1: Fabric Rendering Data Attachment (v1) 0.3.57+73761d2e04
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.18+7feeb73304
		fabric-rendering-v1: Fabric Rendering (v1) 10.1.0+7e7568bf04
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.13+203e6b2304
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.0.10+203e6b2304
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.37+7feeb73304
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.106+7feeb73304
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.32+7feeb73304
		fabric-transfer-api-v1: Fabric Transfer API (v1) 5.4.6+1885ad7404
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.3.2+56e78b9b04
		fabricloader: Fabric Loader 0.16.10
		java: Java HotSpot(TM) 64-Bit Server VM 21
		minecraft: Minecraft 1.21.4
		mixinextras: MixinExtras 0.4.1
		modid: Example mod 1.21.4
	Launched Version: Fabric
	Backend library: LWJGL version 3.3.3-snapshot
	Backend API: Intel(R) UHD Graphics GL version 3.2.0 - Build 31.0.101.4032, Intel
	Window size: 1920x1080
	GFLW Platform: win32
	GL Caps: Using framebuffer using OpenGL 3.2
	GL debug messages: 
	Is Modded: Definitely; Client brand changed to 'fabric'; Server brand changed to 'fabric'
	Universe: 400921fb54442d18
	Type: Integrated Server (map_client.txt)
	Graphics mode: fancy
	Render Distance: 4/4 chunks
	Resource Packs: vanilla, fabric, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-blockrenderlayer-v1, fabric-client-tags-api-v1, fabric-command-api-v1, fabric-command-api-v2, fabric-commands-v0, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-keybindings-v0, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-data-attachment-v1, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader, modid
	Current Language: es_es
	Locale: es_ES
	System encoding: Cp1252
	File encoding: UTF-8
	CPU: 8x Intel(R) Core(TM) i3-N305
	Server Running: true
	Player Count: 1 / 8; [ServerPlayerEntity['Player71'/6, l='ServerLevel[New World]', x=1091.51, y=72.61, z=439.57]]
	Active Data Packs: vanilla, fabric, fabric-convention-tags-v2, fabric-gametest-api-v1
	Available Data Packs: fabric, fabric-convention-tags-v2, fabric-gametest-api-v1, minecart_improvements, redstone_experiments, trade_rebalance, vanilla
	Enabled Feature Flags: minecraft:vanilla
	World Generation: Stable
	World Seed: 2860993946497712108
	Suppressed Exceptions: ~~NONE~~