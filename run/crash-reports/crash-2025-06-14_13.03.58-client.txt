---- Minecraft Crash Report ----
// There are four lights!

Time: 2025-06-14 13:03:58
Description: Client shutdown

java.lang.Error: Watchdog
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:164)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1375)
	at knot//net.minecraft.client.MinecraftClient.disconnect(MinecraftClient.java:2196)
	at knot//net.minecraft.client.MinecraftClient.disconnect(MinecraftClient.java:2156)
	at knot//net.minecraft.client.MinecraftClient.stop(MinecraftClient.java:1228)
	at knot//net.minecraft.client.main.Main.main(Main.java:270)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000027381019800.invokeStaticInit(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000027381002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at app//net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at app//net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000027381002400.invokeStatic(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000027381002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.devlaunchinjector.Main.main(Main.java:86)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Client shutdown watchdog
Stacktrace:
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:164)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1375)
	at knot//net.minecraft.client.MinecraftClient.disconnect(MinecraftClient.java:2196)
	at knot//net.minecraft.client.MinecraftClient.disconnect(MinecraftClient.java:2156)
	at knot//net.minecraft.client.MinecraftClient.stop(MinecraftClient.java:1228)
	at knot//net.minecraft.client.main.Main.main(Main.java:270)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000027381019800.invokeStaticInit(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000027381002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at app//net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at app//net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)

-- Thread Dump --
Details:
	Threads: "Render thread" prio=10 Id=1 RUNNABLE
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:164)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1375)
	at knot//net.minecraft.client.MinecraftClient.disconnect(MinecraftClient.java:2196)
	at knot//net.minecraft.client.MinecraftClient.disconnect(MinecraftClient.java:2156)
	at knot//net.minecraft.client.MinecraftClient.stop(MinecraftClient.java:1228)
	at knot//net.minecraft.client.main.Main.main(Main.java:270)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000027381019800.invokeStaticInit(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000027381002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at app//net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at app//net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$DMH/0x0000027381002400.invokeStatic(LambdaForm$DMH)
	at java.base@23.0.2/java.lang.invoke.LambdaForm$MH/0x0000027381002c00.invokeExact_MT(LambdaForm$MH)
	at app//net.fabricmc.devlaunchinjector.Main.main(Main.java:86)


"Reference Handler" daemon prio=10 Id=9 RUNNABLE
	at java.base@23.0.2/java.lang.ref.Reference.waitForReferencePendingList(Native Method)
	at java.base@23.0.2/java.lang.ref.Reference.processPendingReferences(Reference.java:246)
	at java.base@23.0.2/java.lang.ref.Reference$ReferenceHandler.run(Reference.java:208)


"Finalizer" daemon prio=8 Id=10 WAITING on java.lang.ref.NativeReferenceQueue$Lock@37673558
	at java.base@23.0.2/java.lang.Object.wait0(Native Method)
	-  waiting on java.lang.ref.NativeReferenceQueue$Lock@37673558
	at java.base@23.0.2/java.lang.Object.wait(Object.java:378)
	at java.base@23.0.2/java.lang.Object.wait(Object.java:352)
	at java.base@23.0.2/java.lang.ref.NativeReferenceQueue.await(NativeReferenceQueue.java:48)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:166)
	at java.base@23.0.2/java.lang.ref.NativeReferenceQueue.remove(NativeReferenceQueue.java:89)
	at java.base@23.0.2/java.lang.ref.Finalizer$FinalizerThread.run(Finalizer.java:173)


"Signal Dispatcher" daemon prio=9 Id=11 RUNNABLE


"Attach Listener" daemon prio=5 Id=12 RUNNABLE


"Notification Thread" daemon prio=9 Id=19 RUNNABLE


"Common-Cleaner" daemon prio=8 Id=20 TIMED_WAITING on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@3816069e
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@3816069e
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:79)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:151)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:229)
	at java.base@23.0.2/jdk.internal.ref.CleanerImpl.run(CleanerImpl.java:140)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)
	at java.base@23.0.2/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)


"JNA Cleaner" daemon prio=5 Id=44 TIMED_WAITING on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@3fa4cece
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@3fa4cece
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:79)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:151)
	at java.base@23.0.2/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:229)
	at knot//com.sun.jna.internal.Cleaner$CleanerThread.run(Cleaner.java:154)


"Timer hack thread" daemon prio=5 Id=45 TIMED_WAITING
	at java.base@23.0.2/java.lang.Thread.sleepNanos0(Native Method)
	at java.base@23.0.2/java.lang.Thread.sleepNanos(Thread.java:496)
	at java.base@23.0.2/java.lang.Thread.sleep(Thread.java:527)
	at knot//net.minecraft.util.Util$9.run(Util.java:988)


"Yggdrasil Key Fetcher" daemon prio=5 Id=47 TIMED_WAITING on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@1a8f5444
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@1a8f5444
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
	at java.base@23.0.2/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
	at java.base@23.0.2/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Download-1" daemon prio=5 Id=48 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@14cc6fb8
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@14cc6fb8
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Worker-Main-1" daemon prio=5 Id=54 WAITING on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Worker-Main-2" daemon prio=5 Id=56 RUNNABLE
	at java.base@23.0.2/java.io.FileOutputStream.writeBytes(Native Method)
	at java.base@23.0.2/java.io.FileOutputStream.write(FileOutputStream.java:400)
	at java.base@23.0.2/java.lang.System$Out.write(System.java:2290)
	at java.base@23.0.2/java.io.BufferedOutputStream.implWrite(BufferedOutputStream.java:217)
	at java.base@23.0.2/java.io.BufferedOutputStream.write(BufferedOutputStream.java:200)
	at java.base@23.0.2/java.io.PrintStream.implWrite(PrintStream.java:647)
	at java.base@23.0.2/java.io.PrintStream.write(PrintStream.java:627)
	at knot//org.apache.logging.log4j.core.util.CloseShieldOutputStream.write(CloseShieldOutputStream.java:53)
	at knot//org.apache.logging.log4j.core.appender.OutputStreamManager.writeToDestination(OutputStreamManager.java:263)
	at knot//org.apache.logging.log4j.core.appender.OutputStreamManager.flushBuffer(OutputStreamManager.java:296)
	at knot//org.apache.logging.log4j.core.appender.OutputStreamManager.flush(OutputStreamManager.java:307)
	-  locked org.apache.logging.log4j.core.appender.OutputStreamManager@52dc6b5
	at knot//org.apache.logging.log4j.core.appender.AbstractOutputStreamAppender.directEncodeEvent(AbstractOutputStreamAppender.java:229)
	at knot//org.apache.logging.log4j.core.appender.AbstractOutputStreamAppender.tryAppend(AbstractOutputStreamAppender.java:220)
	at knot//org.apache.logging.log4j.core.appender.AbstractOutputStreamAppender.append(AbstractOutputStreamAppender.java:211)
	at knot//org.apache.logging.log4j.core.config.AppenderControl.tryCallAppender(AppenderControl.java:160)
	at knot//org.apache.logging.log4j.core.config.AppenderControl.callAppender0(AppenderControl.java:133)
	at knot//org.apache.logging.log4j.core.config.AppenderControl.callAppenderPreventRecursion(AppenderControl.java:124)
	at knot//org.apache.logging.log4j.core.config.AppenderControl.callAppender(AppenderControl.java:88)
	at knot//org.apache.logging.log4j.core.config.LoggerConfig.callAppenders(LoggerConfig.java:714)
	at knot//org.apache.logging.log4j.core.config.LoggerConfig.processLogEvent(LoggerConfig.java:672)
	at knot//org.apache.logging.log4j.core.config.LoggerConfig.log(LoggerConfig.java:648)
	at knot//org.apache.logging.log4j.core.config.LoggerConfig.logParent(LoggerConfig.java:705)
	at knot//org.apache.logging.log4j.core.config.LoggerConfig.processLogEvent(LoggerConfig.java:674)
	at knot//org.apache.logging.log4j.core.config.LoggerConfig.log(LoggerConfig.java:648)
	at knot//org.apache.logging.log4j.core.config.LoggerConfig.log(LoggerConfig.java:584)
	at knot//org.apache.logging.log4j.core.config.AwaitCompletionReliabilityStrategy.log(AwaitCompletionReliabilityStrategy.java:92)
	at knot//org.apache.logging.log4j.core.Logger.log(Logger.java:240)
	at knot//org.apache.logging.log4j.spi.AbstractLogger.tryLogMessage(AbstractLogger.java:2904)
	at knot//org.apache.logging.log4j.spi.AbstractLogger.logMessageTrackRecursion(AbstractLogger.java:2857)
	at knot//org.apache.logging.log4j.spi.AbstractLogger.logMessageSafely(AbstractLogger.java:2839)
	at knot//org.apache.logging.log4j.spi.AbstractLogger.logMessage(AbstractLogger.java:2646)
	at knot//org.apache.logging.log4j.spi.AbstractLogger.logIfEnabled(AbstractLogger.java:2413)
	at knot//org.apache.logging.slf4j.Log4jLogger.info(Log4jLogger.java:183)
	at knot//net.minecraft.util.logging.LoggerPrintStream.log(LoggerPrintStream.java:31)
	at knot//net.minecraft.util.logging.LoggerPrintStream.println(LoggerPrintStream.java:22)
	at knot//net.minecraft.structure.pool.StructurePoolBasedGenerator$StructurePoolGenerator.handler$zcp000$ai_villagers$afterGeneratePiece(StructurePoolBasedGenerator.java:1097)
	at knot//net.minecraft.structure.pool.StructurePoolBasedGenerator$StructurePoolGenerator.generatePiece(StructurePoolBasedGenerator.java:260)
	at knot//net.minecraft.structure.pool.StructurePoolBasedGenerator.generate(StructurePoolBasedGenerator.java:373)
	at knot//net.minecraft.structure.pool.StructurePoolBasedGenerator.method_39824(StructurePoolBasedGenerator.java:337)
	at knot//net.minecraft.structure.pool.StructurePoolBasedGenerator$$Lambda/0x000002738244f030.accept(Unknown Source)
	at knot//net.minecraft.world.gen.structure.Structure$StructurePosition.method_44021(Structure.java:156)
	at knot//net.minecraft.world.gen.structure.Structure$StructurePosition$$Lambda/0x000002738244f6d0.apply(Unknown Source)
	at knot//com.mojang.datafixers.util.Either$Left.map(Either.java:38)
	at knot//net.minecraft.world.gen.structure.Structure$StructurePosition.generate(Structure.java:154)
	at knot//net.minecraft.world.gen.structure.Structure.createStructureStart(Structure.java:168)
	at knot//net.minecraft.world.gen.chunk.ChunkGenerator.trySetStructureStart(ChunkGenerator.java:518)
	at knot//net.minecraft.world.gen.chunk.ChunkGenerator.method_41041(ChunkGenerator.java:500)
	at knot//net.minecraft.world.gen.chunk.ChunkGenerator$$Lambda/0x00000273822562e8.accept(Unknown Source)
	at java.base@23.0.2/java.lang.Iterable.forEach(Iterable.java:75)
	at knot//net.minecraft.world.gen.chunk.ChunkGenerator.setStructureStarts(ChunkGenerator.java:453)
	at knot//net.minecraft.world.chunk.ChunkGenerating.generateStructures(ChunkGenerating.java:37)
	at knot//net.minecraft.world.chunk.ChunkGenerationSteps$$Lambda/0x0000027381abafa8.doWork(Unknown Source)
	at knot//net.minecraft.world.chunk.ChunkGenerationStep.run(ChunkGenerationStep.java:33)
	at knot//net.minecraft.server.world.ServerChunkLoadingManager.generate(ServerChunkLoadingManager.java:661)
	at knot//net.minecraft.world.chunk.AbstractChunkHolder.generate(AbstractChunkHolder.java:101)
	at knot//net.minecraft.world.chunk.ChunkLoader.load(ChunkLoader.java:148)
	at knot//net.minecraft.world.chunk.ChunkLoader.loadAll(ChunkLoader.java:125)
	at knot//net.minecraft.world.chunk.ChunkLoader.loadNextStatus(ChunkLoader.java:76)
	at knot//net.minecraft.world.chunk.ChunkLoader.run(ChunkLoader.java:61)
	at knot//net.minecraft.server.world.ServerChunkLoadingManager.method_60446(ServerChunkLoadingManager.java:695)
	at knot//net.minecraft.server.world.ServerChunkLoadingManager$$Lambda/0x000002738223ad50.run(Unknown Source)
	at knot//net.minecraft.server.world.ChunkTaskScheduler.method_63554(ChunkTaskScheduler.java:88)
	at knot//net.minecraft.server.world.ChunkTaskScheduler$$Lambda/0x0000027382240468.accept(Unknown Source)
	at knot//net.minecraft.util.thread.TaskExecutor.method_63604(TaskExecutor.java:21)
	at knot//net.minecraft.util.thread.TaskExecutor$$Lambda/0x00000273822406a0.run(Unknown Source)
	at knot//net.minecraft.util.Util.runInNamedZone(Util.java:297)
	at knot//net.minecraft.util.thread.ConsecutiveExecutor.runOnce(ConsecutiveExecutor.java:50)
	at knot//net.minecraft.util.thread.ConsecutiveExecutor.run(ConsecutiveExecutor.java:62)
	at java.base@23.0.2/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.compute(ForkJoinTask.java:1726)
	at java.base@23.0.2/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.compute(ForkJoinTask.java:1717)
	at java.base@23.0.2/java.util.concurrent.ForkJoinTask$InterruptibleTask.exec(ForkJoinTask.java:1641)
	at java.base@23.0.2/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:507)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1460)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2036)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)

	Number of locked synchronizers = 2
	- java.util.concurrent.locks.ReentrantLock$NonfairSync@12268d9b
	- java.util.concurrent.locks.ReentrantLock$NonfairSync@3b234277


"Worker-Main-3" daemon prio=5 Id=57 WAITING on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Worker-Main-4" daemon prio=5 Id=58 WAITING on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Worker-Main-5" daemon prio=5 Id=59 WAITING on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Worker-Main-6" daemon prio=5 Id=60 WAITING on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"Worker-Main-7" daemon prio=5 Id=61 WAITING on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.ForkJoinPool@1c66a949
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:2137)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.deactivate(ForkJoinPool.java:2101)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2045)
	at java.base@23.0.2/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)


"IO-Worker-1" prio=10 Id=62 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Server thread" prio=8 Id=65 TIMED_WAITING on java.lang.String@3128bf7d
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.lang.String@3128bf7d
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at knot//net.minecraft.server.MinecraftServer.waitForTasks(MinecraftServer.java:840)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:149)
	at knot//net.minecraft.server.MinecraftServer.runTasks(MinecraftServer.java:822)
	at knot//net.minecraft.server.MinecraftServer.runTasksTillTickEnd(MinecraftServer.java:829)
	at knot//net.minecraft.server.MinecraftServer.shutdown(MinecraftServer.java:624)
	at knot//net.minecraft.server.integrated.IntegratedServer.shutdown(IntegratedServer.java:223)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:747)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:290)
	at knot//net.minecraft.server.MinecraftServer$$Lambda/0x00000273821d6688.run(Unknown Source)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-2" prio=5 Id=66 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-4" prio=10 Id=68 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-5" prio=10 Id=69 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-6" prio=10 Id=70 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Netty Server IO #0" daemon prio=5 Id=71 RUNNABLE (in native)
	at java.base@23.0.2/sun.nio.ch.WEPoll.wait(Native Method)
	at java.base@23.0.2/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
	at java.base@23.0.2/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
	-  locked io.netty.channel.nio.SelectedSelectionKeySet@1744a696
	-  locked sun.nio.ch.WEPollSelectorImpl@352c54e5
	at java.base@23.0.2/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:147)
	at knot//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at knot//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:887)
	at knot//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at knot//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at knot//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Netty Local Client IO #0" daemon prio=5 Id=72 WAITING on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@36556ef3
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@36556ef3
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4023)
	at java.base@23.0.2/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3969)
	at java.base@23.0.2/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
	at java.base@23.0.2/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
	at knot//io.netty.util.concurrent.SingleThreadEventExecutor.takeTask(SingleThreadEventExecutor.java:243)
	at knot//io.netty.channel.DefaultEventLoop.run(DefaultEventLoop.java:52)
	at knot//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at knot//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Netty Server IO #1" daemon prio=5 Id=73 RUNNABLE (in native)
	at java.base@23.0.2/sun.nio.ch.WEPoll.wait(Native Method)
	at java.base@23.0.2/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
	at java.base@23.0.2/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
	-  locked io.netty.channel.nio.SelectedSelectionKeySet@674a5582
	-  locked sun.nio.ch.WEPollSelectorImpl@61fb793a
	at java.base@23.0.2/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:147)
	at knot//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at knot//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:887)
	at knot//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at knot//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at knot//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"IO-Worker-7" prio=5 Id=76 TIMED_WAITING on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.util.concurrent.SynchronousQueue$Transferer@50d033c8
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@23.0.2/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@23.0.2/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@23.0.2/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Client shutdown watchdog" daemon prio=10 Id=77 RUNNABLE
	at java.management@23.0.2/sun.management.ThreadImpl.dumpThreads0(Native Method)
	at java.management@23.0.2/sun.management.ThreadImpl.dumpAllThreads(ThreadImpl.java:518)
	at java.management@23.0.2/sun.management.ThreadImpl.dumpAllThreads(ThreadImpl.java:506)
	at knot//net.minecraft.server.dedicated.DedicatedServerWatchdog.createCrashReport(DedicatedServerWatchdog.java:75)
	at knot//net.minecraft.client.ClientWatchdog.method_61935(ClientWatchdog.java:26)
	at knot//net.minecraft.client.ClientWatchdog$$Lambda/0x00000273824d8448.run(Unknown Source)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)


"Sound engine" daemon prio=10 Id=78 WAITING on java.lang.String@3128bf7d
	at java.base@23.0.2/jdk.internal.misc.Unsafe.park(Native Method)
	-  waiting on java.lang.String@3128bf7d
	at java.base@23.0.2/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221)
	at knot//net.minecraft.client.sound.SoundExecutor.waitForTasks(SoundExecutor.java:49)
	at knot//net.minecraft.util.thread.ThreadExecutor.runTasks(ThreadExecutor.java:149)
	at knot//net.minecraft.client.sound.SoundExecutor.waitForStop(SoundExecutor.java:42)
	at knot//net.minecraft.client.sound.SoundExecutor$$Lambda/0x0000027381e17da0.run(Unknown Source)
	at java.base@23.0.2/java.lang.Thread.runWith(Thread.java:1588)
	at java.base@23.0.2/java.lang.Thread.run(Thread.java:1575)



Stacktrace:
	at knot//net.minecraft.server.dedicated.DedicatedServerWatchdog.createCrashReport(DedicatedServerWatchdog.java:90)
	at knot//net.minecraft.client.ClientWatchdog.method_61935(ClientWatchdog.java:26)
	at java.base/java.lang.Thread.run(Thread.java:1575)

-- System Details --
Details:
	Minecraft Version: 1.21.5
	Minecraft Version ID: 1.21.5
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 221794880 bytes (211 MiB) / 1052770304 bytes (1004 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6874
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 10686.71
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 2580.44
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 177910.89, total: 389296.00
	Space in storage for workdir (MiB): available: 177910.89, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: AiVillagersFabric 1.0.0
		fabric-api: Fabric API 0.125.3+1.21.5
		fabric-api-base: Fabric API Base 0.4.62+73a52b4b49
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.96+86c3a9f149
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.7+2dd063df49
		fabric-block-api-v1: Fabric Block API (v1) 1.1.0+ed91556f49
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.26+aa6d566c49
		fabric-blockrenderlayer-v1: Fabric BlockRenderLayer Registration (v1) 2.0.16+86c3a9f149
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.0+dac2d6e349
		fabric-client-tags-api-v1: Fabric Client Tags 1.1.37+86c3a9f149
		fabric-command-api-v1: Fabric Command API (v1) 1.2.70+f71b366f49
		fabric-command-api-v2: Fabric Command API (v2) 2.2.49+73a52b4b49
		fabric-commands-v0: Fabric Commands (v0) 0.2.87+df3654b349
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.14+3e6c1f7d49
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.32+7f945d5b49
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.1+0570995b49
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.12+86c3a9f149
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.3+bc514c4549
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 22.4.2+ea72995749
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.17+3e6c1f7d49
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.0+3ce7866349
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.15+64e3057949
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.70+c327076a49
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.3+2a6ec84b49
		fabric-item-api-v1: Fabric Item API (v1) 11.4.1+e46fd76a49
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.9+3459fc6149
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.63+ecf51cdc49
		fabric-keybindings-v0: Fabric Key Bindings (v0) 0.2.61+df3654b349
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.0+230071a049
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.48+3f89f5a549
		fabric-loot-api-v3: Fabric Loot API (v3) 1.0.36+86c3a9f149
		fabric-message-api-v1: Fabric Message API (v1) 6.1.0+fe971bba49
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.0+c982b95149
		fabric-networking-api-v1: Fabric Networking API (v1) 4.5.0+775be32c49
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.1+b8d6ba7049
		fabric-particles-v1: Fabric Particles (v1) 4.1.2+112e550e49
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.8+3235ab3249
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.21+b556383249
		fabric-renderer-api-v1: Fabric Renderer API (v1) 6.0.2+c982b95149
		fabric-renderer-indigo: Fabric Renderer - Indigo 3.0.3+3e6c1f7d49
		fabric-rendering-data-attachment-v1: Fabric Rendering Data Attachment (v1) 0.3.64+73761d2e49
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.27+86c3a9f149
		fabric-rendering-v1: Fabric Rendering (v1) 11.1.11+5490746649
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.21+73a52b4b49
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.7+847e5f5c49
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.46+86c3a9f149
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.128+c327076a49
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.38+86c3a9f149
		fabric-tag-api-v1: Fabric Tag API (v1) 1.0.17+ecf51cdc49
		fabric-transfer-api-v1: Fabric Transfer API (v1) 5.4.24+7b20cbb049
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.3.17+f17a180c49
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.5
		mixinextras: MixinExtras 0.4.1