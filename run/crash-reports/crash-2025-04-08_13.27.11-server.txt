---- Minecraft Crash Report ----
// You should try our sister game, <PERSON>ceraft!

Time: 2025-04-08 13:27:11
Description: Ticking entity

java.lang.NullPointerException: Cannot invoke "java.util.List.removeIf(java.util.function.Predicate)" because "tasks" is null
	at knot//net.minecraft.entity.ai.brain.task.VillagerTaskListProvider.handler$zgh000$ai_villagers$cancelFishermanWorkStationTask(VillagerTaskListProvider.java:529)
	at knot//net.minecraft.entity.ai.brain.task.VillagerTaskListProvider.createCoreTasks(VillagerTaskListProvider.java)
	at knot//net.minecraft.entity.passive.VillagerEntity.initBrain(VillagerEntity.java:243)
	at knot//net.minecraft.entity.passive.VillagerEntity.reinitializeBrain(VillagerEntity.java:226)
	at knot//net.minecraft.entity.ai.brain.task.UpdateJobSiteTask.method_46891(UpdateJobSiteTask.java:40)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at knot//net.minecraft.entity.ai.brain.task.UpdateJobSiteTask.method_46890(UpdateJobSiteTask.java:38)
	at knot//net.minecraft.entity.ai.brain.task.TaskTriggerer$1.trigger(TaskTriggerer.java:108)
	at knot//net.minecraft.entity.ai.brain.task.SingleTickTask.tryStarting(SingleTickTask.java:16)
	at knot//net.minecraft.entity.ai.brain.Brain.startTasks(Brain.java:448)
	at knot//net.minecraft.entity.ai.brain.Brain.tick(Brain.java:408)
	at knot//net.minecraft.entity.passive.VillagerEntity.mobTick(VillagerEntity.java:281)
	at knot//net.minecraft.entity.mob.MobEntity.tickNewAi(MobEntity.java:693)
	at knot//net.minecraft.entity.LivingEntity.tickMovement(LivingEntity.java:2870)
	at knot//net.minecraft.entity.mob.MobEntity.tickMovement(MobEntity.java:474)
	at knot//net.minecraft.entity.passive.PassiveEntity.tickMovement(PassiveEntity.java:129)
	at knot//net.minecraft.entity.LivingEntity.tick(LivingEntity.java:2633)
	at knot//net.minecraft.entity.mob.MobEntity.tick(MobEntity.java:371)
	at knot//net.minecraft.entity.passive.VillagerEntity.tick(VillagerEntity.java:321)
	at knot//net.minecraft.server.world.ServerWorld.tickEntity(ServerWorld.java:793)
	at knot//net.minecraft.world.World.tickEntity(World.java:582)
	at knot//net.minecraft.server.world.ServerWorld.method_31420(ServerWorld.java:425)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:76)
	at knot//net.minecraft.server.world.ServerWorld.tick(ServerWorld.java:408)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1100)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:992)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:757)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:299)
	at java.base/java.lang.Thread.run(Thread.java:1575)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Server thread
Stacktrace:
	at knot//net.minecraft.entity.ai.brain.task.VillagerTaskListProvider.handler$zgh000$ai_villagers$cancelFishermanWorkStationTask(VillagerTaskListProvider.java:529)
	at knot//net.minecraft.entity.ai.brain.task.VillagerTaskListProvider.createCoreTasks(VillagerTaskListProvider.java)
	at knot//net.minecraft.entity.passive.VillagerEntity.initBrain(VillagerEntity.java:243)
	at knot//net.minecraft.entity.passive.VillagerEntity.reinitializeBrain(VillagerEntity.java:226)
	at knot//net.minecraft.entity.ai.brain.task.UpdateJobSiteTask.method_46891(UpdateJobSiteTask.java:40)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at knot//net.minecraft.entity.ai.brain.task.UpdateJobSiteTask.method_46890(UpdateJobSiteTask.java:38)
	at knot//net.minecraft.entity.ai.brain.task.TaskTriggerer$1.trigger(TaskTriggerer.java:108)
	at knot//net.minecraft.entity.ai.brain.task.SingleTickTask.tryStarting(SingleTickTask.java:16)
	at knot//net.minecraft.entity.ai.brain.Brain.startTasks(Brain.java:448)
	at knot//net.minecraft.entity.ai.brain.Brain.tick(Brain.java:408)
	at knot//net.minecraft.entity.passive.VillagerEntity.mobTick(VillagerEntity.java:281)
	at knot//net.minecraft.entity.mob.MobEntity.tickNewAi(MobEntity.java:693)
	at knot//net.minecraft.entity.LivingEntity.tickMovement(LivingEntity.java:2870)
	at knot//net.minecraft.entity.mob.MobEntity.tickMovement(MobEntity.java:474)
	at knot//net.minecraft.entity.passive.PassiveEntity.tickMovement(PassiveEntity.java:129)
	at knot//net.minecraft.entity.LivingEntity.tick(LivingEntity.java:2633)
	at knot//net.minecraft.entity.mob.MobEntity.tick(MobEntity.java:371)
	at knot//net.minecraft.entity.passive.VillagerEntity.tick(VillagerEntity.java:321)
	at knot//net.minecraft.server.world.ServerWorld.tickEntity(ServerWorld.java:793)
	at knot//net.minecraft.world.World.tickEntity(World.java:582)
	at knot//net.minecraft.server.world.ServerWorld.method_31420(ServerWorld.java:425)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:76)

-- Entity being ticked --
Details:
	Entity Type: minecraft:villager (net.minecraft.entity.passive.VillagerEntity)
	Entity ID: 141
	Entity Name: Pescador
	Entity's Exact location: -262.36, 63.00, 388.55
	Entity's Block location: World: (-263,63,388), Section: (at 9,15,4 in -17,3,24; chunk contains blocks -272,-64,384 to -257,319,399), Region: (-1,0; contains chunks -32,0 to -1,31, blocks -512,-64,0 to -1,319,511)
	Entity's Momentum: 0.05, -0.08, 0.01
	Entity's Passengers: []
	Entity's Vehicle: null
Stacktrace:
	at knot//net.minecraft.world.World.tickEntity(World.java:582)
	at knot//net.minecraft.server.world.ServerWorld.method_31420(ServerWorld.java:425)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:76)
	at knot//net.minecraft.server.world.ServerWorld.tick(ServerWorld.java:408)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1100)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:992)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:757)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:299)
	at java.base/java.lang.Thread.run(Thread.java:1575)

-- Affected level --
Details:
	All players: 1 total; ServerPlayerEntity{name=Player123, id=38, pos=(-261.1357937059318, 64.06045824641463, 391.6262150689696), mode=CREATIVE, permission=4}
	Chunk stats: 1922
	Level dimension: minecraft:overworld
	Level spawn location: World: (-16,95,16), Section: (at 0,15,0 in -1,5,1; chunk contains blocks -16,-64,16 to -1,319,31), Region: (-1,0; contains chunks -32,0 to -1,31, blocks -512,-64,0 to -1,319,511)
	Level time: 30251 game time, 11776 day time
	Level name: Fisher
	Level game mode: Game mode: creative (ID 1). Hardcore: false. Commands: true
	Level weather: Rain time: 122433 (now: false), thunder time: 100416 (now: false)
	Known server brands: fabric
	Removed feature flags: 
	Level was modded: true
	Level storage version: 0x04ABD - Anvil
	Loaded entity count: 98
Stacktrace:
	at knot//net.minecraft.server.world.ServerWorld.addDetailsToCrashReport(ServerWorld.java:1952)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1103)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:992)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:757)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:299)
	at java.base/java.lang.Thread.run(Thread.java:1575)

-- System Details --
Details:
	Minecraft Version: 1.21.5
	Minecraft Version ID: 1.21.5
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 137363456 bytes (131 MiB) / 870318080 bytes (830 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6651
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 9966.59
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 2224.21
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 254159.80, total: 389296.00
	Space in storage for workdir (MiB): available: 254159.80, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: AiVillagersFabric 1.0.0
		fabric-api: Fabric API 0.119.6+1.21.5
		fabric-api-base: Fabric API Base 0.4.62+73a52b4b49
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.95+86c3a9f149
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.7+2dd063df49
		fabric-block-api-v1: Fabric Block API (v1) 1.0.37+86c3a9f149
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.26+aa6d566c49
		fabric-blockrenderlayer-v1: Fabric BlockRenderLayer Registration (v1) 2.0.16+86c3a9f149
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.1.10+f3ffa98949
		fabric-client-tags-api-v1: Fabric Client Tags 1.1.37+86c3a9f149
		fabric-command-api-v1: Fabric Command API (v1) 1.2.70+f71b366f49
		fabric-command-api-v2: Fabric Command API (v2) 2.2.49+73a52b4b49
		fabric-commands-v0: Fabric Commands (v0) 0.2.87+df3654b349
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.11+216530c849
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.28+7f945d5b49
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.14.2+7d35484649
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.12+86c3a9f149
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.6.6+7b20cbb049
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 22.3.2+d94614b849
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.16+86c3a9f149
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.0.25+27c1078f49
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.13+73a52b4b49
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.70+c327076a49
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.2+2a6ec84b49
		fabric-item-api-v1: Fabric Item API (v1) 11.3.1+eeb42f0249
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.8+3459fc6149
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.63+ecf51cdc49
		fabric-keybindings-v0: Fabric Key Bindings (v0) 0.2.61+df3654b349
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.5.13+5cbce67349
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.47+3f89f5a549
		fabric-loot-api-v3: Fabric Loot API (v3) 1.0.35+86c3a9f149
		fabric-message-api-v1: Fabric Message API (v1) 6.0.33+86c3a9f149
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.0.3+129968e949
		fabric-networking-api-v1: Fabric Networking API (v1) 4.4.0+f3ffa98949
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.0.0+7b20cbb049
		fabric-particles-v1: Fabric Particles (v1) 4.0.22+86c3a9f149
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.6+052a85d049
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.20+b556383249
		fabric-renderer-api-v1: Fabric Renderer API (v1) 6.0.0+55bce67a49
		fabric-renderer-indigo: Fabric Renderer - Indigo 3.0.0+55bce67a49
		fabric-rendering-data-attachment-v1: Fabric Rendering Data Attachment (v1) 0.3.64+73761d2e49
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.27+86c3a9f149
		fabric-rendering-v1: Fabric Rendering (v1) 11.1.11+081cc04349
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.21+73a52b4b49
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.6+02ca679649
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.46+86c3a9f149
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.127+c327076a49
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.38+86c3a9f149
		fabric-tag-api-v1: Fabric Tag API (v1) 1.0.16+ecf51cdc49
		fabric-transfer-api-v1: Fabric Transfer API (v1) 5.4.23+7b20cbb049
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.3.17+f17a180c49
		fabricloader: Fabric Loader 0.16.12
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.5
		mixinextras: MixinExtras 0.4.1
	Server Running: true
	Player Count: 1 / 8; [ServerPlayerEntity['Player123'/38, l='ServerLevel[Fisher]', x=-261.14, y=64.06, z=391.63]]
	Active Data Packs: vanilla, fabric, fabric-convention-tags-v2, fabric-gametest-api-v1
	Available Data Packs: fabric, fabric-convention-tags-v2, fabric-gametest-api-v1, minecart_improvements, redstone_experiments, trade_rebalance, vanilla
	Enabled Feature Flags: minecraft:vanilla
	World Generation: Stable
	World Seed: -1407141413364013521
	Suppressed Exceptions: ~~NONE~~
	Type: Integrated Server (map_client.txt)
	Is Modded: Definitely; Client brand changed to 'fabric'; Server brand changed to 'fabric'
	Launched Version: Fabric