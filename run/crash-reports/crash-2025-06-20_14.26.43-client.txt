---- Minecraft Crash Report ----
// You're mean.

Time: 2025-06-20 14:26:43
Description: Registry Loading

java.lang.IllegalStateException: Failed to load registries due to errors
	at knot//net.minecraft.registry.RegistryLoader.createLoadingException(RegistryLoader.java)
	at knot//net.minecraft.registry.RegistryLoader.writeAndCreateLoadingException(RegistryLoader.java:156)
	at knot//net.minecraft.registry.RegistryLoader.load(RegistryLoader.java:128)
	at knot//net.minecraft.registry.RegistryLoader.mixinextras$bridge$load$39(RegistryLoader.java)
	at knot//net.minecraft.registry.RegistryLoader.wrapOperation$zno000$fabric-registry-sync-v0$wrapIsServerCall(RegistryLoader.java:1555)
	at knot//net.minecraft.registry.RegistryLoader.loadFromResource(RegistryLoader.java:104)
	at knot//net.minecraft.server.SaveLoading.load(SaveLoading.java:42)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:357)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:321)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:308)
	at knot//net.minecraft.client.gui.screen.world.SelectWorldScreen.method_19944(SelectWorldScreen.java:55)
	at knot//net.minecraft.client.gui.widget.ButtonWidget.onPress(ButtonWidget.java:96)
	at knot//net.minecraft.client.gui.widget.PressableWidget.onClick(PressableWidget.java:43)
	at knot//net.minecraft.client.gui.widget.ClickableWidget.mouseClicked(ClickableWidget.java:136)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.Mouse.mixinextras$bridge$mouseClicked$38(Mouse.java)
	at knot//net.minecraft.client.Mouse.wrapOperation$ban000$fabric-screen-api-v1$invokeMouseClickedEvents(Mouse.java:545)
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:106)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:179)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1383)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:947)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Render thread
Stacktrace:
	at knot//net.minecraft.registry.RegistryLoader.createLoadingException(RegistryLoader.java)
	at knot//net.minecraft.registry.RegistryLoader.writeAndCreateLoadingException(RegistryLoader.java:156)
	at knot//net.minecraft.registry.RegistryLoader.load(RegistryLoader.java:128)
	at knot//net.minecraft.registry.RegistryLoader.mixinextras$bridge$load$39(RegistryLoader.java)
	at knot//net.minecraft.registry.RegistryLoader.wrapOperation$zno000$fabric-registry-sync-v0$wrapIsServerCall(RegistryLoader.java:1555)
	at knot//net.minecraft.registry.RegistryLoader.loadFromResource(RegistryLoader.java:104)
	at knot//net.minecraft.server.SaveLoading.load(SaveLoading.java:42)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:357)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:321)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:308)
	at knot//net.minecraft.client.gui.screen.world.SelectWorldScreen.method_19944(SelectWorldScreen.java:55)
	at knot//net.minecraft.client.gui.widget.ButtonWidget.onPress(ButtonWidget.java:96)
	at knot//net.minecraft.client.gui.widget.PressableWidget.onClick(PressableWidget.java:43)
	at knot//net.minecraft.client.gui.widget.ClickableWidget.mouseClicked(ClickableWidget.java:136)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.Mouse.mixinextras$bridge$mouseClicked$38(Mouse.java)
	at knot//net.minecraft.client.Mouse.wrapOperation$ban000$fabric-screen-api-v1$invokeMouseClickedEvents(Mouse.java:545)
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:106)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)

-- Loading info --
Details:
	Errors: 
		minecraft:root/minecraft:worldgen/structure: Unbound values in registry ResourceKey[minecraft:root / minecraft:worldgen/structure]: [minecraft:village_plains]
		minecraft:worldgen/structure/minecraft:village_plains: Failed to parse minecraft:worldgen/structure/village_plains.json from pack ai_villagers
Stacktrace:
	at knot//net.minecraft.registry.RegistryLoader.createLoadingException(RegistryLoader.java)
	at knot//net.minecraft.registry.RegistryLoader.writeAndCreateLoadingException(RegistryLoader.java:156)
	at knot//net.minecraft.registry.RegistryLoader.load(RegistryLoader.java:128)
	at knot//net.minecraft.registry.RegistryLoader.mixinextras$bridge$load$39(RegistryLoader.java)
	at knot//net.minecraft.registry.RegistryLoader.wrapOperation$zno000$fabric-registry-sync-v0$wrapIsServerCall(RegistryLoader.java:1555)
	at knot//net.minecraft.registry.RegistryLoader.loadFromResource(RegistryLoader.java:104)
	at knot//net.minecraft.server.SaveLoading.load(SaveLoading.java:42)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:357)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:321)
	at knot//net.minecraft.client.gui.screen.world.CreateWorldScreen.show(CreateWorldScreen.java:308)
	at knot//net.minecraft.client.gui.screen.world.SelectWorldScreen.method_19944(SelectWorldScreen.java:55)
	at knot//net.minecraft.client.gui.widget.ButtonWidget.onPress(ButtonWidget.java:96)
	at knot//net.minecraft.client.gui.widget.PressableWidget.onClick(PressableWidget.java:43)
	at knot//net.minecraft.client.gui.widget.ClickableWidget.mouseClicked(ClickableWidget.java:136)
	at knot//net.minecraft.client.gui.ParentElement.mouseClicked(ParentElement.java:43)
	at knot//net.minecraft.client.Mouse.mixinextras$bridge$mouseClicked$38(Mouse.java)
	at knot//net.minecraft.client.Mouse.wrapOperation$ban000$fabric-screen-api-v1$invokeMouseClickedEvents(Mouse.java:545)
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:106)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:179)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1383)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:947)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Affected screen --
Details:
	Screen name: net.minecraft.client.gui.screen.world.SelectWorldScreen
Stacktrace:
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:106)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:179)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1383)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:947)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Mouse --
Details:
	Mouse location: Scaled: (285.500000, 201.000000). Absolute: (571.000000, 402.000000)
	Screen size: ~~ERROR~~ IllegalFormatConversionException: f != java.lang.Integer
	Button: 0
Stacktrace:
	at knot//net.minecraft.client.Mouse.onMouseButton(Mouse.java:120)
	at knot//net.minecraft.client.Mouse.method_22686(Mouse.java:226)
	at knot//net.minecraft.util.thread.ThreadExecutor.execute(ThreadExecutor.java:106)
	at knot//net.minecraft.client.Mouse.method_22684(Mouse.java:226)
	at knot//org.lwjgl.glfw.GLFWMouseButtonCallbackI.callback(GLFWMouseButtonCallbackI.java:43)
	at knot//org.lwjgl.system.JNI.invokeV(Native Method)
	at knot//org.lwjgl.glfw.GLFW.glfwWaitEventsTimeout(GLFW.java:3509)
	at knot//com.mojang.blaze3d.systems.RenderSystem.limitDisplayFPS(RenderSystem.java:179)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1383)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:947)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Uptime --
Details:
	JVM uptime: 35.125s
	Wall uptime: 15.587s
	High-res time: 13.912s
	Client ticks: 172 ticks / 8.600s
Stacktrace:
	at knot//net.minecraft.client.MinecraftClient.addDetailsToCrashReport(MinecraftClient.java:2411)
	at knot//net.minecraft.client.MinecraftClient.printCrashReport(MinecraftClient.java:1016)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:968)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Last reload --
Details:
	Reload number: 1
	Reload reason: initial
	Finished: Yes
	Packs: vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader

-- System Details --
Details:
	Minecraft Version: 1.21.6
	Minecraft Version ID: 1.21.6
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 153141608 bytes (146 MiB) / 587202560 bytes (560 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6874
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 10994.80
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 2892.38
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 175528.52, total: 389296.00
	Space in storage for workdir (MiB): available: 175528.52, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: AiVillagersFabric 1.0.0
		fabric-api: Fabric API 0.127.0+1.21.6
		fabric-api-base: Fabric API Base 0.4.63+9ec45cd89c
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.99+9ec45cd89c
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.10+fa6cb72b9c
		fabric-block-api-v1: Fabric Block API (v1) 1.1.2+78dbe4fb9c
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.30+d32f812d9c
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.3+458b8c9a9c
		fabric-command-api-v2: Fabric Command API (v2) 2.2.52+b39a696a9c
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.17+fa6cb72b9c
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.37+7f945d5b9c
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.2+d9a896309c
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.14+fa6cb72b9c
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.8+d9a896309c
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 23.2.0+a12c79229c
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.18+75fa737a9c
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.0+c9e472739c
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.22+0d4d74479c
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.71+9ec45cd89c
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.8+39ce47f59c
		fabric-item-api-v1: Fabric Item API (v1) 11.4.2+5e29f1899c
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.12+9ec45cd89c
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.64+9ec45cd89c
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.2+db4dfd859c
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.54+3f89f5a59c
		fabric-loot-api-v3: Fabric Loot API (v3) 2.0.1+f40817309c
		fabric-message-api-v1: Fabric Message API (v1) 6.1.0+8efa0e499c
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.4+ae8be2b89c
		fabric-networking-api-v1: Fabric Networking API (v1) 5.0.0+d32f812d9c
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.5+946cf7899c
		fabric-particles-v1: Fabric Particles (v1) 4.1.6+c1dce2189c
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.13+39ce47f59c
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.25+9ec45cd89c
		fabric-renderer-api-v1: Fabric Renderer API (v1) 7.0.1+a0cfcc829c
		fabric-renderer-indigo: Fabric Renderer - Indigo 4.0.1+2516f2229c
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.29+fa6cb72b9c
		fabric-rendering-v1: Fabric Rendering (v1) 12.3.0+ac3e15d19c
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.23+908cbc919c
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.10+fa6cb72b9c
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.50+908cbc919c
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.134+d32f812d9c
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.41+d32f812d9c
		fabric-tag-api-v1: Fabric Tag API (v1) 1.2.0+75110b049c
		fabric-transfer-api-v1: Fabric Transfer API (v1) 6.0.4+074c84ee9c
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.4.0+ac3e15d19c
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.6
		mixinextras: MixinExtras 0.4.1
	Launched Version: Fabric
	Backend library: LWJGL version 3.3.3-snapshot
	Backend API: Intel(R) UHD Graphics GL version 3.2.0 - Build 32.0.101.6874, Intel
	Window size: 854x480
	GFLW Platform: win32
	Render Extensions: GL_ARB_buffer_storage, GL_KHR_debug, GL_ARB_vertex_attrib_binding, GL_ARB_direct_state_access
	GL debug messages: 
	Is Modded: Definitely; Client brand changed to 'fabric'
	Universe: 400921fb54442d18
	Type: Client (map_client.txt)
	Graphics mode: fancy
	Render Distance: 12/12 chunks
	Resource Packs: vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader
	Current Language: es_es
	Locale: es_ES
	System encoding: Cp1252
	File encoding: UTF-8
	CPU: 8x Intel(R) Core(TM) i3-N305