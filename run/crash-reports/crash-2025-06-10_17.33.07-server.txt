---- Minecraft Crash Report ----
// My bad.

Time: 2025-06-10 17:33:07
Description: Exception ticking world

java.lang.RuntimeException: Mixin transformation of net.minecraft.server.network.EntityTrackerEntry failed
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.getPostMixinClassByteArray(KnotClassDelegate.java:427)
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.tryLoadClass(KnotClassDelegate.java:323)
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.loadClass(KnotClassDelegate.java:218)
	at net.fabricmc.loader.impl.launch.knot.KnotClassLoader.loadClass(KnotClassLoader.java:119)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:528)
	at knot//net.minecraft.server.world.ServerChunkLoadingManager$EntityTracker.<init>(ServerChunkLoadingManager.java:1328)
	at knot//net.minecraft.server.world.ServerChunkLoadingManager.loadEntity(ServerChunkLoadingManager.java:1180)
	at knot//net.minecraft.server.world.ServerChunkManager.loadEntity(ServerChunkManager.java:529)
	at knot//net.minecraft.server.world.ServerWorld$ServerEntityHandler.startTracking(ServerWorld.java:1613)
	at knot//net.minecraft.server.world.ServerWorld$ServerEntityHandler.startTracking(ServerWorld.java:1591)
	at knot//net.minecraft.server.world.ServerEntityManager.startTracking(ServerEntityManager.java:220)
	at knot//net.minecraft.server.world.ServerEntityManager.addEntity(ServerEntityManager.java:186)
	at knot//net.minecraft.server.world.ServerEntityManager.method_31857(ServerEntityManager.java:355)
	at java.base/java.util.Collections$2.tryAdvance(Collections.java:5075)
	at java.base/java.util.Collections$2.forEachRemaining(Collections.java:5083)
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:807)
	at knot//net.minecraft.server.world.ServerEntityManager.loadChunks(ServerEntityManager.java:355)
	at knot//net.minecraft.server.world.ServerEntityManager.tick(ServerEntityManager.java:361)
	at knot//net.minecraft.server.world.ServerWorld.tick(ServerWorld.java:412)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1062)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:946)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:706)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:290)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: org.spongepowered.asm.mixin.transformer.throwables.MixinTransformerError: An unexpected critical error was encountered
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:392)
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClass(MixinTransformer.java:234)
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClassBytes(MixinTransformer.java:202)
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.getPostMixinClassByteArray(KnotClassDelegate.java:422)
	... 24 more
Caused by: org.spongepowered.asm.mixin.throwables.MixinApplyError: Mixin [ai_villagers.mixins.json:Global.EntityTrackerEntryMixin from mod ai_villagers] from phase [DEFAULT] in config [ai_villagers.mixins.json] FAILED during APPLY
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.handleMixinError(MixinProcessor.java:638)
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.handleMixinApplyError(MixinProcessor.java:589)
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:379)
	... 27 more
Caused by: org.spongepowered.asm.mixin.injection.throwables.InvalidInjectionException: @ModifyArgs annotation on is targetting a non-method insn in net/minecraft/server/network/EntityTrackerEntry::<init>(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/Entity;IZLjava/util/function/Consumer;Ljava/util/function/BiConsumer;)V in net/minecraft/server/network/EntityTrackerEntry::aiVillagers$modifyVillagerTrackingParameters [INJECT_APPLY Applicator Phase -> ai_villagers.mixins.json:Global.EntityTrackerEntryMixin from mod ai_villagers -> Apply Injections ->  -> Inject -> ai_villagers.mixins.json:Global.EntityTrackerEntryMixin from mod ai_villagers->@ModifyArgs::aiVillagers$modifyVillagerTrackingParameters(Lorg/spongepowered/asm/mixin/injection/invoke/arg/Args;Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/Entity;IZLjava/util/function/Consumer;Ljava/util/function/BiConsumer;)V]
	at org.spongepowered.asm.mixin.injection.invoke.InvokeInjector.inject(InvokeInjector.java:78)
	at org.spongepowered.asm.mixin.injection.invoke.ModifyArgsInjector.inject(ModifyArgsInjector.java:73)
	at org.spongepowered.asm.mixin.injection.code.Injector.inject(Injector.java:284)
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.inject(InjectionInfo.java:508)
	at org.spongepowered.asm.mixin.transformer.MixinTargetContext.applyInjections(MixinTargetContext.java:1483)
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.applyInjections(MixinApplicatorStandard.java:752)
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.applyMixin(MixinApplicatorStandard.java:330)
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.apply(MixinApplicatorStandard.java:246)
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.apply(TargetClassContext.java:437)
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.applyMixins(TargetClassContext.java:418)
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:363)
	... 27 more


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Server thread
Stacktrace:
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.getPostMixinClassByteArray(KnotClassDelegate.java:427)
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.tryLoadClass(KnotClassDelegate.java:323)
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.loadClass(KnotClassDelegate.java:218)
	at net.fabricmc.loader.impl.launch.knot.KnotClassLoader.loadClass(KnotClassLoader.java:119)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:528)
	at knot//net.minecraft.server.world.ServerChunkLoadingManager$EntityTracker.<init>(ServerChunkLoadingManager.java:1328)
	at knot//net.minecraft.server.world.ServerChunkLoadingManager.loadEntity(ServerChunkLoadingManager.java:1180)
	at knot//net.minecraft.server.world.ServerChunkManager.loadEntity(ServerChunkManager.java:529)
	at knot//net.minecraft.server.world.ServerWorld$ServerEntityHandler.startTracking(ServerWorld.java:1613)
	at knot//net.minecraft.server.world.ServerWorld$ServerEntityHandler.startTracking(ServerWorld.java:1591)
	at knot//net.minecraft.server.world.ServerEntityManager.startTracking(ServerEntityManager.java:220)
	at knot//net.minecraft.server.world.ServerEntityManager.addEntity(ServerEntityManager.java:186)
	at knot//net.minecraft.server.world.ServerEntityManager.method_31857(ServerEntityManager.java:355)
	at java.base/java.util.Collections$2.tryAdvance(Collections.java:5075)
	at java.base/java.util.Collections$2.forEachRemaining(Collections.java:5083)
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:807)
	at knot//net.minecraft.server.world.ServerEntityManager.loadChunks(ServerEntityManager.java:355)
	at knot//net.minecraft.server.world.ServerEntityManager.tick(ServerEntityManager.java:361)

-- Affected level --
Details:
	All players: 0 total; 
	Chunk stats: 841
	Level dimension: minecraft:overworld
	Level spawn location: World: (0,-60,0), Section: (at 0,4,0 in 0,-4,0; chunk contains blocks 0,-64,0 to 15,319,15), Region: (0,0; contains chunks 0,0 to 31,31, blocks 0,-64,0 to 511,319,511)
	Level time: 20346 game time, 11332 day time
	Level name: Mundo nuevo
	Level game mode: Game mode: creative (ID 1). Hardcore: false. Commands: true
	Level weather: Rain time: 97891 (now: false), thunder time: 57254 (now: false)
	Known server brands: fabric
	Removed feature flags: 
	Level was modded: true
	Level storage version: 0x04ABD - Anvil
	Loaded entity count: 1
Stacktrace:
	at knot//net.minecraft.server.world.ServerWorld.addDetailsToCrashReport(ServerWorld.java:1734)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1065)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:946)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:706)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:290)
	at java.base/java.lang.Thread.run(Thread.java:1575)

-- System Details --
Details:
	Minecraft Version: 1.21.5
	Minecraft Version ID: 1.21.5
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 212914016 bytes (203 MiB) / 725614592 bytes (692 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6874
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 11494.90
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 3287.53
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 185283.69, total: 389296.00
	Space in storage for workdir (MiB): available: 185283.69, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: AiVillagersFabric 1.0.0
		fabric-api: Fabric API 0.125.3+1.21.5
		fabric-api-base: Fabric API Base 0.4.62+73a52b4b49
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.96+86c3a9f149
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.7+2dd063df49
		fabric-block-api-v1: Fabric Block API (v1) 1.1.0+ed91556f49
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.26+aa6d566c49
		fabric-blockrenderlayer-v1: Fabric BlockRenderLayer Registration (v1) 2.0.16+86c3a9f149
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.0+dac2d6e349
		fabric-client-tags-api-v1: Fabric Client Tags 1.1.37+86c3a9f149
		fabric-command-api-v1: Fabric Command API (v1) 1.2.70+f71b366f49
		fabric-command-api-v2: Fabric Command API (v2) 2.2.49+73a52b4b49
		fabric-commands-v0: Fabric Commands (v0) 0.2.87+df3654b349
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.14+3e6c1f7d49
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.32+7f945d5b49
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.1+0570995b49
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.12+86c3a9f149
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.3+bc514c4549
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 22.4.2+ea72995749
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.17+3e6c1f7d49
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.0+3ce7866349
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.15+64e3057949
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.70+c327076a49
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.3+2a6ec84b49
		fabric-item-api-v1: Fabric Item API (v1) 11.4.1+e46fd76a49
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.9+3459fc6149
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.63+ecf51cdc49
		fabric-keybindings-v0: Fabric Key Bindings (v0) 0.2.61+df3654b349
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.0+230071a049
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.48+3f89f5a549
		fabric-loot-api-v3: Fabric Loot API (v3) 1.0.36+86c3a9f149
		fabric-message-api-v1: Fabric Message API (v1) 6.1.0+fe971bba49
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.0+c982b95149
		fabric-networking-api-v1: Fabric Networking API (v1) 4.5.0+775be32c49
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.1+b8d6ba7049
		fabric-particles-v1: Fabric Particles (v1) 4.1.2+112e550e49
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.8+3235ab3249
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.21+b556383249
		fabric-renderer-api-v1: Fabric Renderer API (v1) 6.0.2+c982b95149
		fabric-renderer-indigo: Fabric Renderer - Indigo 3.0.3+3e6c1f7d49
		fabric-rendering-data-attachment-v1: Fabric Rendering Data Attachment (v1) 0.3.64+73761d2e49
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.27+86c3a9f149
		fabric-rendering-v1: Fabric Rendering (v1) 11.1.11+5490746649
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.21+73a52b4b49
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.7+847e5f5c49
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.46+86c3a9f149
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.128+c327076a49
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.38+86c3a9f149
		fabric-tag-api-v1: Fabric Tag API (v1) 1.0.17+ecf51cdc49
		fabric-transfer-api-v1: Fabric Transfer API (v1) 5.4.24+7b20cbb049
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.3.17+f17a180c49
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.5
		mixinextras: MixinExtras 0.4.1
	Server Running: true
	Player Count: 0 / 8; []
	Active Data Packs: vanilla, fabric, ai_villagers, fabric-convention-tags-v2, fabric-gametest-api-v1
	Available Data Packs: ai_villagers, fabric, fabric-convention-tags-v2, fabric-gametest-api-v1, minecart_improvements, redstone_experiments, trade_rebalance, vanilla
	Enabled Feature Flags: minecraft:vanilla
	World Generation: Stable
	World Seed: 5977307356009849152
	Suppressed Exceptions: ~~NONE~~
	Type: Integrated Server (map_client.txt)
	Is Modded: Definitely; Client brand changed to 'fabric'; Server brand changed to 'fabric'
	Launched Version: Fabric