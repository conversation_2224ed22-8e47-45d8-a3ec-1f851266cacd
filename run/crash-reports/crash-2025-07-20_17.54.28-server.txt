---- Minecraft Crash Report ----
// You're mean.

Time: 2025-07-20 17:54:28
Description: Exception in server tick loop

java.lang.IllegalStateException: Not a string
	at knot//com.mojang.serialization.DataResult$Error.getOrThrow(DataResult.java:287)
	at knot//com.mojang.serialization.DataResult.getOrThrow(DataResult.java:81)
	at knot//net.minecraft.world.PersistentStateManager.encode(PersistentStateManager.java:192)
	at knot//net.minecraft.world.PersistentStateManager.method_67445(PersistentStateManager.java:181)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at knot//net.minecraft.world.PersistentStateManager.method_67444(PersistentStateManager.java:180)
	at java.base/java.util.HashMap.forEach(HashMap.java:1430)
	at knot//net.minecraft.world.PersistentStateManager.collectStatesToSave(PersistentStateManager.java:179)
	at knot//net.minecraft.world.PersistentStateManager.startSaving(PersistentStateManager.java:142)
	at knot//net.minecraft.server.world.ServerWorld.savePersistentState(ServerWorld.java:860)
	at knot//net.minecraft.server.world.ServerWorld.save(ServerWorld.java:837)
	at knot//net.minecraft.server.MinecraftServer.save(MinecraftServer.java:564)
	at knot//net.minecraft.server.MinecraftServer.saveAll(MinecraftServer.java:588)
	at knot//net.minecraft.server.integrated.IntegratedServer.saveAll(IntegratedServer.java:313)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:96)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:707)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:291)
	at java.base/java.lang.Thread.run(Thread.java:1575)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- System Details --
Details:
	Minecraft Version: 1.21.7
	Minecraft Version ID: 1.21.7
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 225933744 bytes (215 MiB) / 743440384 bytes (709 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6913
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 11697.62
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 3256.18
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 182234.28, total: 389296.00
	Space in storage for workdir (MiB): available: 182234.28, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: ai_villagers 1.0.0
		fabric-api: Fabric API 0.129.0+1.21.7
		fabric-api-base: Fabric API Base 0.4.64+9ec45cd86c
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.100+946bf4c36c
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.11+946bf4c36c
		fabric-block-api-v1: Fabric Block API (v1) 1.1.3+946bf4c36c
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.31+946bf4c36c
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.5+8a98c3fc6c
		fabric-command-api-v2: Fabric Command API (v2) 2.2.53+946bf4c36c
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.18+946bf4c36c
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.40+7f945d5b6c
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.5+eb5df52f6c
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.15+946bf4c36c
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.10+946bf4c36c
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 23.2.4+55e55d296c
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.19+946bf4c36c
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.1+c9e472736c
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.23+946bf4c36c
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.73+c64c9c5b6c
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.9+39ce47f56c
		fabric-item-api-v1: Fabric Item API (v1) 11.4.3+946bf4c36c
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.13+946bf4c36c
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.65+946bf4c36c
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.3+db4dfd856c
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.55+3f89f5a56c
		fabric-loot-api-v3: Fabric Loot API (v3) 2.0.2+946bf4c36c
		fabric-message-api-v1: Fabric Message API (v1) 6.1.1+946bf4c36c
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.5+946bf4c36c
		fabric-networking-api-v1: Fabric Networking API (v1) 5.0.1+946bf4c36c
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.7+946bf4c36c
		fabric-particles-v1: Fabric Particles (v1) 4.1.7+946bf4c36c
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.14+946bf4c36c
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.27+946bf4c36c
		fabric-renderer-api-v1: Fabric Renderer API (v1) 7.0.2+946bf4c36c
		fabric-renderer-indigo: Fabric Renderer - Indigo 4.0.2+946bf4c36c
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.30+fa6cb72b6c
		fabric-rendering-v1: Fabric Rendering (v1) 12.4.0+e8d43c766c
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.24+946bf4c36c
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.11+946bf4c36c
		fabric-screen-api-v1: Fabric Screen API (v1) 2.1.0+277ecf7d6c
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.136+946bf4c36c
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.42+946bf4c36c
		fabric-tag-api-v1: Fabric Tag API (v1) 1.2.1+946bf4c36c
		fabric-transfer-api-v1: Fabric Transfer API (v1) 6.0.5+946bf4c36c
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.4.1+ac3e15d16c
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.7
		mixinextras: MixinExtras 0.4.1
	Server Running: true
	Player Count: 1 / 8; [ServerPlayerEntity['Player973'/1, l='ServerLevel[Mundo nuevo]', x=112.71, y=-52.42, z=135.61]]
	Active Data Packs: vanilla, fabric, ai_villagers, fabric-convention-tags-v2, fabric-gametest-api-v1
	Available Data Packs: ai_villagers, fabric, fabric-convention-tags-v2, fabric-gametest-api-v1, minecart_improvements, redstone_experiments, trade_rebalance, vanilla
	Enabled Feature Flags: minecraft:vanilla
	World Generation: Stable
	World Seed: 4323829268185952846
	Suppressed Exceptions: ~~NONE~~
	Type: Integrated Server (map_client.txt)
	Is Modded: Definitely; Client brand changed to 'fabric'; Server brand changed to 'fabric'
	Launched Version: Fabric