---- Minecraft Crash Report ----
// I let you down. Sorry :(

Time: 2025-03-10 19:10:58
Description: Ticking entity

java.lang.NullPointerException: Cannot invoke "java.util.function.Predicate.test(Object)" because "predicate" is null
	at knot//net.minecraft.world.World.method_47576(World.java:711)
	at knot//net.minecraft.world.entity.EntityTrackingSection.forEach(EntityTrackingSection.java:51)
	at knot//net.minecraft.world.entity.SectionedEntityCache.method_31778(SectionedEntityCache.java:127)
	at knot//net.minecraft.world.entity.SectionedEntityCache.forEachInBox(SectionedEntityCache.java:66)
	at knot//net.minecraft.world.entity.SectionedEntityCache.forEachIntersects(SectionedEntityCache.java:127)
	at knot//net.minecraft.world.entity.SimpleEntityLookup.forEachIntersects(SimpleEntityLookup.java:48)
	at knot//net.minecraft.world.World.collectEntitiesByType(World.java:710)
	at knot//net.minecraft.world.World.collectEntitiesByType(World.java:704)
	at knot//net.minecraft.world.World.getEntitiesByType(World.java:699)
	at knot//net.minecraft.world.EntityView.getEntitiesByClass(EntityView.java:24)
	at knot//net.minecraft.entity.passive.VillagerEntity.stopAllChasing(VillagerEntity.java:4374)
	at knot//net.minecraft.entity.passive.VillagerEntity.handleAnimalChasing(VillagerEntity.java:4325)
	at knot//net.minecraft.entity.passive.VillagerEntity.handler$znm000$modid$handleAnimalChasingInjection(VillagerEntity.java:4317)
	at knot//net.minecraft.entity.passive.VillagerEntity.tick(VillagerEntity.java:326)
	at knot//net.minecraft.server.world.ServerWorld.tickEntity(ServerWorld.java:762)
	at knot//net.minecraft.world.World.tickEntity(World.java:502)
	at knot//net.minecraft.server.world.ServerWorld.method_31420(ServerWorld.java:407)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:54)
	at knot//net.minecraft.server.world.ServerWorld.tick(ServerWorld.java:377)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1069)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:953)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:713)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:292)
	at java.base/java.lang.Thread.run(Thread.java:1583)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Server thread
Stacktrace:
	at knot//net.minecraft.world.World.method_47576(World.java:711)
	at knot//net.minecraft.world.entity.EntityTrackingSection.forEach(EntityTrackingSection.java:51)
	at knot//net.minecraft.world.entity.SectionedEntityCache.method_31778(SectionedEntityCache.java:127)
	at knot//net.minecraft.world.entity.SectionedEntityCache.forEachInBox(SectionedEntityCache.java:66)
	at knot//net.minecraft.world.entity.SectionedEntityCache.forEachIntersects(SectionedEntityCache.java:127)
	at knot//net.minecraft.world.entity.SimpleEntityLookup.forEachIntersects(SimpleEntityLookup.java:48)
	at knot//net.minecraft.world.World.collectEntitiesByType(World.java:710)
	at knot//net.minecraft.world.World.collectEntitiesByType(World.java:704)
	at knot//net.minecraft.world.World.getEntitiesByType(World.java:699)
	at knot//net.minecraft.world.EntityView.getEntitiesByClass(EntityView.java:24)
	at knot//net.minecraft.entity.passive.VillagerEntity.stopAllChasing(VillagerEntity.java:4374)
	at knot//net.minecraft.entity.passive.VillagerEntity.handleAnimalChasing(VillagerEntity.java:4325)
	at knot//net.minecraft.entity.passive.VillagerEntity.handler$znm000$modid$handleAnimalChasingInjection(VillagerEntity.java:4317)
	at knot//net.minecraft.entity.passive.VillagerEntity.tick(VillagerEntity.java:326)
	at knot//net.minecraft.server.world.ServerWorld.tickEntity(ServerWorld.java:762)
	at knot//net.minecraft.world.World.tickEntity(World.java:502)
	at knot//net.minecraft.server.world.ServerWorld.method_31420(ServerWorld.java:407)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:54)

-- Entity being ticked --
Details:
	Entity Type: minecraft:villager (net.minecraft.entity.passive.VillagerEntity)
	Entity ID: 12
	Entity Name: Aldeano
	Entity's Exact location: 1083.46, 74.00, 484.09
	Entity's Block location: World: (1083,74,484), Section: (at 11,10,4 in 67,4,30; chunk contains blocks 1072,-64,480 to 1087,319,495), Region: (2,0; contains chunks 64,0 to 95,31, blocks 1024,-64,0 to 1535,319,511)
	Entity's Momentum: 0.00, -0.08, 0.00
	Entity's Passengers: []
	Entity's Vehicle: null
Stacktrace:
	at knot//net.minecraft.world.World.tickEntity(World.java:502)
	at knot//net.minecraft.server.world.ServerWorld.method_31420(ServerWorld.java:407)
	at knot//net.minecraft.world.EntityList.forEach(EntityList.java:54)
	at knot//net.minecraft.server.world.ServerWorld.tick(ServerWorld.java:377)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1069)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:953)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:713)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:292)
	at java.base/java.lang.Thread.run(Thread.java:1583)

-- Affected level --
Details:
	All players: 1 total; [ServerPlayerEntity['Player71'/6, l='ServerLevel[New World]', x=1091.51, y=72.61, z=439.57]]
	Chunk stats: 1888
	Level dimension: minecraft:overworld
	Level spawn location: World: (0,84,0), Section: (at 0,4,0 in 0,5,0; chunk contains blocks 0,-64,0 to 15,319,15), Region: (0,0; contains chunks 0,0 to 31,31, blocks 0,-64,0 to 511,319,511)
	Level time: 49599 game time, 4649 day time
	Level name: New World
	Level game mode: Game mode: creative (ID 1). Hardcore: false. Commands: true
	Level weather: Rain time: 117678 (now: false), thunder time: 41956 (now: false)
	Known server brands: fabric
	Removed feature flags: 
	Level was modded: true
	Level storage version: 0x04ABD - Anvil
	Loaded entity count: 49
Stacktrace:
	at knot//net.minecraft.server.world.ServerWorld.addDetailsToCrashReport(ServerWorld.java:1744)
	at knot//net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1072)
	at knot//net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:953)
	at knot//net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114)
	at knot//net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:713)
	at knot//net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:292)
	at java.base/java.lang.Thread.run(Thread.java:1583)

-- System Details --
Details:
	Minecraft Version: 1.21.4
	Minecraft Version ID: 1.21.4
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 21.0.6, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 174895848 bytes (166 MiB) / 586153984 bytes (559 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 1024.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 31.0.101.4032
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 10736.18
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 2118.85
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 249295.83, total: 389296.00
	Space in storage for workdir (MiB): available: 249295.83, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		fabric-api: Fabric API 0.111.0+1.21.4
		fabric-api-base: Fabric API Base 0.4.53+453d4f9104
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.84+7feeb73304
		fabric-biome-api-v1: Fabric Biome API (v1) 15.0.4+466a140b04
		fabric-block-api-v1: Fabric Block API (v1) 1.0.31+7feeb73304
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.19+7feeb73304
		fabric-blockrenderlayer-v1: Fabric BlockRenderLayer Registration (v1) 2.0.7+7feeb73304
		fabric-client-tags-api-v1: Fabric Client Tags 1.1.27+7feeb73304
		fabric-command-api-v1: Fabric Command API (v1) 1.2.61+f71b366f04
		fabric-command-api-v2: Fabric Command API (v2) 2.2.40+e496eb1504
		fabric-commands-v0: Fabric Commands (v0) 0.2.78+df3654b304
		fabric-content-registries-v0: Fabric Content Registries (v0) 9.1.12+7feeb73304
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.6+7f945d5b04
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.9.6+466a140b04
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.6+7feeb73304
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.4.3+9aea556b04
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 22.1.2+466a140b04
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.10+7feeb73304
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.0.11+60fccc7604
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.1+a4eebcf004
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.62+7feeb73304
		fabric-gametest-api-v1: Fabric Game Test API (v1) 2.0.19+7feeb73304
		fabric-item-api-v1: Fabric Item API (v1) 11.1.12+203e6b2304
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.1.21+7feeb73304
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.56+7feeb73304
		fabric-keybindings-v0: Fabric Key Bindings (v0) 0.2.54+df3654b304
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.5.3+bf2a60eb04
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.31+3f89f5a504
		fabric-loot-api-v3: Fabric Loot API (v3) 1.0.19+203e6b2304
		fabric-message-api-v1: Fabric Message API (v1) 6.0.24+7feeb73304
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 4.1.0+7b6b225304
		fabric-networking-api-v1: Fabric Networking API (v1) 4.3.8+cc0fa2fe04
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 18.0.5+e604fe7f04
		fabric-particles-v1: Fabric Particles (v1) 4.0.13+7feeb73304
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.0.3+fa62a02304
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.0.1+cc0fa2fe04
		fabric-renderer-api-v1: Fabric Renderer API (v1) 5.0.1+84404cdd04
		fabric-renderer-indigo: Fabric Renderer - Indigo 2.0.1+84404cdd04
		fabric-rendering-data-attachment-v1: Fabric Rendering Data Attachment (v1) 0.3.57+73761d2e04
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.18+7feeb73304
		fabric-rendering-v1: Fabric Rendering (v1) 10.1.0+7e7568bf04
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.13+203e6b2304
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.0.10+203e6b2304
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.37+7feeb73304
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.106+7feeb73304
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.32+7feeb73304
		fabric-transfer-api-v1: Fabric Transfer API (v1) 5.4.6+1885ad7404
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.3.2+56e78b9b04
		fabricloader: Fabric Loader 0.16.10
		java: Java HotSpot(TM) 64-Bit Server VM 21
		minecraft: Minecraft 1.21.4
		mixinextras: MixinExtras 0.4.1
		modid: Example mod 1.21.4
	Server Running: true
	Player Count: 1 / 8; [ServerPlayerEntity['Player71'/6, l='ServerLevel[New World]', x=1091.51, y=72.61, z=439.57]]
	Active Data Packs: vanilla, fabric, fabric-convention-tags-v2, fabric-gametest-api-v1
	Available Data Packs: fabric, fabric-convention-tags-v2, fabric-gametest-api-v1, minecart_improvements, redstone_experiments, trade_rebalance, vanilla
	Enabled Feature Flags: minecraft:vanilla
	World Generation: Stable
	World Seed: 2860993946497712108
	Suppressed Exceptions: ~~NONE~~
	Type: Integrated Server (map_client.txt)
	Is Modded: Definitely; Client brand changed to 'fabric'; Server brand changed to 'fabric'
	Launched Version: Fabric