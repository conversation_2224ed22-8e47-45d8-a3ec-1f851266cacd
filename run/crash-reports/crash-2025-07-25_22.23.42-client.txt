---- Minecraft Crash Report ----
// Who set us up the TNT?

Time: 2025-07-25 22:23:42
Description: Extracting render state for an entity in world

java.lang.ClassCastException: class net.minecraft.client.render.entity.state.VillagerEntityRenderState cannot be cast to class AiVillagers.interfaces.VillagerReferenceHolderInterface (net.minecraft.client.render.entity.state.VillagerEntityRenderState and AiVillagers.interfaces.VillagerReferenceHolderInterface are in unnamed module of loader 'knot' @67205a84)
	at knot//net.minecraft.client.render.entity.VillagerEntityRenderer.handler$zmf000$ai_villagers$aiVillagersFabric$injectVillager(VillagerEntityRenderer.java:527)
	at knot//net.minecraft.client.render.entity.VillagerEntityRenderer.updateRenderState(VillagerEntityRenderer.java)
	at knot//net.minecraft.client.render.entity.VillagerEntityRenderer.updateRenderState(VillagerEntityRenderer.java:13)
	at knot//net.minecraft.client.render.entity.EntityRenderer.getAndUpdateRenderState(EntityRenderer.java:234)
	at knot//net.minecraft.client.render.entity.EntityRenderDispatcher.render(EntityRenderDispatcher.java:166)
	at knot//net.minecraft.client.render.entity.EntityRenderDispatcher.render(EntityRenderDispatcher.java:160)
	at knot//net.minecraft.client.render.WorldRenderer.renderEntity(WorldRenderer.java:949)
	at knot//net.minecraft.client.render.WorldRenderer.renderEntities(WorldRenderer.java:823)
	at knot//net.minecraft.client.render.WorldRenderer.method_62214(WorldRenderer.java:614)
	at knot//net.minecraft.client.render.FrameGraphBuilder.run(FrameGraphBuilder.java:67)
	at knot//net.minecraft.client.render.WorldRenderer.render(WorldRenderer.java:544)
	at knot//net.minecraft.client.render.GameRenderer.renderWorld(GameRenderer.java:738)
	at knot//net.minecraft.client.render.GameRenderer.render(GameRenderer.java:506)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1361)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:947)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Render thread
Stacktrace:
	at knot//net.minecraft.client.render.entity.VillagerEntityRenderer.handler$zmf000$ai_villagers$aiVillagersFabric$injectVillager(VillagerEntityRenderer.java:527)
	at knot//net.minecraft.client.render.entity.VillagerEntityRenderer.updateRenderState(VillagerEntityRenderer.java)
	at knot//net.minecraft.client.render.entity.VillagerEntityRenderer.updateRenderState(VillagerEntityRenderer.java:13)

-- Entity being extracted --
Details:
	Entity Type: minecraft:villager (net.minecraft.entity.passive.VillagerEntity)
	Entity ID: 9
	Entity Name: Aldeano
	Entity's Exact location: 6.45, -60.00, -53.25
	Entity's Block location: World: (6,-60,-54), Section: (at 6,4,10 in 0,-4,-4; chunk contains blocks 0,-64,-64 to 15,319,-49), Region: (0,-1; contains chunks 0,-32 to 31,-1, blocks 0,-64,-512 to 511,319,-1)
	Entity's Momentum: 0.00, -0.07, 0.00
	Entity's Passengers: []
	Entity's Vehicle: null
Stacktrace:
	at knot//net.minecraft.client.render.entity.EntityRenderDispatcher.render(EntityRenderDispatcher.java:166)
	at knot//net.minecraft.client.render.entity.EntityRenderDispatcher.render(EntityRenderDispatcher.java:160)
	at knot//net.minecraft.client.render.WorldRenderer.renderEntity(WorldRenderer.java:949)
	at knot//net.minecraft.client.render.WorldRenderer.renderEntities(WorldRenderer.java:823)
	at knot//net.minecraft.client.render.WorldRenderer.method_62214(WorldRenderer.java:614)
	at knot//net.minecraft.client.render.FrameGraphBuilder.run(FrameGraphBuilder.java:67)
	at knot//net.minecraft.client.render.WorldRenderer.render(WorldRenderer.java:544)
	at knot//net.minecraft.client.render.GameRenderer.renderWorld(GameRenderer.java:738)
	at knot//net.minecraft.client.render.GameRenderer.render(GameRenderer.java:506)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1361)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:947)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Renderer details --
Details:
	Assigned renderer: net.minecraft.client.render.entity.VillagerEntityRenderer@6640ae0e
	Location: 1.26,-1.62,-0.44 - World: (1,-2,-1), Section: (at 1,14,15 in 0,-1,-1; chunk contains blocks 0,-64,-16 to 15,319,-1), Region: (0,-1; contains chunks 0,-32 to 31,-1, blocks 0,-64,-512 to 511,319,-1)
	Delta: 0.6200032
Stacktrace:
	at knot//net.minecraft.client.render.entity.EntityRenderDispatcher.addRendererDetails(EntityRenderDispatcher.java:245)
	at knot//net.minecraft.client.render.entity.EntityRenderDispatcher.render(EntityRenderDispatcher.java:172)
	at knot//net.minecraft.client.render.entity.EntityRenderDispatcher.render(EntityRenderDispatcher.java:160)
	at knot//net.minecraft.client.render.WorldRenderer.renderEntity(WorldRenderer.java:949)
	at knot//net.minecraft.client.render.WorldRenderer.renderEntities(WorldRenderer.java:823)
	at knot//net.minecraft.client.render.WorldRenderer.method_62214(WorldRenderer.java:614)
	at knot//net.minecraft.client.render.FrameGraphBuilder.run(FrameGraphBuilder.java:67)
	at knot//net.minecraft.client.render.WorldRenderer.render(WorldRenderer.java:544)
	at knot//net.minecraft.client.render.GameRenderer.renderWorld(GameRenderer.java:738)
	at knot//net.minecraft.client.render.GameRenderer.render(GameRenderer.java:506)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1361)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:947)
	at knot//net.minecraft.client.main.Main.main(Main.java:265)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Uptime --
Details:
	JVM uptime: 44.491s
	Wall uptime: 25.944s
	High-res time: 24.485s
	Client ticks: 254 ticks / 12.700s

-- Affected level --
Details:
	All players: 1 total; ClientPlayerEntity{name=Player900, id=7, pos=(5.191775357552111, -60.0, -52.81257320243174), mode=CREATIVE, permission=4}
	Chunk stats: 361, 116
	Level dimension: minecraft:overworld
	Level spawn location: World: (0,-60,0), Section: (at 0,4,0 in 0,-4,0; chunk contains blocks 0,-64,0 to 15,319,15), Region: (0,0; contains chunks 0,0 to 31,31, blocks 0,-64,0 to 511,319,511)
	Level time: 8011 game time, 8011 day time
	Server brand: fabric
	Server type: Integrated singleplayer server
	Tracked entity count: 23

-- Last reload --
Details:
	Reload number: 1
	Reload reason: initial
	Finished: Yes
	Packs: vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader

-- System Details --
Details:
	Minecraft Version: 1.21.8
	Minecraft Version ID: 1.21.8
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 23.0.2, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 184694680 bytes (176 MiB) / 526385152 bytes (502 MiB) up to 2063597568 bytes (1968 MiB)
	CPUs: 8
	Processor Vendor: GenuineIntel
	Processor Name: Intel(R) Core(TM) i3-N305
	Identifier: Intel64 Family 6 Model 190 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): 1.80
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 8
	Graphics card #0 name: Intel(R) UHD Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6913
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 6.40
	Memory slot #0 type: LPDDR5
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 6.40
	Memory slot #1 type: LPDDR5
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 6.40
	Memory slot #2 type: LPDDR5
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 6.40
	Memory slot #3 type: LPDDR5
	Virtual memory max (MiB): 16057.49
	Virtual memory used (MiB): 12439.95
	Swap memory total (MiB): 8192.00
	Swap memory used (MiB): 4227.77
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 175021.56, total: 389296.00
	Space in storage for workdir (MiB): available: 175021.56, total: 389296.00
	JVM Flags: 0 total; 
	Fabric Mods: 
		ai_villagers: ai_villagers 1.0.0
		fabric-api: Fabric API 0.129.0+1.21.8
		fabric-api-base: Fabric API Base 0.4.64+9ec45cd8f3
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.100+946bf4c3f3
		fabric-biome-api-v1: Fabric Biome API (v1) 16.0.11+946bf4c3f3
		fabric-block-api-v1: Fabric Block API (v1) 1.1.3+946bf4c3f3
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.31+946bf4c3f3
		fabric-client-gametest-api-v1: Fabric Client Game Test API (v1) 4.2.5+8a98c3fcf3
		fabric-command-api-v2: Fabric Command API (v2) 2.2.53+946bf4c3f3
		fabric-content-registries-v0: Fabric Content Registries (v0) 10.0.18+946bf4c3f3
		fabric-convention-tags-v1: Fabric Convention Tags 2.1.40+7f945d5bf3
		fabric-convention-tags-v2: Fabric Convention Tags (v2) 2.15.5+eb5df52ff3
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.3.15+946bf4c3f3
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.8.10+946bf4c3f3
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 23.2.4+55e55d29f3
		fabric-dimensions-v1: Fabric Dimensions API (v1) 4.0.19+946bf4c3f3
		fabric-entity-events-v1: Fabric Entity Events (v1) 2.1.1+c9e47273f3
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 4.0.23+946bf4c3f3
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.73+c64c9c5bf3
		fabric-gametest-api-v1: Fabric Game Test API (v1) 3.1.9+39ce47f5f3
		fabric-item-api-v1: Fabric Item API (v1) 11.4.3+946bf4c3f3
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.2.13+946bf4c3f3
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.65+946bf4c3f3
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.6.3+db4dfd85f3
		fabric-loot-api-v2: Fabric Loot API (v2) 3.0.55+3f89f5a5f3
		fabric-loot-api-v3: Fabric Loot API (v3) 2.0.2+946bf4c3f3
		fabric-message-api-v1: Fabric Message API (v1) 6.1.1+946bf4c3f3
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 5.2.5+946bf4c3f3
		fabric-networking-api-v1: Fabric Networking API (v1) 5.0.1+946bf4c3f3
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 21.1.7+946bf4c3f3
		fabric-particles-v1: Fabric Particles (v1) 4.1.7+946bf4c3f3
		fabric-recipe-api-v1: Fabric Recipe API (v1) 8.1.14+946bf4c3f3
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 6.1.27+946bf4c3f3
		fabric-renderer-api-v1: Fabric Renderer API (v1) 7.0.2+946bf4c3f3
		fabric-renderer-indigo: Fabric Renderer - Indigo 4.0.2+946bf4c3f3
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.1.30+fa6cb72bf3
		fabric-rendering-v1: Fabric Rendering (v1) 12.4.0+e8d43c76f3
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 5.0.24+946bf4c3f3
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 3.1.11+946bf4c3f3
		fabric-screen-api-v1: Fabric Screen API (v1) 2.1.0+277ecf7df3
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.136+946bf4c3f3
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.42+946bf4c3f3
		fabric-tag-api-v1: Fabric Tag API (v1) 1.2.1+946bf4c3f3
		fabric-transfer-api-v1: Fabric Transfer API (v1) 6.0.5+946bf4c3f3
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 6.4.1+ac3e15d1f3
		fabricloader: Fabric Loader 0.16.14
		java: Java HotSpot(TM) 64-Bit Server VM 23
		minecraft: Minecraft 1.21.8
		mixinextras: MixinExtras 0.4.1
	Launched Version: Fabric
	Backend library: LWJGL version 3.3.3-snapshot
	Backend API: Intel(R) UHD Graphics GL version 3.2.0 - Build 32.0.101.6913, Intel
	Window size: 854x480
	GFLW Platform: win32
	Render Extensions: GL_ARB_buffer_storage, GL_KHR_debug, GL_ARB_vertex_attrib_binding, GL_ARB_direct_state_access
	GL debug messages: 
	Is Modded: Definitely; Client brand changed to 'fabric'; Server brand changed to 'fabric'
	Universe: 400921fb54442d18
	Type: Integrated Server (map_client.txt)
	Graphics mode: fancy
	Render Distance: 6/6 chunks
	Resource Packs: vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader
	Current Language: es_es
	Locale: es_ES
	System encoding: Cp1252
	File encoding: UTF-8
	CPU: 8x Intel(R) Core(TM) i3-N305
	Server Running: true
	Player Count: 1 / 8; [ServerPlayerEntity['Player900'/7, l='ServerLevel[Mundo nuevo]', x=5.19, y=-60.00, z=-52.81]]
	Active Data Packs: vanilla, fabric, ai_villagers, fabric-convention-tags-v2, fabric-gametest-api-v1
	Available Data Packs: ai_villagers, fabric, fabric-convention-tags-v2, fabric-gametest-api-v1, minecart_improvements, redstone_experiments, trade_rebalance, vanilla
	Enabled Feature Flags: minecraft:vanilla
	World Generation: Stable
	World Seed: 7083465079513316983
	Suppressed Exceptions: ~~NONE~~