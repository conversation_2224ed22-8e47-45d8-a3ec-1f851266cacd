#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffa14e2bb11, pid=18772, tid=12752
#
# JRE version: Java(TM) SE Runtime Environment (23.0.2+7) (build 23.0.2+7-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (23.0.2+7-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# C  [igxelpicd64.dll+0x1bb11]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://bugreport.java.com/bugreport/crash.jsp
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  S U M M A R Y ------------

Command Line: -Dfabric.dli.config=C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\launch.cfg -Dfabric.dli.env=client -Dfabric.dli.main=net.fabricmc.loader.impl.launch.knot.KnotClient -Dfile.encoding=UTF-8 -Duser.country=ES -Duser.language=es -Duser.variant net.fabricmc.devlaunchinjector.Main

Host: Intel(R) Core(TM) i3-N305, 8 cores, 7G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Sun Jun 22 20:29:47 2025 Hora de verano romance elapsed time: 924.946354 seconds (0d 0h 15m 24s)

---------------  T H R E A D  ---------------

Current thread (0x000002f25030cf40):  JavaThread "Render thread"        [_thread_in_native, id=12752, stack(0x000000fab2c00000,0x000000fab2d00000) (1024K)]

Stack: [0x000000fab2c00000,0x000000fab2d00000],  sp=0x000000fab2cfdba8,  free space=1014k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [igxelpicd64.dll+0x1bb11]  (no source info available)

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 15454  org.lwjgl.opengl.GL45C.nglNamedBufferSubData(IJJJ)V (0 bytes) @ 0x000002f260c2ab76 [0x000002f260c2ab20+0x0000000000000056]
J 28015 c2 net.minecraft.client.gl.GlCommandEncoder.writeToBuffer(Lcom/mojang/blaze3d/buffers/GpuBufferSlice;Ljava/nio/ByteBuffer;)V (158 bytes) @ 0x000002f260aaf700 [0x000002f260aaf640+0x00000000000000c0]
J 16335 c1 net.minecraft.client.gl.GlobalSettings.set(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V (127 bytes) @ 0x000002f259a1680c [0x000002f259a15620+0x00000000000011ec]
j  net.minecraft.client.render.GameRenderer.render(Lnet/minecraft/client/render/RenderTickCounter;Z)V+180
J 30097 c2 net.minecraft.client.MinecraftClient.render(Z)V (1078 bytes) @ 0x000002f26120907c [0x000002f261207420+0x0000000000001c5c]
j  net.minecraft.client.MinecraftClient.disconnect(Lnet/minecraft/client/gui/screen/Screen;Z)V+125
j  net.minecraft.client.MinecraftClient.disconnectWithProgressScreen()V+10
j  net.minecraft.client.MinecraftClient.stop()V+45
j  net.minecraft.client.main.Main.main([Ljava/lang/String;)V+1655
j  java.lang.invoke.LambdaForm$DMH+0x000002f201019800.invokeStaticInit(Ljava/lang/Object;Ljava/lang/Object;)V+10 java.base@23.0.2
j  java.lang.invoke.LambdaForm$MH+0x000002f201002c00.invokeExact_MT(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V+19 java.base@23.0.2
j  net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(Ljava/lang/ClassLoader;)V+74
j  net.fabricmc.loader.impl.launch.knot.Knot.launch([Ljava/lang/String;Lnet/fabricmc/api/EnvType;)V+40
j  net.fabricmc.loader.impl.launch.knot.KnotClient.main([Ljava/lang/String;)V+4
j  java.lang.invoke.LambdaForm$DMH+0x000002f201002400.invokeStatic(Ljava/lang/Object;Ljava/lang/Object;)V+10 java.base@23.0.2
j  java.lang.invoke.LambdaForm$MH+0x000002f201002c00.invokeExact_MT(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V+19 java.base@23.0.2
j  net.fabricmc.devlaunchinjector.Main.main([Ljava/lang/String;)V+307
v  ~StubRoutines::call_stub 0x000002f25ff70fcd

siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), writing address 0x000002f294290000


Registers:
RAX=0x0000000000080000, RBX=0x000002f24c1f3130, RCX=0x000002f24c1f3140, RDX=0x000000fab2cfdbd0
RSP=0x000000fab2cfdba8, RBP=0x000000fab2cfdd30, RSI=0x000002f24c1b8f00, RDI=0x0000000000000000
R8 =0x000002f294290000, R9 =0x0000000000080000, R10=0x000002f24c1f5e60, R11=0x000002f2748534ec
R12=0x000002f24c1f30c0, R13=0x0000000000000001, R14=0x000000fab2cfe400, R15=0x000002f24cc7b000
RIP=0x00007ffa14e2bb11, EFLAGS=0x0000000000010202


Register to memory mapping:

RAX=0x0000000000080000 is an unknown value
RBX=0x000002f24c1f3130 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
RCX=0x000002f24c1f3140 points into unknown readable memory: 0x000002f24c1f30e0 | e0 30 1f 4c f2 02 00 00
RDX=0x000000fab2cfdbd0 is pointing into the stack for thread: 0x000002f25030cf40
RSP=0x000000fab2cfdba8 is pointing into the stack for thread: 0x000002f25030cf40
RBP=0x000000fab2cfdd30 is pointing into the stack for thread: 0x000002f25030cf40
RSI=0x000002f24c1b8f00 points into unknown readable memory: 0x000002f24bd78fc0 | c0 8f d7 4b f2 02 00 00
RDI=0x0 is null
R8 =0x000002f294290000 is an unknown value
R9 =0x0000000000080000 is an unknown value
R10=0x000002f24c1f5e60 points into unknown readable memory: 0x000002f24c1b8f00 | 00 8f 1b 4c f2 02 00 00
R11=0x000002f2748534ec points into unknown readable memory: 04 00 1f 78
R12=0x000002f24c1f30c0 points into unknown readable memory: 0x000002f24c8fa5c0 | c0 a5 8f 4c f2 02 00 00
R13=0x0000000000000001 is an unknown value
R14=0x000000fab2cfe400 is pointing into the stack for thread: 0x000002f25030cf40
R15=0x000002f24cc7b000 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00

Top of Stack: (sp=0x000000fab2cfdba8)
0x000000fab2cfdba8:   00007ffa1510e10c 2010143d00000000
0x000000fab2cfdbb8:   000002f24c1b8f00 0021000078500003
0x000000fab2cfdbc8:   0000000000000000 0000000000000000
0x000000fab2cfdbd8:   000000003f000000 3f00000000000000
0x000000fab2cfdbe8:   0000000000000000 0000000000000000
0x000000fab2cfdbf8:   0000000000000000 467ffc0000000000
0x000000fab2cfdc08:   467ffc0000000000 0000000000000000
0x000000fab2cfdc18:   000002f24cc7b0b0 000000fab2cfdd30
0x000000fab2cfdc28:   00007ffa151046c7 000002f24c1b8f00
0x000000fab2cfdc38:   000002f24c1b8f00 000002f24c1b8f00
0x000000fab2cfdc48:   0000000000000000 000002f24cc7b0b0
0x000000fab2cfdc58:   0000000000000000 000000000000001e
0x000000fab2cfdc68:   0000000000000000 0000000000000000
0x000000fab2cfdc78:   0000000000000000 0000000000000000
0x000000fab2cfdc88:   0000000000000000 0000000000000000
0x000000fab2cfdc98:   0000000000000000 0000000000000000
0x000000fab2cfdca8:   000002f2971ccb40 000000000000001e
0x000000fab2cfdcb8:   0000000000000000 000000fab2cfde20
0x000000fab2cfdcc8:   000002f24cc7b0b0 000002f2481730a0
0x000000fab2cfdcd8:   000002f248b02280 000002f249a09310
0x000000fab2cfdce8:   0000000000000000 0000000000000000
0x000000fab2cfdcf8:   0000000000000000 0000000000000000
0x000000fab2cfdd08:   0000000000000000 000000fab2cfe450
0x000000fab2cfdd18:   0000000000000000 0000000000000000
0x000000fab2cfdd28:   0000000000000000 000000fab2cfe4a0
0x000000fab2cfdd38:   0000000000000000 0000000000000048
0x000000fab2cfdd48:   00007ffa8344f2a4 000002f24dde0000
0x000000fab2cfdd58:   00007ffa00000005 0000000000000048
0x000000fab2cfdd68:   0000000000000000 0000000000000888
0x000000fab2cfdd78:   0000000000000000 0000000000000000
0x000000fab2cfdd88:   0000000000000000 0000000100001000
0x000000fab2cfdd98:   0000000000000000 000000fab2cfe3d0 

Instructions: (pc=0x00007ffa14e2bb11)
0x00007ffa14e2ba11:   83 ec 28 e8 f7 a4 40 00 48 83 c4 28 c3 cc cc 48
0x00007ffa14e2ba21:   83 ec 28 e8 77 71 40 00 48 83 c4 28 c3 cc cc 33
0x00007ffa14e2ba31:   c0 80 79 10 01 48 0f 45 c8 48 8b c1 c3 cc cc 48
0x00007ffa14e2ba41:   83 ec 28 48 81 f9 00 10 00 00 76 0a 48 85 d2 74
0x00007ffa14e2ba51:   05 4d 85 c0 75 05 48 83 c4 28 c3 e8 1f 0d d8 00
0x00007ffa14e2ba61:   eb f4 cc cc cc cc cc cc cc cc cc cc cc cc cc 48
0x00007ffa14e2ba71:   81 f9 00 10 00 00 76 03 b0 01 c3 32 c0 c3 cc 4c
0x00007ffa14e2ba81:   8b 41 18 4d 85 c0 74 0c 3b 51 20 73 07 8b c2 49
0x00007ffa14e2ba91:   8b 04 c0 c3 33 c0 c3 80 79 69 00 0f 85 fe 02 e0
0x00007ffa14e2baa1:   00 e9 09 00 00 00 cc cc cc cc cc cc cc cc cc 48
0x00007ffa14e2bab1:   83 ec 28 80 79 69 00 4c 8b c1 0f 85 2b ff df 00
0x00007ffa14e2bac1:   41 8b 40 28 48 83 c1 10 83 c0 3f 83 e0 c0 41 89
0x00007ffa14e2bad1:   40 28 48 85 d2 74 0e 48 83 c4 28 e9 0f 00 00 00
0x00007ffa14e2bae1:   48 83 c4 28 c3 8d 48 40 41 89 48 28 eb f2 cc 4c
0x00007ffa14e2baf1:   8b 41 08 44 8b 49 18 4d 85 c0 74 37 4d 03 c1 49
0x00007ffa14e2bb01:   81 f8 00 10 00 00 76 27 48 85 d2 74 22 0f 10 02
0x00007ffa14e2bb11:   41 0f 11 00 0f 10 4a 10 41 0f 11 48 10 0f 10 42
0x00007ffa14e2bb21:   20 41 0f 11 40 20 0f 10 4a 30 41 0f 11 48 30 83
0x00007ffa14e2bb31:   41 18 40 41 8b c1 c3 cc cc cc cc cc cc cc cc 44
0x00007ffa14e2bb41:   8b 51 18 45 33 db 44 8b ca 44 8b c2 49 8d 41 ff
0x00007ffa14e2bb51:   49 85 c1 0f 85 a2 fe df 00 41 8d 41 ff 42 8d 14
0x00007ffa14e2bb61:   10 f7 d0 23 c2 41 2b c2 41 03 c2 89 41 18 c3 40
0x00007ffa14e2bb71:   56 48 83 ec 20 80 79 69 00 4c 8b d2 41 8b f0 0f
0x00007ffa14e2bb81:   85 98 fe df 00 48 89 5c 24 30 48 89 7c 24 38 48
0x00007ffa14e2bb91:   8d 79 28 41 83 f9 10 0f 82 aa fe df 00 44 8b 1f
0x00007ffa14e2bba1:   45 33 c0 41 8b d9 41 8b d3 48 8d 43 ff 48 85 c3
0x00007ffa14e2bbb1:   0f 85 79 fe df 00 8d 43 ff 44 8d 04 10 f7 d0 44
0x00007ffa14e2bbc1:   23 c0 44 2b c2 43 8d 1c 03 89 1f 4d 85 d2 74 4c
0x00007ffa14e2bbd1:   48 8b 41 18 48 85 c0 75 12 48 8b 7c 24 38 8b c3
0x00007ffa14e2bbe1:   48 8b 5c 24 30 48 83 c4 20 5e c3 8b cb 4c 8b c6
0x00007ffa14e2bbf1:   48 03 c8 48 81 f9 00 10 00 00 76 0c 85 f6 74 08
0x00007ffa14e2bc01:   49 8b d2 e8 77 0b d8 00 01 37 8b c3 48 8b 7c 24 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x00007ffa1510e10c igxelpicd64.dll
stack at sp + 1 slots: 0x2010143d00000000 is an unknown value
stack at sp + 2 slots: 0x000002f24c1b8f00 points into unknown readable memory: 0x000002f24bd78fc0 | c0 8f d7 4b f2 02 00 00
stack at sp + 3 slots: 0x0021000078500003 is an unknown value
stack at sp + 4 slots: 0x0 is null
stack at sp + 5 slots: 0x0 is null
stack at sp + 6 slots: 0x000000003f000000 is an unknown value
stack at sp + 7 slots: 0x3f00000000000000 is an unknown value

Lock stack of current Java thread (top to bottom):


Compiled method (n/a) 925236 15454     n 0       org.lwjgl.opengl.GL45C::nglNamedBufferSubData (native)
 total in heap  [0x000002f260c2aa08,0x000002f260c2ad40] = 824
 relocation     [0x000002f260c2aae0,0x000002f260c2ab18] = 56
 main code      [0x000002f260c2ab20,0x000002f260c2ad35] = 533
 stub code      [0x000002f260c2ad35,0x000002f260c2ad38] = 3
 oops           [0x000002f260c2ad38,0x000002f260c2ad40] = 8

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000002f26d935120} 'nglNamedBufferSubData' '(IJJJ)V' in 'org/lwjgl/opengl/GL45C'
  # parm0:    rdx       = int
  # parm1:    r8:r8     = long
  # parm2:    r9:r9     = long
  # parm3:    rdi:rdi   = long
  #           [sp+0x80]  (sp of caller)
  0x000002f260c2ab20: 6690 448b | 5208 443b 

  0x000002f260c2ab28: ;   {runtime_call ic_miss_stub}
  0x000002f260c2ab28: 5008 0f85 | b039 39ff 
[Verified Entry Point]
  0x000002f260c2ab30: 8984 2400 | 80ff ff55 | 488b ec48 | 83ec 7090 | 4181 7f20 | 2700 0000 

  0x000002f260c2ab48: ;   {runtime_call StubRoutines (final stubs)}
  0x000002f260c2ab48: 7405 e8f1 | cb37 ff48 | 897c 2428 | 4c89 4c24 | 204d 8bc8 

  0x000002f260c2ab5c: ;   {oop(a 'java/lang/Class'{0x0000000090eb78a8} = 'org/lwjgl/opengl/GL45C')}
  0x000002f260c2ab5c: 4c8b c249 | bea8 78eb | 9000 0000 | 004c 8974 | 2460 4c8d | 7424 6049 | 8bd6 c5f8 

  0x000002f260c2ab78: ;   {internal_word}
  0x000002f260c2ab78: 7749 ba76 | abc2 60f2 | 0200 004d | 8997 a803 | 0000 4989 | a7a0 0300 

  0x000002f260c2ab90: ;   {external_word}
  0x000002f260c2ab90: 0049 ba41 | bd74 17fa | 7f00 0041 | 803a 000f | 844e 0000 | 0052 4150 

  0x000002f260c2aba8: ;   {metadata({method} {0x000002f26d935120} 'nglNamedBufferSubData' '(IJJJ)V' in 'org/lwjgl/opengl/GL45C')}
  0x000002f260c2aba8: 4151 48ba | 1851 936d | f202 0000 | 498b cf48 | 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec 
  0x000002f260c2abc8: ;   {runtime_call}
  0x000002f260c2abc8: 0848 b8f0 | f024 17fa | 7f00 00ff | d048 83c4 | 08e9 0c00 

  0x000002f260c2abdc: ;   {runtime_call}
  0x000002f260c2abdc: 0000 48b8 | f0f0 2417 | fa7f 0000 | ffd0 4883 | c420 4159 | 4158 5a49 | 8d8f c003 | 0000 41c7 
  0x000002f260c2abfc: 8744 0400 | 0004 0000 

  0x000002f260c2ac04: ;   {runtime_call}
  0x000002f260c2ac04: 0048 b8b0 | 2fd3 14fa | 7f00 00ff | d0c5 f877 | 41c7 8744 | 0400 0005 | 0000 00f0 | 8344 24c0 
  0x000002f260c2ac24: 0049 3baf | 4804 0000 | 0f87 0e00 | 0000 4183 | bf40 0400 | 0000 0f84 | 2300 0000 | c5f8 7749 
  0x000002f260c2ac44: 8bcf 4c8b | e448 83ec | 2048 83e4 

  0x000002f260c2ac50: ;   {runtime_call}
  0x000002f260c2ac50: f048 b880 | abee 16fa | 7f00 00ff | d049 8be4 | 4d33 e441 | c787 4404 | 0000 0800 | 0000 4183 
  0x000002f260c2ac70: bfc8 0400 | 0002 0f84 | 9400 0000 

  0x000002f260c2ac7c: ;   {external_word}
  0x000002f260c2ac7c: 49ba 41bd | 7417 fa7f | 0000 4180 | 3a00 0f84 | 4400 0000 

  0x000002f260c2ac90: ;   {metadata({method} {0x000002f26d935120} 'nglNamedBufferSubData' '(IJJJ)V' in 'org/lwjgl/opengl/GL45C')}
  0x000002f260c2ac90: 48ba 1851 | 936d f202 | 0000 498b | cf48 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 

  0x000002f260c2acac: ;   {runtime_call}
  0x000002f260c2acac: 83ec 0848 | b8f0 f024 | 17fa 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 

  0x000002f260c2acc4: ;   {runtime_call}
  0x000002f260c2acc4: 48b8 f0f0 | 2417 fa7f | 0000 ffd0 | 4883 c420 | 49c7 87a0 | 0300 0000 | 0000 0049 | c787 a803 
  0x000002f260c2ace4: 0000 0000 | 0000 c5f8 | 7749 8b8f | 3004 0000 | c781 0001 | 0000 0000 | 0000 c949 | 837f 0800 
  0x000002f260c2ad04: 0f85 0100 

  0x000002f260c2ad08: ;   {runtime_call StubRoutines (initial stubs)}
  0x000002f260c2ad08: 0000 c3e9 | b061 34ff | c5f8 774c | 8be4 4883 | ec20 4883 

  0x000002f260c2ad1c: ;   {runtime_call}
  0x000002f260c2ad1c: e4f0 48b8 | a025 2517 | fa7f 0000 | ffd0 498b | e44d 33e4 | e947 ffff | fff4 f4f4 
[/MachCode]


Compiled method (c2) 925243 28015       4       net.minecraft.client.gl.GlCommandEncoder::writeToBuffer (158 bytes)
 total in heap  [0x000002f260aaf508,0x000002f260aaf940] = 1080
 relocation     [0x000002f260aaf5e0,0x000002f260aaf638] = 88
 main code      [0x000002f260aaf640,0x000002f260aaf868] = 552
 stub code      [0x000002f260aaf868,0x000002f260aaf890] = 40
 oops           [0x000002f260aaf890,0x000002f260aaf898] = 8
 metadata       [0x000002f260aaf898,0x000002f260aaf940] = 168
 immutable data [0x000002f24638d4a0,0x000002f24638d6b0] = 528
 dependencies   [0x000002f24638d4a0,0x000002f24638d4d8] = 56
 nul chk table  [0x000002f24638d4d8,0x000002f24638d508] = 48
 handler table  [0x000002f24638d508,0x000002f24638d520] = 24
 scopes pcs     [0x000002f24638d520,0x000002f24638d600] = 224
 scopes data    [0x000002f24638d600,0x000002f24638d6b0] = 176

[Constant Pool (empty)]

[MachCode]
[Instructions begin]
  0x000002f260aaf640: 6690 448b | 5208 443b 

  0x000002f260aaf648: ;   {runtime_call ic_miss_stub}
  0x000002f260aaf648: 5008 0f85 | 90ee 50ff 
[Verified Entry Point]
  0x000002f260aaf650: 8984 2400 | 80ff ff55 | 4883 ec20 | 4181 7f20 | 2700 0000 | 0f85 f001 | 0000 4d8b | d144 0fb6 
  0x000002f260aaf670: 4a14 4585 | c90f 85c9 | 0000 0045 | 8b48 1441 | 0fb6 6918 | 85ed 0f85 | d000 0000 | 418b 690c 
  0x000002f260aaf690: 83e5 080f | 84d7 0000 | 0049 8bda 

  0x000002f260aaf69c: ; implicit exception: dispatches to 0x000002f260aaf800
  0x000002f260aaf69c: 458b 521c | 8b73 1844 | 2bd6 4585 | d27e 7041 | 8bc2 418b | 4810 3bc1 | 0f8f ca00 | 0000 458b 
  0x000002f260aaf6bc: 5910 418b | 780c 03cf | 413b cb0f | 8fd7 0000 | 0044 8b42 | 1841 8b68 | 2041 8b49 | 1444 8b5d 
  0x000002f260aaf6dc: ;   {metadata('net/minecraft/client/gl/BufferManager$ARBBufferManager')}
  0x000002f260aaf6dc: 0841 81fb | 9874 e601 | 753e 4585 | d27e 344d | 63ca 4c63 | c748 63fe | 4803 7b10 

  0x000002f260aaf6f8: ;   {static_call}
  0x000002f260aaf6f8: 8bd1 90e8 

  0x000002f260aaf6fc: ; ImmutableOopMap {}
                      ;*invokestatic nglNamedBufferSubData {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.lwjgl.opengl.GL45C::glNamedBufferSubData@11 (line 767)
                      ; - org.lwjgl.opengl.ARBDirectStateAccess::glNamedBufferSubData@3 (line 611)
                      ; - net.minecraft.client.gl.BufferManager$ARBBufferManager::setBufferSubData@4 (line 65)
                      ; - net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@154 (line 270)
  0x000002f260aaf6fc: 30b4 1700 

  0x000002f260aaf700: ;   {other}
  0x000002f260aaf700: 0f1f 8400 | f801 0000 | 4883 c420 

  0x000002f260aaf70c: ;   {poll_return}
  0x000002f260aaf70c: 5d49 3ba7 | 4804 0000 | 0f87 2a01 | 0000 c333 | c0eb 8f45 | 33d2 ebc7 | ba37 ffff | ff48 891c 
  0x000002f260aaf72c: 2489 4c24 | 0889 7c24 

  0x000002f260aaf734: ;   {runtime_call UncommonTrapBlob}
  0x000002f260aaf734: 0c66 90e8 

  0x000002f260aaf738: ; ImmutableOopMap {rbp=NarrowOop [0]=Oop }
                      ;*invokevirtual setBufferSubData {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@154 (line 270)
  0x000002f260aaf738: 244c 51ff 

  0x000002f260aaf73c: ;   {other}
  0x000002f260aaf73c: 0f1f 8400 | 3402 0001 | ba45 ffff | ff41 8be9 

  0x000002f260aaf74c: ;   {runtime_call UncommonTrapBlob}
  0x000002f260aaf74c: 6666 90e8 

  0x000002f260aaf750: ; ImmutableOopMap {}
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@4 (line 250)
  0x000002f260aaf750: 0c4c 51ff 

  0x000002f260aaf754: ;   {other}
  0x000002f260aaf754: 0f1f 8400 | 4c02 0002 | ba45 ffff 

  0x000002f260aaf760: ;   {runtime_call UncommonTrapBlob}
  0x000002f260aaf760: ff66 90e8 

  0x000002f260aaf764: ; ImmutableOopMap {}
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@30 (line 255)
  0x000002f260aaf764: f84b 51ff 

  0x000002f260aaf768: ;   {other}
  0x000002f260aaf768: 0f1f 8400 | 6002 0003 | ba45 ffff 

  0x000002f260aaf774: ;   {runtime_call UncommonTrapBlob}
  0x000002f260aaf774: ff66 90e8 

  0x000002f260aaf778: ; ImmutableOopMap {}
                      ;*ifne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@51 (line 258)
  0x000002f260aaf778: e44b 51ff 

  0x000002f260aaf77c: ;   {other}
  0x000002f260aaf77c: 0f1f 8400 | 7402 0004 | ba45 ffff | ff49 8be8 | 8944 2404 | 894c 2408 

  0x000002f260aaf794: ;   {runtime_call UncommonTrapBlob}
  0x000002f260aaf794: 6666 90e8 

  0x000002f260aaf798: ; ImmutableOopMap {rbp=Oop }
                      ;*if_icmple {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@77 (line 263)
  0x000002f260aaf798: c44b 51ff 

  0x000002f260aaf79c: ;   {other}
  0x000002f260aaf79c: 0f1f 8400 | 9402 0005 | ba45 ffff | ff49 8be8 | 4489 0c24 | 8944 2404 | 894c 2408 | 4489 5c24 
  0x000002f260aaf7bc: ;   {runtime_call UncommonTrapBlob}
  0x000002f260aaf7bc: 0c66 90e8 

  0x000002f260aaf7c0: ; ImmutableOopMap {rbp=Oop [0]=NarrowOop }
                      ;*if_icmple {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@112 (line 266)
  0x000002f260aaf7c0: 9c4b 51ff 

  0x000002f260aaf7c4: ;   {other}
  0x000002f260aaf7c4: 0f1f 8400 | bc02 0006 | 488b d048 | 83c4 205d 

  0x000002f260aaf7d4: ;   {runtime_call _rethrow_Java}
  0x000002f260aaf7d4: e987 465e | ffba f6ff 

  0x000002f260aaf7dc: ;   {runtime_call UncommonTrapBlob}
  0x000002f260aaf7dc: ffff 90e8 

  0x000002f260aaf7e0: ; ImmutableOopMap {}
                      ;*invokevirtual buffer {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@19 (line 254)
  0x000002f260aaf7e0: 7c4b 51ff 

  0x000002f260aaf7e4: ;   {other}
  0x000002f260aaf7e4: 0f1f 8400 | dc02 0007 | ba5e ffff 

  0x000002f260aaf7f0: ;   {runtime_call UncommonTrapBlob}
  0x000002f260aaf7f0: ff66 90e8 

  0x000002f260aaf7f4: ; ImmutableOopMap {}
                      ;*getfield closed {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@27 (line 255)
  0x000002f260aaf7f4: 684b 51ff 

  0x000002f260aaf7f8: ;   {other}
  0x000002f260aaf7f8: 0f1f 8400 | f002 0008 | baf6 ffff 

  0x000002f260aaf804: ;   {runtime_call UncommonTrapBlob}
  0x000002f260aaf804: ff66 90e8 

  0x000002f260aaf808: ; ImmutableOopMap {}
                      ;*invokevirtual remaining {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@66 (line 262)
  0x000002f260aaf808: 544b 51ff 

  0x000002f260aaf80c: ;   {other}
  0x000002f260aaf80c: 0f1f 8400 | 0403 0009 | baf6 ffff 

  0x000002f260aaf818: ;   {runtime_call UncommonTrapBlob}
  0x000002f260aaf818: ff66 90e8 

  0x000002f260aaf81c: ; ImmutableOopMap {}
                      ;*invokevirtual getBufferManager {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@142 (line 270)
  0x000002f260aaf81c: 404b 51ff 

  0x000002f260aaf820: ;   {other}
  0x000002f260aaf820: 0f1f 8400 | 1803 000a | baf6 ffff | ff48 8beb | 890c 2489 

  0x000002f260aaf834: ;   {runtime_call UncommonTrapBlob}
  0x000002f260aaf834: 7c24 04e8 

  0x000002f260aaf838: ; ImmutableOopMap {rbp=Oop }
                      ;*invokevirtual setBufferSubData {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlCommandEncoder::writeToBuffer@154 (line 270)
  0x000002f260aaf838: 244b 51ff 

  0x000002f260aaf83c: ;   {other}
  0x000002f260aaf83c: 0f1f 8400 | 3403 000b 

  0x000002f260aaf844: ;   {internal_word}
  0x000002f260aaf844: 49ba 0df7 | aa60 f202 | 0000 4d89 | 9760 0400 

  0x000002f260aaf854: ;   {runtime_call SafepointBlob}
  0x000002f260aaf854: 00e9 865b 

  0x000002f260aaf858: ;   {runtime_call StubRoutines (final stubs)}
  0x000002f260aaf858: 51ff e8e1 | 7e4f ffe9 | 06fe ffff | f4f4 f4f4 
[Stub Code]
  0x000002f260aaf868: ;   {no_reloc}
  0x000002f260aaf868: 48bb 0000 | 0000 0000 

  0x000002f260aaf870: ;   {runtime_call nmethod}
  0x000002f260aaf870: 0000 e9fb 

  0x000002f260aaf874: ;   {runtime_call ExceptionBlob}
  0x000002f260aaf874: ffff ffe9 | 64d0 5dff 
[Deopt Handler Code]
  0x000002f260aaf87c: e800 0000 | 0048 832c 

  0x000002f260aaf884: ;   {runtime_call DeoptimizationBlob}
  0x000002f260aaf884: 2405 e9f5 | 4d51 fff4 | f4f4 f4f4 
[/MachCode]


Compiled method (c1) 925251 16335   !   3       net.minecraft.client.gl.GlobalSettings::set (127 bytes)
 total in heap  [0x000002f259a15288,0x000002f259a17390] = 8456
 relocation     [0x000002f259a15360,0x000002f259a15600] = 672
 constants      [0x000002f259a15600,0x000002f259a15620] = 32
 main code      [0x000002f259a15620,0x000002f259a170d8] = 6840
 stub code      [0x000002f259a170d8,0x000002f259a17258] = 384
 oops           [0x000002f259a17258,0x000002f259a17270] = 24
 metadata       [0x000002f259a17270,0x000002f259a17390] = 288
 immutable data [0x000002f24ae7f170,0x000002f24ae7ff18] = 3496
 dependencies   [0x000002f24ae7f170,0x000002f24ae7f1d0] = 96
 nul chk table  [0x000002f24ae7f1d0,0x000002f24ae7f258] = 136
 handler table  [0x000002f24ae7f258,0x000002f24ae7f630] = 984
 scopes pcs     [0x000002f24ae7f630,0x000002f24ae7fa60] = 1072
 scopes data    [0x000002f24ae7fa60,0x000002f24ae7ff18] = 1208


[Constant Pool]
             Address          hex4                    hex8      
  0x000002f259a15600:   0x3f800000      0x46bb80003f800000      
  0x000002f259a15604:   0x46bb8000                              
  0x000002f259a15608:   0xf4f4f4f4      0xf4f4f4f4f4f4f4f4      
  0x000002f259a1560c:   0xf4f4f4f4                              
  0x000002f259a15610:   0xf4f4f4f4      0xf4f4f4f4f4f4f4f4      
  0x000002f259a15614:   0xf4f4f4f4                              
  0x000002f259a15618:   0xf4f4f4f4      0xf4f4f4f4f4f4f4f4      
  0x000002f259a1561c:   0xf4f4f4f4                              

[MachCode]
[Instructions begin]
  0x000002f259a15620: ;   {no_reloc}
  0x000002f259a15620: 6666 660f | 1f84 0000 | 0000 0066 | 6666 9066 | 6690 448b | 5208 443b 

  0x000002f259a15638: ;   {runtime_call ic_miss_stub}
  0x000002f259a15638: 5008 0f85 | a08e 5a06 
[Verified Entry Point]
  0x000002f259a15640: 8984 2400 | 80ff ff55 | 4881 ec00 | 0100 0090 | 4181 7f20 | 2700 0000 

  0x000002f259a15658: ;   {runtime_call StubRoutines (final stubs)}
  0x000002f259a15658: 7405 e8e1 | 2059 0648 | 8994 24a0 | 0000 0044 | 8984 2480 | 0000 0044 | 898c 2484 | 0000 00c5 
  0x000002f259a15678: fb11 8424 | d800 0000 | 4889 bc24 | 8800 0000 | 4889 b424 | 9000 0000 | 898c 2498 

  0x000002f259a15694: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a15694: 0000 0048 | bb40 4c58 | 6ef2 0200 | 008b 83cc | 0000 0083 | c002 8983 | cc00 0000 | 25fe 0700 
  0x000002f259a156b4: 0085 c00f | 8474 1600 

  0x000002f259a156bc: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a156bc: 0048 bb40 | 4c58 6ef2 | 0200 0048 | 8383 1801 

  0x000002f259a156cc: ;   {metadata(method data for {method} {0x000002f26d5d6480} 'stackPush' '()Lorg/lwjgl/system/MemoryStack;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a156cc: 0000 0148 | bb90 9d81 | 6df2 0200 | 008b 83cc | 0000 0083 | c002 8983 | cc00 0000 | 25fe ff1f 
  0x000002f259a156ec: 0085 c00f | 845d 1600 

  0x000002f259a156f4: ;   {metadata(method data for {method} {0x000002f26d5d6480} 'stackPush' '()Lorg/lwjgl/system/MemoryStack;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a156f4: 0048 bb90 | 9d81 6df2 | 0200 0048 | 8383 1801 

  0x000002f259a15704: ;   {metadata(method data for {method} {0x000002f26d5d63e0} 'stackGet' '()Lorg/lwjgl/system/MemoryStack;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a15704: 0000 0148 | bb58 fd82 | 6df2 0200 | 008b 83cc | 0000 0083 | c002 8983 | cc00 0000 | 25fe ff1f 
  0x000002f259a15724: 0085 c00f | 8446 1600 

  0x000002f259a1572c: ;   {oop(a 'java/lang/ThreadLocal$SuppliedThreadLocal'{0x0000000092ba0c40})}
  0x000002f259a1572c: 0048 bb40 | 0cba 9200 | 0000 0048 

  0x000002f259a15738: ;   {metadata(method data for {method} {0x000002f26d5d63e0} 'stackGet' '()Lorg/lwjgl/system/MemoryStack;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a15738: 8bc3 49bb | 58fd 826d | f202 0000 | 8b40 0849 | ba00 0000 | 00f2 0200 | 0049 03c2 | 493b 8320 
  0x000002f259a15758: 0100 0075 | 0d49 8383 | 2801 0000 | 01e9 6000 | 0000 493b | 8330 0100 | 0075 0d49 | 8383 3801 
  0x000002f259a15778: 0000 01e9 | 4a00 0000 | 4983 bb20 | 0100 0000 | 7517 4989 | 8320 0100 | 0049 c783 | 2801 0000 
  0x000002f259a15798: 0100 0000 | e929 0000 | 0049 83bb | 3001 0000 | 0075 1749 | 8983 3001 | 0000 49c7 | 8338 0100 
  0x000002f259a157b8: 0001 0000 | 00e9 0800 | 0000 4983 | 8318 0100 

  0x000002f259a157c8: ;   {metadata(method data for {method} {0x000002f200238930} 'get' '()Ljava/lang/Object;' in 'java/lang/ThreadLocal')}
  0x000002f259a157c8: 0001 48b8 | 185c b541 | f202 0000 | 448b 98cc | 0000 0041 | 83c3 0244 | 8998 cc00 | 0000 4181 
  0x000002f259a157e8: e3fe ff1f | 0045 85db | 0f84 9e15 

  0x000002f259a157f4: ;   {metadata(method data for {method} {0x000002f200238930} 'get' '()Ljava/lang/Object;' in 'java/lang/ThreadLocal')}
  0x000002f259a157f4: 0000 48b8 | 185c b541 | f202 0000 | 4883 8018 | 0100 0001 

  0x000002f259a15808: ;   {metadata(method data for {method} {0x000002f200238930} 'get' '()Ljava/lang/Object;' in 'java/lang/ThreadLocal')}
  0x000002f259a15808: 488b c349 | bb18 5cb5 | 41f2 0200 | 0049 8383 | 2801 0000 | 0149 8b87 | 8803 0000 | 488b 004c 
  0x000002f259a15828: 8bc0 488b 

  0x000002f259a1582c: ;   {optimized virtual_call}
  0x000002f259a1582c: d366 90e8 

  0x000002f259a15830: ; ImmutableOopMap {[144]=Oop [160]=Oop }
                      ;*invokevirtual get {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ThreadLocal::get@4 (line 172)
                      ; - org.lwjgl.system.MemoryStack::stackGet@3 (line 906)
                      ; - org.lwjgl.system.MemoryStack::stackPush@0 (line 915)
                      ; - net.minecraft.client.gl.GlobalSettings::set@0 (line 27)
  0x000002f259a15830: 7c16 e206 

  0x000002f259a15834: ;   {other}
  0x000002f259a15834: 0f1f 8400 | ac05 0000 

  0x000002f259a1583c: ;   {metadata(method data for {method} {0x000002f26d5d63e0} 'stackGet' '()Lorg/lwjgl/system/MemoryStack;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a1583c: 4885 c048 | bf58 fd82 | 6df2 0200 | 0075 0c80 | 8f41 0100 | 0001 e9af | 0000 008b | 5808 49ba 
  0x000002f259a1585c: 0000 0000 | f202 0000 | 4903 da48 | 3b9f 5001 | 0000 750d | 4883 8758 | 0100 0001 | e960 0000 
  0x000002f259a1587c: 0048 3b9f | 6001 0000 | 750d 4883 | 8768 0100 | 0001 e94a | 0000 0048 | 83bf 5001 | 0000 0075 
  0x000002f259a1589c: 1748 899f | 5001 0000 | 48c7 8758 | 0100 0001 | 0000 00e9 | 2900 0000 | 4883 bf60 | 0100 0000 
  0x000002f259a158bc: 7517 4889 | 9f60 0100 | 0048 c787 | 6801 0000 | 0100 0000 | e908 0000 | 0048 8387 | 4801 0000 
  0x000002f259a158dc: ;   {metadata('org/lwjgl/system/MemoryStack')}
  0x000002f259a158dc: 0148 bb40 | 17e1 01f2 | 0200 008b | 7808 49ba | 0000 0000 | f202 0000 | 4903 fa48 | 3b5f 400f 
  0x000002f259a158fc: 85b4 1400 | 00e9 0000 | 0000 488b | d048 3b02 

  0x000002f259a1590c: ;   {metadata(method data for {method} {0x000002f26d5d6480} 'stackPush' '()Lorg/lwjgl/system/MemoryStack;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a1590c: 488b f248 | bf90 9d81 | 6df2 0200 | 008b 7608 | 49ba 0000 | 0000 f202 | 0000 4903 | f248 3bb7 
  0x000002f259a1592c: 3001 0000 | 750d 4883 | 8738 0100 | 0001 e960 | 0000 0048 | 3bb7 4001 | 0000 750d | 4883 8748 
  0x000002f259a1594c: 0100 0001 | e94a 0000 | 0048 83bf | 3001 0000 | 0075 1748 | 89b7 3001 | 0000 48c7 | 8738 0100 
  0x000002f259a1596c: 0001 0000 | 00e9 2900 | 0000 4883 | bf40 0100 | 0000 7517 | 4889 b740 | 0100 0048 | c787 4801 
  0x000002f259a1598c: 0000 0100 | 0000 e908 | 0000 0048 | 8387 2801 

  0x000002f259a1599c: ;   {optimized virtual_call}
  0x000002f259a1599c: 0000 01e8 

  0x000002f259a159a0: ; ImmutableOopMap {[144]=Oop [160]=Oop }
                      ;*invokevirtual push {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.lwjgl.system.MemoryStack::stackPush@3 (line 915)
                      ; - net.minecraft.client.gl.GlobalSettings::set@0 (line 27)
  0x000002f259a159a0: eca1 2807 

  0x000002f259a159a4: ;   {other}
  0x000002f259a159a4: 0f1f 8400 | 1c07 0001 | 4889 8424 | b000 0000 

  0x000002f259a159b4: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a159b4: 48ba 404c | 586e f202 | 0000 4883 | 8228 0100 

  0x000002f259a159c4: ;   {metadata(method data for {method} {0x000002f26dbbc7e8} 'onStack' '(Lorg/lwjgl/system/MemoryStack;I)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a159c4: 0001 48ba | 589f 556e | f202 0000 | 8b8a cc00 | 0000 83c1 | 0289 8acc | 0000 0081 | e1fe ff1f 
  0x000002f259a159e4: 0085 c90f | 84d6 1300 

  0x000002f259a159ec: ;   {metadata('com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a159ec: 0048 ba70 | 0dee 01f2 | 0200 0048 | 8bd8 498b | 87b8 0100 | 0048 8d78 | 1849 3bbf | c801 0000 
  0x000002f259a15a0c: 0f87 d213 | 0000 4989 | bfb8 0100 | 0048 c700 | 0100 0000 | 488b ca49 | ba00 0000 | 00f2 0200 
  0x000002f259a15a2c: 0049 2bca | 8948 0848 | 33c9 8948 | 0c48 33c9 | 4889 4810 | 4889 8424 | a800 0000 

  0x000002f259a15a48: ; implicit exception: dispatches to 0x000002f259a16df1
  0x000002f259a15a48: 483b 0348 

  0x000002f259a15a4c: ;   {metadata(method data for {method} {0x000002f26dbbc7e8} 'onStack' '(Lorg/lwjgl/system/MemoryStack;I)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a15a4c: 8bd3 48be | 589f 556e | f202 0000 | 8b52 0849 | ba00 0000 | 00f2 0200 | 0049 03d2 | 483b 9620 
  0x000002f259a15a6c: 0100 0075 | 0d48 8386 | 2801 0000 | 01e9 6000 | 0000 483b | 9630 0100 | 0075 0d48 | 8386 3801 
  0x000002f259a15a8c: 0000 01e9 | 4a00 0000 | 4883 be20 | 0100 0000 | 7517 4889 | 9620 0100 | 0048 c786 | 2801 0000 
  0x000002f259a15aac: 0100 0000 | e929 0000 | 0048 83be | 3001 0000 | 0075 1748 | 8996 3001 | 0000 48c7 | 8638 0100 
  0x000002f259a15acc: 0001 0000 | 00e9 0800 | 0000 4883 | 8618 0100 

  0x000002f259a15adc: ;   {metadata(method data for {method} {0x000002f26d5d11e8} 'malloc' '(I)Ljava/nio/ByteBuffer;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a15adc: 0001 48ba | 005e 2e6e | f202 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002f259a15afc: 0085 f60f | 84f1 1200 | 0048 8bd3 

  0x000002f259a15b08: ;   {metadata(method data for {method} {0x000002f26d5d11e8} 'malloc' '(I)Ljava/nio/ByteBuffer;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a15b08: 48be 005e | 2e6e f202 | 0000 8b52 | 0849 ba00 | 0000 00f2 | 0200 0049 | 03d2 483b | 9620 0100 
  0x000002f259a15b28: 0075 0d48 | 8386 2801 | 0000 01e9 | 6000 0000 | 483b 9630 | 0100 0075 | 0d48 8386 | 3801 0000 
  0x000002f259a15b48: 01e9 4a00 | 0000 4883 | be20 0100 | 0000 7517 | 4889 9620 | 0100 0048 | c786 2801 | 0000 0100 
  0x000002f259a15b68: 0000 e929 | 0000 0048 | 83be 3001 | 0000 0075 | 1748 8996 | 3001 0000 | 48c7 8638 | 0100 0001 
  0x000002f259a15b88: 0000 00e9 | 0800 0000 | 4883 8618 | 0100 0001 | 41b8 0800 | 0000 41b9 | 1400 0000 

  0x000002f259a15ba4: ;   {optimized virtual_call}
  0x000002f259a15ba4: 488b d3e8 

  0x000002f259a15ba8: ; ImmutableOopMap {[144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual nmalloc {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.lwjgl.system.MemoryStack::malloc@8 (line 377)
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@6 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
  0x000002f259a15ba8: e49f 1e07 

  0x000002f259a15bac: ;   {other}
  0x000002f259a15bac: 0f1f 8400 | 2409 0002 

  0x000002f259a15bb4: ;   {metadata(method data for {method} {0x000002f26d5d11e8} 'malloc' '(I)Ljava/nio/ByteBuffer;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a15bb4: 48ba 005e | 2e6e f202 | 0000 4883 | 8248 0100 

  0x000002f259a15bc4: ;   {oop(a 'java/lang/Class'{0x0000000085caead0} = 'java/nio/DirectByteBuffer')}
  0x000002f259a15bc4: 0001 48ba | d0ea ca85 | 0000 0000 | 4c8b c041 | b914 0000 | 0066 0f1f 

  0x000002f259a15bdc: ;   {static_call}
  0x000002f259a15bdc: 4400 00e8 

  0x000002f259a15be0: ; ImmutableOopMap {[144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokestatic wrap {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.lwjgl.system.MemoryStack::malloc@12 (line 377)
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@6 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
  0x000002f259a15be0: 3cd9 0407 

  0x000002f259a15be4: ;   {other}
  0x000002f259a15be4: 0f1f 8400 | 5c09 0003 

  0x000002f259a15bec: ;   {metadata(method data for {method} {0x000002f26d5d11e8} 'malloc' '(I)Ljava/nio/ByteBuffer;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a15bec: 4885 c048 | be00 5e2e | 6ef2 0200 | 0075 0c80 | 8e51 0100 | 0001 e9af | 0000 008b | 7808 49ba 
  0x000002f259a15c0c: 0000 0000 | f202 0000 | 4903 fa48 | 3bbe 6001 | 0000 750d | 4883 8668 | 0100 0001 | e960 0000 
  0x000002f259a15c2c: 0048 3bbe | 7001 0000 | 750d 4883 | 8678 0100 | 0001 e94a | 0000 0048 | 83be 6001 | 0000 0075 
  0x000002f259a15c4c: 1748 89be | 6001 0000 | 48c7 8668 | 0100 0001 | 0000 00e9 | 2900 0000 | 4883 be70 | 0100 0000 
  0x000002f259a15c6c: 7517 4889 | be70 0100 | 0048 c786 | 7801 0000 | 0100 0000 | e908 0000 | 0048 8386 | 5801 0000 
  0x000002f259a15c8c: ;   {metadata('java/nio/ByteBuffer')}
  0x000002f259a15c8c: 0148 bf70 | 181f 00f2 | 0200 008b | 7008 49ba | 0000 0000 | f202 0000 | 4903 f248 | 3b7e 400f 
  0x000002f259a15cac: 8566 1100 | 00e9 0000 | 0000 488b | d048 3b02 

  0x000002f259a15cbc: ;   {metadata(method data for {method} {0x000002f26d5d11e8} 'malloc' '(I)Ljava/nio/ByteBuffer;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a15cbc: 4c8b c248 | be00 5e2e | 6ef2 0200 | 0048 8386 | 8801 0000 

  0x000002f259a15cd0: ;   {oop(a 'java/nio/ByteOrder'{0x0000000085cb0068})}
  0x000002f259a15cd0: 0149 b868 | 00cb 8500 | 0000 000f 

  0x000002f259a15cdc: ;   {optimized virtual_call}
  0x000002f259a15cdc: 1f40 00e8 

  0x000002f259a15ce0: ; ImmutableOopMap {[144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual order {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.lwjgl.system.MemoryStack::malloc@21 (line 377)
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@6 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
  0x000002f259a15ce0: acbc 1a07 

  0x000002f259a15ce4: ;   {other}
  0x000002f259a15ce4: 0f1f 8400 | 5c0a 0004 | 4c8b 8424 | a800 0000 

  0x000002f259a15cf4: ;   {metadata(method data for {method} {0x000002f26dbbc7e8} 'onStack' '(Lorg/lwjgl/system/MemoryStack;I)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a15cf4: 48ba 589f | 556e f202 | 0000 4883 | 8248 0100 

  0x000002f259a15d04: ;   {metadata(method data for {method} {0x000002f26dbbc668} '<init>' '(Ljava/nio/ByteBuffer;)V' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a15d04: 0001 49b8 | d07d 526e | f202 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002f259a15d24: ff1f 0085 | d20f 84f6 | 1000 004c | 8b84 24a8 

  0x000002f259a15d34: ;   {metadata(method data for {method} {0x000002f26dbbc668} '<init>' '(Ljava/nio/ByteBuffer;)V' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a15d34: 0000 0048 | bad0 7d52 | 6ef2 0200 | 0048 8382 | 1801 0000 

  0x000002f259a15d48: ;   {metadata(method data for {method} {0x000002f20049d358} '<init>' '()V' in 'java/lang/Object')}
  0x000002f259a15d48: 0149 b890 | 6800 41f2 | 0200 0041 | 8b90 cc00 | 0000 83c2 | 0241 8990 | cc00 0000 | 81e2 feff 
  0x000002f259a15d68: 1f00 85d2 | 0f84 d410 | 0000 450f | be47 3845 | 85c0 488b | b424 a800 | 0000 0f85 | df10 0000 
  0x000002f259a15d88: 4c8b d044 | 8956 104c | 8bc6 4c33 | c049 c1e8 | 1449 83f8 | 000f 85e0 | 1000 0048 | 3b00 4c8b 
  0x000002f259a15da8: ;   {metadata(method data for {method} {0x000002f26dbbc668} '<init>' '(Ljava/nio/ByteBuffer;)V' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a15da8: c048 bad0 | 7d52 6ef2 | 0200 0048 | 8382 2801 | 0000 0144 | 8b40 1844 | 8946 0c48 

  0x000002f259a15dc4: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a15dc4: 8bc6 49b8 | 404c 586e | f202 0000 | 4983 8048 | 0100 0001 | 448b 8424 | 8000 0000 | c4c1 7a2a 
  0x000002f259a15de4: c044 8b8c | 2484 0000 | 00c4 c172 

  0x000002f259a15df0: ;   {metadata(method data for {method} {0x000002f26dbbcbf8} 'putVec2' '(FF)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a15df0: 2ac9 49b8 | d850 586e | f202 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002f259a15e10: ff1f 0085 | d20f 8485 | 1000 0048 

  0x000002f259a15e1c: ;   {metadata(method data for {method} {0x000002f26dbbcbf8} 'putVec2' '(FF)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a15e1c: 8bc6 49b8 | d850 586e | f202 0000 | 4983 8028 | 0100 0001 | 41b8 0800 | 0000 488b | d6c5 fa11 
  0x000002f259a15e3c: 8c24 e000 | 0000 c5fa | 1184 249c | 0000 000f 

  0x000002f259a15e4c: ;   {optimized virtual_call}
  0x000002f259a15e4c: 1f40 00e8 

  0x000002f259a15e50: ; ImmutableOopMap {[144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual align {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putVec2@3 (line 62)
                      ; - net.minecraft.client.gl.GlobalSettings::set@17 (line 29)
  0x000002f259a15e50: 5c2b 2807 

  0x000002f259a15e54: ;   {other}
  0x000002f259a15e54: 0f1f 8400 | cc0b 0005 | 488b 8424 | a800 0000 | 8b50 1048 | 3b02 488b 

  0x000002f259a15e6c: ;   {metadata(method data for {method} {0x000002f26dbbcbf8} 'putVec2' '(FF)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a15e6c: f248 bfd8 | 5058 6ef2 | 0200 008b | 7608 49ba | 0000 0000 | f202 0000 | 4903 f248 | 3bb7 5001 
  0x000002f259a15e8c: 0000 750d | 4883 8758 | 0100 0001 | e960 0000 | 0048 3bb7 | 6001 0000 | 750d 4883 | 8768 0100 
  0x000002f259a15eac: 0001 e94a | 0000 0048 | 83bf 5001 | 0000 0075 | 1748 89b7 | 5001 0000 | 48c7 8758 | 0100 0001 
  0x000002f259a15ecc: 0000 00e9 | 2900 0000 | 4883 bf60 | 0100 0000 | 7517 4889 | b760 0100 | 0048 c787 | 6801 0000 
  0x000002f259a15eec: 0100 0000 | e908 0000 | 0048 8387 | 4801 0000 | 01c5 fa10 | 8424 9c00 | 0000 0f1f | 8000 0000 
  0x000002f259a15f0c: 0048 b840 | 93e1 48f2 

  0x000002f259a15f14: ;   {virtual_call}
  0x000002f259a15f14: 0200 00e8 

  0x000002f259a15f18: ; ImmutableOopMap {[144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual putFloat {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putVec2@12 (line 63)
                      ; - net.minecraft.client.gl.GlobalSettings::set@17 (line 29)
  0x000002f259a15f18: e655 2a07 

  0x000002f259a15f1c: ;   {other}
  0x000002f259a15f1c: 0f1f 8400 | 940c 0006 | 488b 8424 | a800 0000 | 8b50 1048 | 3b02 488b 

  0x000002f259a15f34: ;   {metadata(method data for {method} {0x000002f26dbbcbf8} 'putVec2' '(FF)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a15f34: f248 bfd8 | 5058 6ef2 | 0200 008b | 7608 49ba | 0000 0000 | f202 0000 | 4903 f248 | 3bb7 8001 
  0x000002f259a15f54: 0000 750d | 4883 8788 | 0100 0001 | e960 0000 | 0048 3bb7 | 9001 0000 | 750d 4883 | 8798 0100 
  0x000002f259a15f74: 0001 e94a | 0000 0048 | 83bf 8001 | 0000 0075 | 1748 89b7 | 8001 0000 | 48c7 8788 | 0100 0001 
  0x000002f259a15f94: 0000 00e9 | 2900 0000 | 4883 bf90 | 0100 0000 | 7517 4889 | b790 0100 | 0048 c787 | 9801 0000 
  0x000002f259a15fb4: 0100 0000 | e908 0000 | 0048 8387 | 7801 0000 | 01c5 fa10 | 8424 e000 | 0000 0f1f | 8000 0000 
  0x000002f259a15fd4: 0048 b868 | 93e1 48f2 

  0x000002f259a15fdc: ;   {virtual_call}
  0x000002f259a15fdc: 0200 00e8 

  0x000002f259a15fe0: ; ImmutableOopMap {[144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual putFloat {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putVec2@21 (line 64)
                      ; - net.minecraft.client.gl.GlobalSettings::set@17 (line 29)
  0x000002f259a15fe0: 1e55 2a07 

  0x000002f259a15fe4: ;   {other}
  0x000002f259a15fe4: 0f1f 8400 | 5c0d 0007 | 488b 8424 | a800 0000 

  0x000002f259a15ff4: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a15ff4: 49b8 404c | 586e f202 | 0000 4983 | 8078 0100 | 0001 c5fb | 1084 24d8 | 0000 00c5 

  0x000002f259a16010: ;   {metadata(method data for {method} {0x000002f26dbbca48} 'putFloat' '(F)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a16010: fb5a c049 | b8a8 352b | 6ef2 0200 | 0041 8b90 | cc00 0000 | 83c2 0241 | 8990 cc00 | 0000 81e2 
  0x000002f259a16030: feff 1f00 | 85d2 0f84 | 8f0e 0000 | 488b 8424 | a800 0000 

  0x000002f259a16044: ;   {metadata(method data for {method} {0x000002f26dbbca48} 'putFloat' '(F)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a16044: 49b8 a835 | 2b6e f202 | 0000 4983 | 8028 0100 | 0001 41b8 | 0400 0000 | 488b 9424 | a800 0000 
  0x000002f259a16064: c5fa 1184 | 24e4 0000 

  0x000002f259a1606c: ;   {optimized virtual_call}
  0x000002f259a1606c: 0066 90e8 

  0x000002f259a16070: ; ImmutableOopMap {[144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual align {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putFloat@2 (line 50)
                      ; - net.minecraft.client.gl.GlobalSettings::set@22 (line 30)
  0x000002f259a16070: 3c29 2807 

  0x000002f259a16074: ;   {other}
  0x000002f259a16074: 0f1f 8400 | ec0d 0008 | 488b 8424 | a800 0000 | 8b50 1048 | 3b02 488b 

  0x000002f259a1608c: ;   {metadata(method data for {method} {0x000002f26dbbca48} 'putFloat' '(F)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a1608c: f248 bfa8 | 352b 6ef2 | 0200 008b | 7608 49ba | 0000 0000 | f202 0000 | 4903 f248 | 3bb7 5001 
  0x000002f259a160ac: 0000 750d | 4883 8758 | 0100 0001 | e960 0000 | 0048 3bb7 | 6001 0000 | 750d 4883 | 8768 0100 
  0x000002f259a160cc: 0001 e94a | 0000 0048 | 83bf 5001 | 0000 0075 | 1748 89b7 | 5001 0000 | 48c7 8758 | 0100 0001 
  0x000002f259a160ec: 0000 00e9 | 2900 0000 | 4883 bf60 | 0100 0000 | 7517 4889 | b760 0100 | 0048 c787 | 6801 0000 
  0x000002f259a1610c: 0100 0000 | e908 0000 | 0048 8387 | 4801 0000 | 01c5 fa10 | 8424 e400 | 0000 0f1f | 8000 0000 
  0x000002f259a1612c: 0048 b890 | 93e1 48f2 

  0x000002f259a16134: ;   {virtual_call}
  0x000002f259a16134: 0200 00e8 

  0x000002f259a16138: ; ImmutableOopMap {[144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual putFloat {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putFloat@11 (line 51)
                      ; - net.minecraft.client.gl.GlobalSettings::set@22 (line 30)
  0x000002f259a16138: c653 2a07 

  0x000002f259a1613c: ;   {other}
  0x000002f259a1613c: 0f1f 8400 | b40e 0009 | 488b 9424 | 8800 0000 | b9c0 5d00 | 0048 8bf1 | 488b ce48 | 83fe 000f 
  0x000002f259a1615c: 8490 0d00 

  0x000002f259a16160: ;   {runtime_call}
  0x000002f259a16160: 0048 b8e0 | 1925 17fa | 7f00 00ff 

  0x000002f259a1616c: ;   {other}
  0x000002f259a1616c: d00f 1f84 | 0000 0000 | 00c4 e1fa | 2ac0 488b | b424 9000 | 0000 4885 | f60f 8429 

  0x000002f259a16188: ;   {metadata('net/minecraft/client/render/RenderTickCounter$Dynamic')}
  0x000002f259a16188: 0000 0048 | bb10 cac9 | 01f2 0200 | 008b 7e08 | 49ba 0000 | 0000 f202 | 0000 4903 | fa48 3b5f 
  0x000002f259a161a8: 380f 8547 | 0d00 00e9 | 0000 0000 | 4c8b c648 | 3b06 4c8b 

  0x000002f259a161bc: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a161bc: c648 ba40 | 4c58 6ef2 | 0200 0048 | 8382 a801 

  0x000002f259a161cc: ;   {metadata(method data for {method} {0x000002f244d69498} 'getTickProgress' '(Z)F' in 'net/minecraft/client/render/RenderTickCounter$Dynamic')}
  0x000002f259a161cc: 0000 0149 | b8d0 5258 | 6ef2 0200 | 0041 8b90 | cc00 0000 | 83c2 0241 | 8990 cc00 | 0000 81e2 
  0x000002f259a161ec: feff 1f00 | 85d2 0f84 | 080d 0000 

  0x000002f259a161f8: ;   {metadata(method data for {method} {0x000002f244d69498} 'getTickProgress' '(Z)F' in 'net/minecraft/client/render/RenderTickCounter$Dynamic')}
  0x000002f259a161f8: 49b8 d052 | 586e f202 | 0000 41ff | 8028 0100 | 0044 0fbe | 4631 4585 

  0x000002f259a16210: ;   {metadata(method data for {method} {0x000002f244d69498} 'getTickProgress' '(Z)F' in 'net/minecraft/client/render/RenderTickCounter$Dynamic')}
  0x000002f259a16210: c049 b8d0 | 5258 6ef2 | 0200 00ba | 3801 0000 | 7405 ba48 | 0100 0049 | 8b3c 1048 | 8d7f 0149 
  0x000002f259a16230: 893c 100f | 840d 0000 

  0x000002f259a16238: ;   {section_word}
  0x000002f259a16238: 00c5 fa10 | 0dbf f3ff | ffe9 5000 | 0000 440f | be46 3045 

  0x000002f259a1624c: ;   {metadata(method data for {method} {0x000002f244d69498} 'getTickProgress' '(Z)F' in 'net/minecraft/client/render/RenderTickCounter$Dynamic')}
  0x000002f259a1624c: 85c0 49b8 | d052 586e | f202 0000 | ba58 0100 | 0074 05ba | 6801 0000 | 498b 3c10 | 488d 7f01 
  0x000002f259a1626c: 4989 3c10 | 0f84 1b00 | 0000 c5fa 

  0x000002f259a16278: ;   {metadata(method data for {method} {0x000002f244d69498} 'getTickProgress' '(Z)F' in 'net/minecraft/client/render/RenderTickCounter$Dynamic')}
  0x000002f259a16278: 104e 2849 | b8d0 5258 | 6ef2 0200 | 0041 ff80 | 7801 0000 | e905 0000 | 00c5 fa10 | 4e20 488b 
  0x000002f259a16298: 8424 a800 | 0000 4c8b 

  0x000002f259a162a0: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a162a0: c048 ba40 | 4c58 6ef2 | 0200 0048 | 8382 d801 | 0000 01c5 

  0x000002f259a162b4: ;   {section_word}
  0x000002f259a162b4: f258 c8c5 | f25e 0d45 

  0x000002f259a162bc: ;   {metadata(method data for {method} {0x000002f26dbbca48} 'putFloat' '(F)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a162bc: f3ff ff49 | b8a8 352b | 6ef2 0200 | 0041 8b90 | cc00 0000 | 83c2 0241 | 8990 cc00 | 0000 81e2 
  0x000002f259a162dc: feff 1f00 | 85d2 0f84 | 390c 0000 

  0x000002f259a162e8: ;   {metadata(method data for {method} {0x000002f26dbbca48} 'putFloat' '(F)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a162e8: 4c8b c048 | baa8 352b | 6ef2 0200 | 0048 8382 | 2801 0000 | 0141 b804 | 0000 0048 | 8bd0 c5fa 
  0x000002f259a16308: 118c 24e8 

  0x000002f259a1630c: ;   {optimized virtual_call}
  0x000002f259a1630c: 0000 00e8 

  0x000002f259a16310: ; ImmutableOopMap {[160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual align {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putFloat@2 (line 50)
                      ; - net.minecraft.client.gl.GlobalSettings::set@44 (line 31)
  0x000002f259a16310: 9c26 2807 

  0x000002f259a16314: ;   {other}
  0x000002f259a16314: 0f1f 8400 | 8c10 000a | 488b 8424 | a800 0000 | 8b50 1048 | 3b02 488b 

  0x000002f259a1632c: ;   {metadata(method data for {method} {0x000002f26dbbca48} 'putFloat' '(F)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a1632c: f248 bfa8 | 352b 6ef2 | 0200 008b | 7608 49ba | 0000 0000 | f202 0000 | 4903 f248 | 3bb7 5001 
  0x000002f259a1634c: 0000 750d | 4883 8758 | 0100 0001 | e960 0000 | 0048 3bb7 | 6001 0000 | 750d 4883 | 8768 0100 
  0x000002f259a1636c: 0001 e94a | 0000 0048 | 83bf 5001 | 0000 0075 | 1748 89b7 | 5001 0000 | 48c7 8758 | 0100 0001 
  0x000002f259a1638c: 0000 00e9 | 2900 0000 | 4883 bf60 | 0100 0000 | 7517 4889 | b760 0100 | 0048 c787 | 6801 0000 
  0x000002f259a163ac: 0100 0000 | e908 0000 | 0048 8387 | 4801 0000 | 01c5 fa10 | 8424 e800 | 0000 0f1f | 8000 0000 
  0x000002f259a163cc: 0048 b8b8 | 93e1 48f2 

  0x000002f259a163d4: ;   {virtual_call}
  0x000002f259a163d4: 0200 00e8 

  0x000002f259a163d8: ; ImmutableOopMap {[160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual putFloat {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putFloat@11 (line 51)
                      ; - net.minecraft.client.gl.GlobalSettings::set@44 (line 31)
  0x000002f259a163d8: 2651 2a07 

  0x000002f259a163dc: ;   {other}
  0x000002f259a163dc: 0f1f 8400 | 5411 000b | 488b 8424 | a800 0000 

  0x000002f259a163ec: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a163ec: 49b8 404c | 586e f202 | 0000 4983 | 8008 0200 

  0x000002f259a163fc: ;   {metadata(method data for {method} {0x000002f26dbbcb10} 'putInt' '(I)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a163fc: 0001 49b8 | 9854 586e | f202 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002f259a1641c: ff1f 0085 | d20f 8420 | 0b00 0048 | 8b84 24a8 

  0x000002f259a1642c: ;   {metadata(method data for {method} {0x000002f26dbbcb10} 'putInt' '(I)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a1642c: 0000 0049 | b898 5458 | 6ef2 0200 | 0049 8380 | 2801 0000 | 0141 b804 | 0000 0048 | 8b94 24a8 
  0x000002f259a1644c: ;   {optimized virtual_call}
  0x000002f259a1644c: 0000 00e8 

  0x000002f259a16450: ; ImmutableOopMap {[160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual align {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putInt@2 (line 56)
                      ; - net.minecraft.client.gl.GlobalSettings::set@49 (line 32)
  0x000002f259a16450: 5c25 2807 

  0x000002f259a16454: ;   {other}
  0x000002f259a16454: 0f1f 8400 | cc11 000c | 488b 8424 | a800 0000 | 8b50 1048 | 3b02 4c8b 

  0x000002f259a1646c: ;   {metadata(method data for {method} {0x000002f26dbbcb10} 'putInt' '(I)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a1646c: c248 be98 | 5458 6ef2 | 0200 0045 | 8b40 0849 | ba00 0000 | 00f2 0200 | 004d 03c2 | 4c3b 8650 
  0x000002f259a1648c: 0100 0075 | 0d48 8386 | 5801 0000 | 01e9 6000 | 0000 4c3b | 8660 0100 | 0075 0d48 | 8386 6801 
  0x000002f259a164ac: 0000 01e9 | 4a00 0000 | 4883 be50 | 0100 0000 | 7517 4c89 | 8650 0100 | 0048 c786 | 5801 0000 
  0x000002f259a164cc: 0100 0000 | e929 0000 | 0048 83be | 6001 0000 | 0075 174c | 8986 6001 | 0000 48c7 | 8668 0100 
  0x000002f259a164ec: 0001 0000 | 00e9 0800 | 0000 4883 | 8648 0100 | 0001 448b | 8424 9800 | 0000 0f1f | 8000 0000 
  0x000002f259a1650c: 0048 b8e0 | 93e1 48f2 

  0x000002f259a16514: ;   {virtual_call}
  0x000002f259a16514: 0200 00e8 

  0x000002f259a16518: ; ImmutableOopMap {[160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual putInt {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putInt@11 (line 57)
                      ; - net.minecraft.client.gl.GlobalSettings::set@49 (line 32)
  0x000002f259a16518: 66c7 4907 

  0x000002f259a1651c: ;   {other}
  0x000002f259a1651c: 0f1f 8400 | 9412 000d | 488b 8424 | a800 0000 

  0x000002f259a1652c: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a1652c: 48ba 404c | 586e f202 | 0000 4883 | 8238 0200 

  0x000002f259a1653c: ;   {metadata(method data for {method} {0x000002f26dbbc898} 'get' '()Ljava/nio/ByteBuffer;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a1653c: 0001 48ba | f8a0 556e | f202 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002f259a1655c: 0085 f60f | 8408 0a00 | 0048 8b84 | 24a8 0000 | 008b 5010 

  0x000002f259a16570: ; implicit exception: dispatches to 0x000002f259a16f8e
  0x000002f259a16570: 483b 0248 

  0x000002f259a16574: ;   {metadata(method data for {method} {0x000002f26dbbc898} 'get' '()Ljava/nio/ByteBuffer;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a16574: 8bf2 48bf | f8a0 556e | f202 0000 | 8b76 0849 | ba00 0000 | 00f2 0200 | 0049 03f2 | 483b b720 
  0x000002f259a16594: 0100 0075 | 0d48 8387 | 2801 0000 | 01e9 6000 | 0000 483b | b730 0100 | 0075 0d48 | 8387 3801 
  0x000002f259a165b4: 0000 01e9 | 4a00 0000 | 4883 bf20 | 0100 0000 | 7517 4889 | b720 0100 | 0048 c787 | 2801 0000 
  0x000002f259a165d4: 0100 0000 | e929 0000 | 0048 83bf | 3001 0000 | 0075 1748 | 89b7 3001 | 0000 48c7 | 8738 0100 
  0x000002f259a165f4: 0001 0000 | 00e9 0800 | 0000 4883 | 8718 0100 | 0001 0f1f | 8000 0000 | 0048 b808 | 94e1 48f2 
  0x000002f259a16614: ;   {virtual_call}
  0x000002f259a16614: 0200 00e8 

  0x000002f259a16618: ; ImmutableOopMap {[160]=Oop [176]=Oop }
                      ;*invokevirtual flip {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::get@4 (line 40)
                      ; - net.minecraft.client.gl.GlobalSettings::set@52 (line 33)
  0x000002f259a16618: e61b cf06 

  0x000002f259a1661c: ;   {other}
  0x000002f259a1661c: 0f1f 8400 | 9413 000e | 4889 8424 | c000 0000 

  0x000002f259a1662c: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a1662c: 48ba 404c | 586e f202 | 0000 4883 | 8258 0200 

  0x000002f259a1663c: ;   {metadata(method data for {method} {0x000002f244b526d8} 'getDevice' '()Lcom/mojang/blaze3d/systems/GpuDevice;' in 'com/mojang/blaze3d/systems/RenderSystem')}
  0x000002f259a1663c: 0001 48ba | 5056 026e | f202 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002f259a1665c: 0085 f60f | 842e 0900 

  0x000002f259a16664: ;   {oop(a 'java/lang/Class'{0x000000009131e248} = 'com/mojang/blaze3d/systems/RenderSystem')}
  0x000002f259a16664: 0048 ba48 | e231 9100 | 0000 008b | 5278 4885 

  0x000002f259a16674: ;   {metadata(method data for {method} {0x000002f244b526d8} 'getDevice' '()Lcom/mojang/blaze3d/systems/GpuDevice;' in 'com/mojang/blaze3d/systems/RenderSystem')}
  0x000002f259a16674: d248 be50 | 5602 6ef2 | 0200 00bf | 2801 0000 | 7405 bf18 | 0100 0048 | 8b1c 3e48 | 8d5b 0148 
  0x000002f259a16694: 891c 3e0f | 84a6 0300 | 0048 8bb4 | 24a0 0000 | 0048 85d2 | 0f84 2900 

  0x000002f259a166ac: ;   {metadata('net/minecraft/client/gl/GlBackend')}
  0x000002f259a166ac: 0000 49b8 | e0b0 e501 | f202 0000 | 8b4a 0849 | ba00 0000 | 00f2 0200 | 0049 03ca | 4c3b 4138 
  0x000002f259a166cc: 0f85 e208 | 0000 e900 | 0000 0048 | 8bfa 483b | 0248 8bfa 

  0x000002f259a166e0: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a166e0: 48bb 404c | 586e f202 | 0000 4883 | 8378 0200 | 0001 8b7a | 148b 560c 

  0x000002f259a166f8: ; implicit exception: dispatches to 0x000002f259a16fbe
  0x000002f259a166f8: 483b 0248 

  0x000002f259a166fc: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a166fc: 8bda 48b9 | 404c 586e | f202 0000 | 8b5b 0849 | ba00 0000 | 00f2 0200 | 0049 03da | 483b 99a0 
  0x000002f259a1671c: 0200 0075 | 0d48 8381 | a802 0000 | 01e9 6000 | 0000 483b | 99b0 0200 | 0075 0d48 | 8381 b802 
  0x000002f259a1673c: 0000 01e9 | 4a00 0000 | 4883 b9a0 | 0200 0000 | 7517 4889 | 99a0 0200 | 0048 c781 | a802 0000 
  0x000002f259a1675c: 0100 0000 | e929 0000 | 0048 83b9 | b002 0000 | 0075 1748 | 8999 b002 | 0000 48c7 | 81b8 0200 
  0x000002f259a1677c: 0001 0000 | 00e9 0800 | 0000 4883 | 8198 0200 | 0001 4889 | bc24 b800 

  0x000002f259a16794: ;   {optimized virtual_call}
  0x000002f259a16794: 0000 90e8 

  0x000002f259a16798: ; ImmutableOopMap {[160]=Oop [176]=Oop [184]=Oop [192]=Oop }
                      ;*invokevirtual slice {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@69 (line 34)
  0x000002f259a16798: f450 2b07 

  0x000002f259a1679c: ;   {other}
  0x000002f259a1679c: 0f1f 8400 | 1415 000f | 488b bc24 | b800 0000 | 4885 ff0f | 8429 0000 

  0x000002f259a167b4: ;   {metadata('net/minecraft/client/gl/GlCommandEncoder')}
  0x000002f259a167b4: 0048 be68 | 79e6 01f2 | 0200 008b | 5708 49ba | 0000 0000 | f202 0000 | 4903 d248 | 3b72 380f 
  0x000002f259a167d4: 85ea 0700 | 00e9 0000 | 0000 4c8b | c748 3b07 

  0x000002f259a167e4: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a167e4: 4c8b c749 | b940 4c58 | 6ef2 0200 | 0049 8381 | d802 0000 | 014c 8bc0 | 4c8b 8c24 | c000 0000 
  0x000002f259a16804: ;   {optimized virtual_call}
  0x000002f259a16804: 488b d7e8 

  0x000002f259a16808: ; ImmutableOopMap {[160]=Oop [176]=Oop }
                      ;*invokeinterface writeToBuffer {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@74 (line 34)
  0x000002f259a16808: 448e 0907 

  0x000002f259a1680c: ;   {other}
  0x000002f259a1680c: 0f1f 8400 | 8415 0010 | 488b 8424 | b000 0000 

  0x000002f259a1681c: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a1681c: 4885 c048 | ba40 4c58 | 6ef2 0200 | 00be f802 | 0000 7405 | be08 0300 | 0048 8b3c | 3248 8d7f 
  0x000002f259a1683c: 0148 893c | 320f 846d | 0100 0048 

  0x000002f259a16848: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a16848: 8bd0 48be | 404c 586e | f202 0000 | 8b52 0849 | ba00 0000 | 00f2 0200 | 0049 03d2 | 483b 9620 
  0x000002f259a16868: 0300 0075 | 0d48 8386 | 2803 0000 | 01e9 6000 | 0000 483b | 9630 0300 | 0075 0d48 | 8386 3803 
  0x000002f259a16888: 0000 01e9 | 4a00 0000 | 4883 be20 | 0300 0000 | 7517 4889 | 9620 0300 | 0048 c786 | 2803 0000 
  0x000002f259a168a8: 0100 0000 | e929 0000 | 0048 83be | 3003 0000 | 0075 1748 | 8996 3003 | 0000 48c7 | 8638 0300 
  0x000002f259a168c8: 0001 0000 | 00e9 0800 | 0000 4883 | 8618 0300 

  0x000002f259a168d8: ;   {metadata(method data for {method} {0x000002f26d5d06a0} 'close' '()V' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a168d8: 0001 48ba | e0a9 816d | f202 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002f259a168f8: 0085 f60f | 84cc 0600 | 0048 8bd0 

  0x000002f259a16904: ;   {metadata(method data for {method} {0x000002f26d5d06a0} 'close' '()V' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a16904: 48be e0a9 | 816d f202 | 0000 8b52 | 0849 ba00 | 0000 00f2 | 0200 0049 | 03d2 483b | 9620 0100 
  0x000002f259a16924: 0075 0d48 | 8386 2801 | 0000 01e9 | 6000 0000 | 483b 9630 | 0100 0075 | 0d48 8386 | 3801 0000 
  0x000002f259a16944: 01e9 4a00 | 0000 4883 | be20 0100 | 0000 7517 | 4889 9620 | 0100 0048 | c786 2801 | 0000 0100 
  0x000002f259a16964: 0000 e929 | 0000 0048 | 83be 3001 | 0000 0075 | 1748 8996 | 3001 0000 | 48c7 8638 | 0100 0001 
  0x000002f259a16984: 0000 00e9 | 0800 0000 | 4883 8618 | 0100 0001 

  0x000002f259a16994: ;   {optimized virtual_call}
  0x000002f259a16994: 488b d0e8 

  0x000002f259a16998: ; ImmutableOopMap {[160]=Oop }
                      ;*invokevirtual pop {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.lwjgl.system.MemoryStack::close@1 (line 168)
                      ; - net.minecraft.client.gl.GlobalSettings::set@86 (line 35)
  0x000002f259a16998: f4a7 0e07 

  0x000002f259a1699c: ;   {other}
  0x000002f259a1699c: 0f1f 8400 | 1417 0011 

  0x000002f259a169a4: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a169a4: 48ba 404c | 586e f202 | 0000 ff82 | 4803 0000 | 488b 9424 | a000 0000 

  0x000002f259a169bc: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a169bc: 8b52 0c48 | be40 4c58 | 6ef2 0200 | 0048 8386 | f803 0000 

  0x000002f259a169d0: ;   {metadata(method data for {method} {0x000002f244b52008} 'setGlobalSettingsUniform' '(Lcom/mojang/blaze3d/buffers/GpuBuffer;)V' in 'com/mojang/blaze3d/systems/RenderSystem')}
  0x000002f259a169d0: 0148 be98 | 5758 6ef2 | 0200 008b | becc 0000 | 0083 c702 | 89be cc00 | 0000 81e7 | feff 1f00 
  0x000002f259a169f0: 85ff 0f84 | f605 0000 

  0x000002f259a169f8: ;   {oop(a 'java/lang/Class'{0x000000009131e248} = 'com/mojang/blaze3d/systems/RenderSystem')}
  0x000002f259a169f8: 48be 48e2 | 3191 0000 | 0000 410f | be7f 3885 | ff0f 8500 | 0600 004c | 8bd2 4489 | 96cc 0000 
  0x000002f259a16a18: 0048 8bfe | 4833 fa48 | c1ef 1448 | 83ff 000f | 8500 0600 | 0048 81c4 | 0001 0000 

  0x000002f259a16a34: ;   {poll_return}
  0x000002f259a16a34: 5d49 3ba7 | 4804 0000 | 0f87 0306 | 0000 c30f | 1f44 0000 

  0x000002f259a16a48: ;   {no_reloc}
  0x000002f259a16a48: e91d 0600 | 0000 0000 | 0000 80ba | 3101 0000 | 040f 851a | 0600 0049 | 8b87 b801 | 0000 488d 
  0x000002f259a16a68: 7828 493b | bfc8 0100 | 000f 8702 | 0600 0049 | 89bf b801 | 0000 48c7 | 0001 0000 | 0048 8bca 
  0x000002f259a16a88: 49ba 0000 | 0000 f202 | 0000 492b | ca89 4808 | 4833 c989 | 480c 4833 | c948 8948 | 1048 8948 
  0x000002f259a16aa8: 1848 8948 | 204c 8bc0 

  0x000002f259a16ab0: ;   {metadata(method data for {method} {0x000002f244b526d8} 'getDevice' '()Lcom/mojang/blaze3d/systems/GpuDevice;' in 'com/mojang/blaze3d/systems/RenderSystem')}
  0x000002f259a16ab0: 48ba 5056 | 026e f202 | 0000 4883 | 8238 0100 

  0x000002f259a16ac0: ;   {oop("Can't getDevice() before it was initialized"{0x0000000096400000})}
  0x000002f259a16ac0: 0001 49b8 | 0000 4096 | 0000 0000 | 488b d048 | 8984 24c8 

  0x000002f259a16ad4: ;   {optimized virtual_call}
  0x000002f259a16ad4: 0000 00e8 

  0x000002f259a16ad8: ; ImmutableOopMap {[160]=Oop [176]=Oop [192]=Oop [200]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.systems.RenderSystem::getDevice@13 (line 410)
                      ; - net.minecraft.client.gl.GlobalSettings::set@57 (line 34)
  0x000002f259a16ad8: 047d 5a06 

  0x000002f259a16adc: ;   {other}
  0x000002f259a16adc: 0f1f 8400 | 5418 0012 | 488b 8424 

  0x000002f259a16ae8: ; ImmutableOopMap {rax=Oop [160]=Oop [176]=Oop [192]=Oop }
                      ;*athrow {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.mojang.blaze3d.systems.RenderSystem::getDevice@16 (line 410)
                      ; - net.minecraft.client.gl.GlobalSettings::set@57 (line 34)
  0x000002f259a16ae8: c800 0000 

  0x000002f259a16aec: ;   {section_word}
  0x000002f259a16aec: 48ba ec6a | a159 f202 

  0x000002f259a16af4: ;   {runtime_call handle_exception Runtime1 stub}
  0x000002f259a16af4: 0000 e865 | cd66 0690 | 488b 9424 | b000 0000 | 498b 8700 | 0500 004d | 33d2 4d89 | 9700 0500 
  0x000002f259a16b14: 004d 33d2 | 4d89 9708 | 0500 0048 

  0x000002f259a16b20: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a16b20: 85d2 48be | 404c 586e | f202 0000 | bf60 0300 | 0074 05bf | 7003 0000 | 488b 1c3e | 488d 5b01 
  0x000002f259a16b40: 4889 1c3e | 0f84 e201 | 0000 4889 | 8424 d000 | 0000 483b | 0248 8bf2 

  0x000002f259a16b58: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a16b58: 48bf 404c | 586e f202 | 0000 8b76 | 0849 ba00 | 0000 00f2 | 0200 0049 | 03f2 483b | b788 0300 
  0x000002f259a16b78: 0075 0d48 | 8387 9003 | 0000 01e9 | 6000 0000 | 483b b798 | 0300 0075 | 0d48 8387 | a003 0000 
  0x000002f259a16b98: 01e9 4a00 | 0000 4883 | bf88 0300 | 0000 7517 | 4889 b788 | 0300 0048 | c787 9003 | 0000 0100 
  0x000002f259a16bb8: 0000 e929 | 0000 0048 | 83bf 9803 | 0000 0075 | 1748 89b7 | 9803 0000 | 48c7 87a0 | 0300 0001 
  0x000002f259a16bd8: 0000 00e9 | 0800 0000 | 4883 8780 | 0300 0001 

  0x000002f259a16be8: ;   {metadata(method data for {method} {0x000002f26d5d06a0} 'close' '()V' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a16be8: 48be e0a9 | 816d f202 | 0000 8bbe | cc00 0000 | 83c7 0289 | becc 0000 | 0081 e7fe | ff1f 0085 
  0x000002f259a16c08: ff0f 847c | 0400 0048 

  0x000002f259a16c10: ;   {metadata(method data for {method} {0x000002f26d5d06a0} 'close' '()V' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a16c10: 8bf2 48bf | e0a9 816d | f202 0000 | 8b76 0849 | ba00 0000 | 00f2 0200 | 0049 03f2 | 483b b720 
  0x000002f259a16c30: 0100 0075 | 0d48 8387 | 2801 0000 | 01e9 6000 | 0000 483b | b730 0100 | 0075 0d48 | 8387 3801 
  0x000002f259a16c50: 0000 01e9 | 4a00 0000 | 4883 bf20 | 0100 0000 | 7517 4889 | b720 0100 | 0048 c787 | 2801 0000 
  0x000002f259a16c70: 0100 0000 | e929 0000 | 0048 83bf | 3001 0000 | 0075 1748 | 89b7 3001 | 0000 48c7 | 8738 0100 
  0x000002f259a16c90: 0001 0000 | 00e9 0800 | 0000 4883 | 8718 0100 | 0001 0f1f 

  0x000002f259a16ca4: ;   {optimized virtual_call}
  0x000002f259a16ca4: 4400 00e8 

  0x000002f259a16ca8: ; ImmutableOopMap {[208]=Oop }
                      ;*invokevirtual pop {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.lwjgl.system.MemoryStack::close@1 (line 168)
                      ; - net.minecraft.client.gl.GlobalSettings::set@101 (line 27)
  0x000002f259a16ca8: 347b 5a06 

  0x000002f259a16cac: ;   {other}
  0x000002f259a16cac: 0f1f 8400 | 241a 0014 

  0x000002f259a16cb4: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a16cb4: 48b8 404c | 586e f202 | 0000 ff80 | b003 0000 | 488b 8424 | d000 0000 | e95b 0000 | 0049 8b87 
  0x000002f259a16cd4: 0005 0000 | 4d33 d24d | 8997 0005 | 0000 4d33 | d24d 8997 | 0805 0000 | 4c8b 8424 | d000 0000 
  0x000002f259a16cf4: ;   {metadata(method data for {method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a16cf4: 48ba 404c | 586e f202 | 0000 4883 | 82c8 0300 | 0001 4c8b | c048 8b94 | 24d0 0000 | 0066 0f1f 
  0x000002f259a16d14: ;   {optimized virtual_call}
  0x000002f259a16d14: 4400 00e8 

  0x000002f259a16d18: ; ImmutableOopMap {[208]=Oop }
                      ;*invokevirtual addSuppressed {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@113 (line 27)
  0x000002f259a16d18: c47a 5a06 

  0x000002f259a16d1c: ;   {other}
  0x000002f259a16d1c: 0f1f 8400 | 941a 0015 | 488b 8424 | d000 0000 | e998 0300 

  0x000002f259a16d30: ;   {metadata({method} {0x000002f26dbc63b8} 'set' '(IIDJLnet/minecraft/client/render/RenderTickCounter;I)V' in 'net/minecraft/client/gl/GlobalSettings')}
  0x000002f259a16d30: 0049 bab0 | 63bc 6df2 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002f259a16d48: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16d48: e893 1767 

  0x000002f259a16d4c: ; ImmutableOopMap {rdx=Oop rsi=Oop [144]=Oop [160]=Oop }
                      ;*synchronization entry
                      ; - net.minecraft.client.gl.GlobalSettings::set@-1 (line 27)
  0x000002f259a16d4c: 06e9 6be9 

  0x000002f259a16d50: ;   {metadata({method} {0x000002f26d5d6480} 'stackPush' '()Lorg/lwjgl/system/MemoryStack;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a16d50: ffff 49ba | 7864 5d6d | f202 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002f259a16d68: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16d68: ffe8 7217 

  0x000002f259a16d6c: ; ImmutableOopMap {[144]=Oop [160]=Oop }
                      ;*synchronization entry
                      ; - org.lwjgl.system.MemoryStack::stackPush@-1 (line 915)
                      ; - net.minecraft.client.gl.GlobalSettings::set@0 (line 27)
  0x000002f259a16d6c: 6706 e982 

  0x000002f259a16d70: ;   {metadata({method} {0x000002f26d5d63e0} 'stackGet' '()Lorg/lwjgl/system/MemoryStack;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a16d70: e9ff ff49 | bad8 635d | 6df2 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002f259a16d88: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16d88: ffff e851 

  0x000002f259a16d8c: ; ImmutableOopMap {[144]=Oop [160]=Oop }
                      ;*synchronization entry
                      ; - org.lwjgl.system.MemoryStack::stackGet@-1 (line 906)
                      ; - org.lwjgl.system.MemoryStack::stackPush@0 (line 915)
                      ; - net.minecraft.client.gl.GlobalSettings::set@0 (line 27)
  0x000002f259a16d8c: 1767 06e9 | 99e9 ffff 

  0x000002f259a16d94: ;   {metadata({method} {0x000002f200238930} 'get' '()Ljava/lang/Object;' in 'java/lang/ThreadLocal')}
  0x000002f259a16d94: 49ba 2889 | 2300 f202 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002f259a16da8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16da8: ffff ffe8 

  0x000002f259a16dac: ; ImmutableOopMap {rbx=Oop [144]=Oop [160]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ThreadLocal::get@-1 (line 172)
                      ; - org.lwjgl.system.MemoryStack::stackGet@3 (line 906)
                      ; - org.lwjgl.system.MemoryStack::stackPush@0 (line 915)
                      ; - net.minecraft.client.gl.GlobalSettings::set@0 (line 27)
  0x000002f259a16dac: 3017 6706 | e941 eaff | ff48 8904 

  0x000002f259a16db8: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002f259a16db8: 24e8 a2d3 

  0x000002f259a16dbc: ; ImmutableOopMap {[144]=Oop [160]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.lwjgl.system.MemoryStack::stackGet@6 (line 906)
                      ; - org.lwjgl.system.MemoryStack::stackPush@0 (line 915)
                      ; - net.minecraft.client.gl.GlobalSettings::set@0 (line 27)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16dbc: 6606 e89d 

  0x000002f259a16dc0: ; ImmutableOopMap {rdx=Oop [144]=Oop [160]=Oop }
                      ;*invokevirtual push {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.lwjgl.system.MemoryStack::stackPush@3 (line 915)
                      ; - net.minecraft.client.gl.GlobalSettings::set@0 (line 27)
                      ;   {metadata({method} {0x000002f26dbbc7e8} 'onStack' '(Lorg/lwjgl/system/MemoryStack;I)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a16dc0: 9d66 0649 | bae0 c7bb | 6df2 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002f259a16dd8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16dd8: ffff e801 

  0x000002f259a16ddc: ; ImmutableOopMap {rax=Oop [144]=Oop [160]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@-1 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
  0x000002f259a16ddc: 1767 06e9 | 09ec ffff 

  0x000002f259a16de4: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002f259a16de4: 488b d2e8 

  0x000002f259a16de8: ; ImmutableOopMap {rbx=Oop [144]=Oop [160]=Oop [176]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@0 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
  0x000002f259a16de8: 74a6 6606 | e94f ecff 

  0x000002f259a16df0: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16df0: ffe8 6a9d 

  0x000002f259a16df4: ; ImmutableOopMap {rbx=Oop rax=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual malloc {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@6 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
                      ;   {metadata({method} {0x000002f26d5d11e8} 'malloc' '(I)Ljava/nio/ByteBuffer;' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a16df4: 6606 49ba | e011 5d6d | f202 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002f259a16e0c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16e0c: ffe8 ce16 

  0x000002f259a16e10: ; ImmutableOopMap {rbx=Oop rax=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - org.lwjgl.system.MemoryStack::malloc@-1 (line 377)
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@6 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
  0x000002f259a16e10: 6706 e9ee | ecff ff48 

  0x000002f259a16e18: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002f259a16e18: 8904 24e8 

  0x000002f259a16e1c: ; ImmutableOopMap {[144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.lwjgl.system.MemoryStack::malloc@15 (line 377)
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@6 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
  0x000002f259a16e1c: 40d3 6606 

  0x000002f259a16e20: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16e20: e83b 9d66 

  0x000002f259a16e24: ; ImmutableOopMap {rdx=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual order {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.lwjgl.system.MemoryStack::malloc@21 (line 377)
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@6 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
                      ;   {metadata({method} {0x000002f26dbbc668} '<init>' '(Ljava/nio/ByteBuffer;)V' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a16e24: 0649 ba60 | c6bb 6df2 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002f259a16e3c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16e3c: e89f 1667 

  0x000002f259a16e40: ; ImmutableOopMap {rax=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - com.mojang.blaze3d.buffers.Std140Builder::<init>@-1 (line 26)
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@9 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
  0x000002f259a16e40: 06e9 e9ee 

  0x000002f259a16e44: ;   {metadata({method} {0x000002f20049d358} '<init>' '()V' in 'java/lang/Object')}
  0x000002f259a16e44: ffff 49ba | 50d3 4900 | f202 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002f259a16e5c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16e5c: ffe8 7e16 

  0x000002f259a16e60: ; ImmutableOopMap {rax=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - java.lang.Object::<init>@-1 (line 45)
                      ; - com.mojang.blaze3d.buffers.Std140Builder::<init>@1 (line 26)
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@9 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
  0x000002f259a16e60: 6706 e90b | efff ff44 | 8b46 1049 | 83f8 000f | 8413 efff | ff4c 8904 

  0x000002f259a16e78: ;   {runtime_call g1_pre_barrier_slow}
  0x000002f259a16e78: 24e8 621c | 6706 e905 | efff ff48 | 83f8 000f | 8416 efff | ff48 8934 

  0x000002f259a16e90: ;   {runtime_call g1_post_barrier_slow}
  0x000002f259a16e90: 24e8 4a1f | 6706 e908 

  0x000002f259a16e98: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16e98: efff ffe8 

  0x000002f259a16e9c: ; ImmutableOopMap {rax=Oop rsi=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual position {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::<init>@11 (line 28)
                      ; - com.mojang.blaze3d.buffers.Std140Builder::onStack@9 (line 36)
                      ; - net.minecraft.client.gl.GlobalSettings::set@10 (line 28)
  0x000002f259a16e9c: c09c 6606 

  0x000002f259a16ea0: ;   {metadata({method} {0x000002f26dbbcbf8} 'putVec2' '(FF)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a16ea0: 49ba f0cb | bb6d f202 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002f259a16eb4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16eb4: ffff ffe8 

  0x000002f259a16eb8: ; ImmutableOopMap {rsi=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putVec2@-1 (line 62)
                      ; - net.minecraft.client.gl.GlobalSettings::set@17 (line 29)
  0x000002f259a16eb8: 2416 6706 | e95a efff 

  0x000002f259a16ec0: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16ec0: ffe8 9a9c 

  0x000002f259a16ec4: ; ImmutableOopMap {rax=Oop rdx=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual putFloat {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putVec2@12 (line 63)
                      ; - net.minecraft.client.gl.GlobalSettings::set@17 (line 29)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16ec4: 6606 e895 

  0x000002f259a16ec8: ; ImmutableOopMap {rax=Oop rdx=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual putFloat {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putVec2@21 (line 64)
                      ; - net.minecraft.client.gl.GlobalSettings::set@17 (line 29)
                      ;   {metadata({method} {0x000002f26dbbca48} 'putFloat' '(F)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a16ec8: 9c66 0649 | ba40 cabb | 6df2 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002f259a16ee0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16ee0: ffff e8f9 

  0x000002f259a16ee4: ; ImmutableOopMap {[144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putFloat@-1 (line 50)
                      ; - net.minecraft.client.gl.GlobalSettings::set@22 (line 30)
  0x000002f259a16ee4: 1567 06e9 | 50f1 ffff 

  0x000002f259a16eec: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16eec: e86f 9c66 

  0x000002f259a16ef0: ; ImmutableOopMap {rax=Oop rdx=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual putFloat {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putFloat@11 (line 51)
                      ; - net.minecraft.client.gl.GlobalSettings::set@22 (line 30)
                      ;   {runtime_call throw_div0_exception Runtime1 stub}
  0x000002f259a16ef0: 06e8 6a99 

  0x000002f259a16ef4: ; ImmutableOopMap {[144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*lrem {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@30 (line 30)
                      ;   {runtime_call throw_incompatible_class_change_error Runtime1 stub}
  0x000002f259a16ef4: 6606 e8e5 

  0x000002f259a16ef8: ; ImmutableOopMap {rsi=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokeinterface getTickProgress {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@35 (line 31)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16ef8: d966 06e8 

  0x000002f259a16efc: ; ImmutableOopMap {rsi=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokeinterface getTickProgress {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@35 (line 31)
  0x000002f259a16efc: 609c 6606 

  0x000002f259a16f00: ;   {metadata({method} {0x000002f244d69498} 'getTickProgress' '(Z)F' in 'net/minecraft/client/render/RenderTickCounter$Dynamic')}
  0x000002f259a16f00: 49ba 9094 | d644 f202 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002f259a16f14: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16f14: ffff ffe8 

  0x000002f259a16f18: ; ImmutableOopMap {rsi=Oop [144]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - net.minecraft.client.render.RenderTickCounter$Dynamic::getTickProgress@-1 (line 93)
                      ; - net.minecraft.client.gl.GlobalSettings::set@35 (line 31)
  0x000002f259a16f18: c415 6706 | e9d7 f2ff 

  0x000002f259a16f20: ;   {metadata({method} {0x000002f26dbbca48} 'putFloat' '(F)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a16f20: ff49 ba40 | cabb 6df2 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002f259a16f38: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16f38: e8a3 1567 

  0x000002f259a16f3c: ; ImmutableOopMap {rax=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putFloat@-1 (line 50)
                      ; - net.minecraft.client.gl.GlobalSettings::set@44 (line 31)
  0x000002f259a16f3c: 06e9 a6f3 

  0x000002f259a16f40: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16f40: ffff e819 

  0x000002f259a16f44: ; ImmutableOopMap {rax=Oop rdx=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual putFloat {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putFloat@11 (line 51)
                      ; - net.minecraft.client.gl.GlobalSettings::set@44 (line 31)
                      ;   {metadata({method} {0x000002f26dbbcb10} 'putInt' '(I)Lcom/mojang/blaze3d/buffers/Std140Builder;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a16f44: 9c66 0649 | ba08 cbbb | 6df2 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002f259a16f5c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16f5c: ffff e87d 

  0x000002f259a16f60: ; ImmutableOopMap {[160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putInt@-1 (line 56)
                      ; - net.minecraft.client.gl.GlobalSettings::set@49 (line 32)
  0x000002f259a16f60: 1567 06e9 | bff4 ffff 

  0x000002f259a16f68: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16f68: e8f3 9b66 

  0x000002f259a16f6c: ; ImmutableOopMap {rax=Oop rdx=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual putInt {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::putInt@11 (line 57)
                      ; - net.minecraft.client.gl.GlobalSettings::set@49 (line 32)
                      ;   {metadata({method} {0x000002f26dbbc898} 'get' '()Ljava/nio/ByteBuffer;' in 'com/mojang/blaze3d/buffers/Std140Builder')}
  0x000002f259a16f6c: 0649 ba90 | c8bb 6df2 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002f259a16f84: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16f84: e857 1567 

  0x000002f259a16f88: ; ImmutableOopMap {[160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - com.mojang.blaze3d.buffers.Std140Builder::get@-1 (line 40)
                      ; - net.minecraft.client.gl.GlobalSettings::set@52 (line 33)
  0x000002f259a16f88: 06e9 d7f5 

  0x000002f259a16f8c: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16f8c: ffff e8cd 

  0x000002f259a16f90: ; ImmutableOopMap {rdx=Oop [160]=Oop [176]=Oop }
                      ;*invokevirtual flip {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.buffers.Std140Builder::get@4 (line 40)
                      ; - net.minecraft.client.gl.GlobalSettings::set@52 (line 33)
                      ;   {metadata({method} {0x000002f244b526d8} 'getDevice' '()Lcom/mojang/blaze3d/systems/GpuDevice;' in 'com/mojang/blaze3d/systems/RenderSystem')}
  0x000002f259a16f90: 9b66 0649 | bad0 26b5 | 44f2 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002f259a16fa8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16fa8: ffff e831 

  0x000002f259a16fac: ; ImmutableOopMap {rax=Oop [160]=Oop [176]=Oop [192]=Oop }
                      ;*synchronization entry
                      ; - com.mojang.blaze3d.systems.RenderSystem::getDevice@-1 (line 409)
                      ; - net.minecraft.client.gl.GlobalSettings::set@57 (line 34)
  0x000002f259a16fac: 1567 06e9 | b1f6 ffff 

  0x000002f259a16fb4: ;   {runtime_call throw_incompatible_class_change_error Runtime1 stub}
  0x000002f259a16fb4: e827 d966 

  0x000002f259a16fb8: ; ImmutableOopMap {rdx=Oop rsi=Oop [160]=Oop [176]=Oop [192]=Oop }
                      ;*invokeinterface createCommandEncoder {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@60 (line 34)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16fb8: 06e8 a29b 

  0x000002f259a16fbc: ; ImmutableOopMap {rdx=Oop rsi=Oop [160]=Oop [176]=Oop [192]=Oop }
                      ;*invokeinterface createCommandEncoder {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@60 (line 34)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16fbc: 6606 e89d 

  0x000002f259a16fc0: ; ImmutableOopMap {rsi=Oop rdi=Oop rdx=Oop [160]=Oop [176]=Oop [192]=Oop }
                      ;*invokevirtual slice {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@69 (line 34)
                      ;   {runtime_call throw_incompatible_class_change_error Runtime1 stub}
  0x000002f259a16fc0: 9b66 06e8 

  0x000002f259a16fc4: ; ImmutableOopMap {rax=Oop rdi=Oop [160]=Oop [176]=Oop [192]=Oop }
                      ;*invokeinterface writeToBuffer {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@74 (line 34)
  0x000002f259a16fc4: 18d9 6606 

  0x000002f259a16fc8: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a16fc8: e893 9b66 

  0x000002f259a16fcc: ; ImmutableOopMap {rax=Oop rdi=Oop [160]=Oop [176]=Oop [192]=Oop }
                      ;*invokeinterface writeToBuffer {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@74 (line 34)
                      ;   {metadata({method} {0x000002f26d5d06a0} 'close' '()V' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a16fcc: 0649 ba98 | 065d 6df2 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002f259a16fe4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a16fe4: e8f7 1467 

  0x000002f259a16fe8: ; ImmutableOopMap {rax=Oop [160]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - org.lwjgl.system.MemoryStack::close@-1 (line 168)
                      ; - net.minecraft.client.gl.GlobalSettings::set@86 (line 35)
  0x000002f259a16fe8: 06e9 13f9 

  0x000002f259a16fec: ;   {metadata({method} {0x000002f244b52008} 'setGlobalSettingsUniform' '(Lcom/mojang/blaze3d/buffers/GpuBuffer;)V' in 'com/mojang/blaze3d/systems/RenderSystem')}
  0x000002f259a16fec: ffff 49ba | 0020 b544 | f202 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002f259a17004: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a17004: ffe8 d614 

  0x000002f259a17008: ; ImmutableOopMap {rdx=Oop }
                      ;*synchronization entry
                      ; - com.mojang.blaze3d.systems.RenderSystem::setGlobalSettingsUniform@-1 (line 350)
                      ; - net.minecraft.client.gl.GlobalSettings::set@123 (line 36)
  0x000002f259a17008: 6706 e9e9 | f9ff ff8b | becc 0000 | 0048 83ff | 000f 84f0 | f9ff ff48 

  0x000002f259a17020: ;   {runtime_call g1_pre_barrier_slow}
  0x000002f259a17020: 893c 24e8 | b81a 6706 | e9e2 f9ff | ff48 83fa | 000f 84f6 | f9ff ff48 

  0x000002f259a17038: ;   {runtime_call g1_post_barrier_slow}
  0x000002f259a17038: 8934 24e8 | a01d 6706 | e9e8 f9ff 

  0x000002f259a17044: ;   {internal_word}
  0x000002f259a17044: ff49 ba35 | 6aa1 59f2 | 0200 004d | 8997 6004 

  0x000002f259a17054: ;   {runtime_call SafepointBlob}
  0x000002f259a17054: 0000 e985 

  0x000002f259a17058: ;   {metadata(nullptr)}
  0x000002f259a17058: e35a 0648 | ba00 0000 | 0000 0000 | 00b8 000f 

  0x000002f259a17068: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002f259a17068: 050a e871 

  0x000002f259a1706c: ; ImmutableOopMap {[160]=Oop [176]=Oop [192]=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.mojang.blaze3d.systems.RenderSystem::getDevice@6 (line 410)
                      ; - net.minecraft.client.gl.GlobalSettings::set@57 (line 34)
  0x000002f259a1706c: ff66 06e9 | d4f9 ffff 

  0x000002f259a17074: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a17074: e8e7 9a66 

  0x000002f259a17078: ; ImmutableOopMap {[160]=Oop [176]=Oop [192]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.systems.RenderSystem::getDevice@6 (line 410)
                      ; - net.minecraft.client.gl.GlobalSettings::set@57 (line 34)
  0x000002f259a17078: 0648 8bd2 

  0x000002f259a1707c: ;   {runtime_call fast_new_instance_init_check Runtime1 stub}
  0x000002f259a1707c: e85f ab66 

  0x000002f259a17080: ; ImmutableOopMap {[160]=Oop [176]=Oop [192]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.mojang.blaze3d.systems.RenderSystem::getDevice@6 (line 410)
                      ; - net.minecraft.client.gl.GlobalSettings::set@57 (line 34)
  0x000002f259a17080: 06e9 27fa 

  0x000002f259a17084: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002f259a17084: ffff e8d5 

  0x000002f259a17088: ; ImmutableOopMap {rdx=Oop [176]=Oop [208]=Oop }
                      ;*invokevirtual close {reexecute=0 rethrow=0 return_oop=0}
                      ; - net.minecraft.client.gl.GlobalSettings::set@101 (line 27)
                      ;   {metadata({method} {0x000002f26d5d06a0} 'close' '()V' in 'org/lwjgl/system/MemoryStack')}
  0x000002f259a17088: 9a66 0649 | ba98 065d | 6df2 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002f259a170a0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002f259a170a0: ffff e839 

  0x000002f259a170a4: ; ImmutableOopMap {rdx=Oop [176]=Oop [208]=Oop }
                      ;*synchronization entry
                      ; - org.lwjgl.system.MemoryStack::close@-1 (line 168)
                      ; - net.minecraft.client.gl.GlobalSettings::set@101 (line 27)
  0x000002f259a170a4: 1467 06e9 | 63fb ffff | 498b 8700 | 0500 0049 | c787 0005 | 0000 0000 | 0000 49c7 | 8708 0500 
  0x000002f259a170c4: 0000 0000 | 0048 81c4 | 0001 0000 

  0x000002f259a170d0: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000002f259a170d0: 5de9 0a87 | 6606 f4f4 
[Stub Code]
  0x000002f259a170d8: ;   {no_reloc}
  0x000002f259a170d8: 0f1f 4400 

  0x000002f259a170dc: ;   {static_stub}
  0x000002f259a170dc: 0048 bb00 | 0000 0000 

  0x000002f259a170e4: ;   {runtime_call nmethod}
  0x000002f259a170e4: 0000 00e9 | fbff ffff 

  0x000002f259a170ec: ;   {static_stub}
  0x000002f259a170ec: 9048 bb00 | 0000 0000 

  0x000002f259a170f4: ;   {runtime_call nmethod}
  0x000002f259a170f4: 0000 00e9 | fbff ffff 

  0x000002f259a170fc: ;   {static_stub}
  0x000002f259a170fc: 9048 bb00 | 0000 0000 

  0x000002f259a17104: ;   {runtime_call nmethod}
  0x000002f259a17104: 0000 00e9 | fbff ffff 

  0x000002f259a1710c: ;   {static_stub}
  0x000002f259a1710c: 9048 bb00 | 0000 0000 

  0x000002f259a17114: ;   {runtime_call nmethod}
  0x000002f259a17114: 0000 00e9 | fbff ffff 

  0x000002f259a1711c: ;   {static_stub}
  0x000002f259a1711c: 9048 bb00 | 0000 0000 

  0x000002f259a17124: ;   {runtime_call nmethod}
  0x000002f259a17124: 0000 00e9 | fbff ffff 

  0x000002f259a1712c: ;   {static_stub}
  0x000002f259a1712c: 9048 bb00 | 0000 0000 

  0x000002f259a17134: ;   {runtime_call nmethod}
  0x000002f259a17134: 0000 00e9 | fbff ffff 

  0x000002f259a1713c: ;   {static_stub}
  0x000002f259a1713c: 9048 bb00 | 0000 0000 

  0x000002f259a17144: ;   {runtime_call nmethod}
  0x000002f259a17144: 0000 00e9 | fbff ffff 

  0x000002f259a1714c: ;   {static_stub}
  0x000002f259a1714c: 9048 bb00 | 0000 0000 

  0x000002f259a17154: ;   {runtime_call nmethod}
  0x000002f259a17154: 0000 00e9 | fbff ffff 

  0x000002f259a1715c: ;   {static_stub}
  0x000002f259a1715c: 9048 bb00 | 0000 0000 

  0x000002f259a17164: ;   {runtime_call nmethod}
  0x000002f259a17164: 0000 00e9 | fbff ffff 

  0x000002f259a1716c: ;   {static_stub}
  0x000002f259a1716c: 9048 bb00 | 0000 0000 

  0x000002f259a17174: ;   {runtime_call nmethod}
  0x000002f259a17174: 0000 00e9 | fbff ffff 

  0x000002f259a1717c: ;   {static_stub}
  0x000002f259a1717c: 9048 bb00 | 0000 0000 

  0x000002f259a17184: ;   {runtime_call nmethod}
  0x000002f259a17184: 0000 00e9 | fbff ffff 

  0x000002f259a1718c: ;   {static_stub}
  0x000002f259a1718c: 9048 bb00 | 0000 0000 

  0x000002f259a17194: ;   {runtime_call nmethod}
  0x000002f259a17194: 0000 00e9 | fbff ffff 

  0x000002f259a1719c: ;   {static_stub}
  0x000002f259a1719c: 9048 bb00 | 0000 0000 

  0x000002f259a171a4: ;   {runtime_call nmethod}
  0x000002f259a171a4: 0000 00e9 | fbff ffff 

  0x000002f259a171ac: ;   {static_stub}
  0x000002f259a171ac: 9048 bb00 | 0000 0000 

  0x000002f259a171b4: ;   {runtime_call nmethod}
  0x000002f259a171b4: 0000 00e9 | fbff ffff 

  0x000002f259a171bc: ;   {static_stub}
  0x000002f259a171bc: 9048 bb00 | 0000 0000 

  0x000002f259a171c4: ;   {runtime_call nmethod}
  0x000002f259a171c4: 0000 00e9 | fbff ffff 

  0x000002f259a171cc: ;   {static_stub}
  0x000002f259a171cc: 9048 bb00 | 0000 0000 

  0x000002f259a171d4: ;   {runtime_call nmethod}
  0x000002f259a171d4: 0000 00e9 | fbff ffff 

  0x000002f259a171dc: ;   {static_stub}
  0x000002f259a171dc: 48bb 0000 | 0000 0000 

  0x000002f259a171e4: ;   {runtime_call nmethod}
  0x000002f259a171e4: 0000 e9fb 

  0x000002f259a171e8: ;   {static_stub}
  0x000002f259a171e8: ffff ff48 | bb00 0000 | 0000 0000 

  0x000002f259a171f4: ;   {runtime_call nmethod}
  0x000002f259a171f4: 00e9 fbff 

  0x000002f259a171f8: ;   {static_stub}
  0x000002f259a171f8: ffff 48bb | 0000 0000 | 0000 0000 

  0x000002f259a17204: ;   {runtime_call nmethod}
  0x000002f259a17204: e9fb ffff 

  0x000002f259a17208: ;   {static_stub}
  0x000002f259a17208: ff48 bb00 | 0000 0000 

  0x000002f259a17210: ;   {runtime_call nmethod}
  0x000002f259a17210: 0000 00e9 | fbff ffff 

  0x000002f259a17218: ;   {static_stub}
  0x000002f259a17218: 48bb 0000 | 0000 0000 

  0x000002f259a17220: ;   {runtime_call nmethod}
  0x000002f259a17220: 0000 e9fb 

  0x000002f259a17224: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000002f259a17224: ffff ffe8 | 34c9 6606 

  0x000002f259a1722c: ;   {external_word}
  0x000002f259a1722c: 48b9 40e5 | 4c17 fa7f | 0000 4883 

  0x000002f259a17238: ;   {runtime_call}
  0x000002f259a17238: e4f0 48b8 | 504d 1317 | fa7f 0000 

  0x000002f259a17244: ;   {section_word}
  0x000002f259a17244: ffd0 f449 | ba47 72a1 | 59f2 0200 

  0x000002f259a17250: ;   {runtime_call DeoptimizationBlob}
  0x000002f259a17250: 0041 52e9 | 28d4 5a06 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002f29a1ccfd0, length=19, elements={
0x000002f25030cf40, 0x000002f26c7f1be0, 0x000002f26c7f31b0, 0x000002f26c7f69f0,
0x000002f26c7f78b0, 0x000002f26c7f9100, 0x000002f26c7fb810, 0x000002f26c807740,
0x000002f2450122e0, 0x000002f2451c1b30, 0x000002f24516aea0, 0x000002f246c988e0,
0x000002f246c97bc0, 0x000002f2482e5810, 0x000002f24cd37f30, 0x000002f248d6c0b0,
0x000002f248d6d460, 0x000002f248d6e180, 0x000002f29752e480
}

Java Threads: ( => current thread )
=>0x000002f25030cf40 JavaThread "Render thread"                     [_thread_in_native, id=12752, stack(0x000000fab2c00000,0x000000fab2d00000) (1024K)]
  0x000002f26c7f1be0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=13848, stack(0x000000fab3400000,0x000000fab3500000) (1024K)]
  0x000002f26c7f31b0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=16316, stack(0x000000fab3500000,0x000000fab3600000) (1024K)]
  0x000002f26c7f69f0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=17932, stack(0x000000fab3600000,0x000000fab3700000) (1024K)]
  0x000002f26c7f78b0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=17796, stack(0x000000fab3700000,0x000000fab3800000) (1024K)]
  0x000002f26c7f9100 JavaThread "Service Thread"             daemon [_thread_blocked, id=15692, stack(0x000000fab3800000,0x000000fab3900000) (1024K)]
  0x000002f26c7fb810 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=16388, stack(0x000000fab3900000,0x000000fab3a00000) (1024K)]
  0x000002f26c807740 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=10984, stack(0x000000fab3a00000,0x000000fab3b00000) (1024K)]
  0x000002f2450122e0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=16272, stack(0x000000fab3b00000,0x000000fab3c00000) (1024K)]
  0x000002f2451c1b30 JavaThread "Notification Thread"        daemon [_thread_blocked, id=18180, stack(0x000000fab3c00000,0x000000fab3d00000) (1024K)]
  0x000002f24516aea0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=18140, stack(0x000000fab3d00000,0x000000fab3e00000) (1024K)]
  0x000002f246c988e0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=16872, stack(0x000000fab4900000,0x000000fab4a00000) (1024K)]
  0x000002f246c97bc0 JavaThread "Timer hack thread"          daemon [_thread_blocked, id=208, stack(0x000000fab3f00000,0x000000fab4000000) (1024K)]
  0x000002f2482e5810 JavaThread "Yggdrasil Key Fetcher"      daemon [_thread_blocked, id=7860, stack(0x000000fab4d00000,0x000000fab4e00000) (1024K)]
  0x000002f24cd37f30 JavaThread "Server thread"                     [_thread_blocked, id=19368, stack(0x000000fab5900000,0x000000fab5a00000) (1024K)]
  0x000002f248d6c0b0 JavaThread "Netty Server IO #0"         daemon [_thread_in_native, id=2416, stack(0x000000fab6500000,0x000000fab6600000) (1024K)]
  0x000002f248d6d460 JavaThread "Netty Local Client IO #0"   daemon [_thread_blocked, id=11220, stack(0x000000fab6600000,0x000000fab6700000) (1024K)]
  0x000002f248d6e180 JavaThread "Netty Server IO #1"         daemon [_thread_in_native, id=17776, stack(0x000000fab6700000,0x000000fab6800000) (1024K)]
  0x000002f29752e480 JavaThread "Sound engine"               daemon [_thread_blocked, id=18528, stack(0x000000fab4b00000,0x000000fab4c00000) (1024K)]
Total: 19

Other Threads:
  0x000002f26c7d8b20 VMThread "VM Thread"                           [id=3960, stack(0x000000fab3300000,0x000000fab3400000) (1024K)]
  0x000002f26c7c58a0 WatcherThread "VM Periodic Task Thread"        [id=16924, stack(0x000000fab3200000,0x000000fab3300000) (1024K)]
  0x000002f250366d10 WorkerThread "GC Thread#0"                     [id=992, stack(0x000000fab2d00000,0x000000fab2e00000) (1024K)]
  0x000002f245d399b0 WorkerThread "GC Thread#1"                     [id=13392, stack(0x000000fab4000000,0x000000fab4100000) (1024K)]
  0x000002f245e133c0 WorkerThread "GC Thread#2"                     [id=14548, stack(0x000000fab4100000,0x000000fab4200000) (1024K)]
  0x000002f245e2e470 WorkerThread "GC Thread#3"                     [id=16204, stack(0x000000fab4200000,0x000000fab4300000) (1024K)]
  0x000002f2465040e0 WorkerThread "GC Thread#4"                     [id=18880, stack(0x000000fab4300000,0x000000fab4400000) (1024K)]
  0x000002f246504490 WorkerThread "GC Thread#5"                     [id=16240, stack(0x000000fab4400000,0x000000fab4500000) (1024K)]
  0x000002f24665c8a0 WorkerThread "GC Thread#6"                     [id=12360, stack(0x000000fab4500000,0x000000fab4600000) (1024K)]
  0x000002f24665db10 WorkerThread "GC Thread#7"                     [id=15516, stack(0x000000fab4600000,0x000000fab4700000) (1024K)]
  0x000002f25037b0e0 ConcurrentGCThread "G1 Main Marker"            [id=18472, stack(0x000000fab2e00000,0x000000fab2f00000) (1024K)]
  0x000002f25037cb10 WorkerThread "G1 Conc#0"                       [id=15452, stack(0x000000fab2f00000,0x000000fab3000000) (1024K)]
  0x000002f2469516c0 WorkerThread "G1 Conc#1"                       [id=18584, stack(0x000000fab4700000,0x000000fab4800000) (1024K)]
  0x000002f2503be900 ConcurrentGCThread "G1 Refine#0"               [id=18064, stack(0x000000fab3000000,0x000000fab3100000) (1024K)]
  0x000002f26c694110 ConcurrentGCThread "G1 Service"                [id=13540, stack(0x000000fab3100000,0x000000fab3200000) (1024K)]
Total: 15

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000085000000, size: 1968 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002f200000000-0x000002f200d70000-0x000002f200d70000), size 14090240, SharedBaseAddress: 0x000002f200000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002f201000000-0x000002f241000000, reserved size: 1073741824
Narrow klass base: 0x000002f200000000, Narrow klass shift: 0, Narrow klass range: 0x41000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 10 size 36 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 7865M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 124M
 Heap Max Capacity: 1968M
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total reserved 2015232K, committed 616448K, used 540809K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 177 young (181248K), 8 survivors (8192K)
 Metaspace       used 135514K, committed 137280K, reserved 1179648K
  class space    used 21390K, committed 22144K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%|HS|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Complete |  0
|   1|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%|HC|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Complete |  0
|   2|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%|HS|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Complete |  0
|   3|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%|HC|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Complete |  0
|   4|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%|HS|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Complete |  0
|   5|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%|HC|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Complete |  0
|   6|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Untracked |  0
|   7|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked |  0
|   8|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked |  0
|   9|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked |  0
|  10|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked |  0
|  11|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked |  0
|  12|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Untracked |  0
|  13|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked |  0
|  14|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked |  0
|  15|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%|HS|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Complete |  0
|  16|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked |  0
|  17|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked |  0
|  18|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked |  0
|  19|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked |  0
|  20|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked |  0
|  21|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked |  0
|  22|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked |  0
|  23|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked |  0
|  24|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked |  0
|  25|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked |  0
|  26|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked |  0
|  27|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked |  0
|  28|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked |  0
|  29|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Untracked |  0
|  30|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked |  0
|  31|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked |  0
|  32|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Untracked |  0
|  33|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Untracked |  0
|  34|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Untracked |  0
|  35|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Untracked |  0
|  36|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087400000| PB 0x0000000087400000| Untracked |  0
|  37|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Untracked |  0
|  38|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked |  0
|  39|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked |  0
|  40|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Untracked |  0
|  41|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Untracked |  0
|  42|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Untracked |  0
|  43|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Untracked |  0
|  44|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Untracked |  0
|  45|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Untracked |  0
|  46|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087e00000| PB 0x0000000087e00000| Untracked |  0
|  47|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000087f00000| PB 0x0000000087f00000| Untracked |  0
|  48|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Untracked |  0
|  49|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Untracked |  0
|  50|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Untracked |  0
|  51|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Untracked |  0
|  52|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Untracked |  0
|  53|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| O|  |TAMS 0x0000000088500000| PB 0x0000000088500000| Untracked |  0
|  54|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088600000| PB 0x0000000088600000| Untracked |  0
|  55|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088700000| PB 0x0000000088700000| Untracked |  0
|  56|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088800000| PB 0x0000000088800000| Untracked |  0
|  57|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Untracked |  0
|  58|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Untracked |  0
|  59|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Untracked |  0
|  60|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Untracked |  0
|  61|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Untracked |  0
|  62|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Untracked |  0
|  63|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| O|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Untracked |  0
|  64|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Untracked |  0
|  65|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Untracked |  0
|  66|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Untracked |  0
|  67|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Untracked |  0
|  68|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Untracked |  0
|  69|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Untracked |  0
|  70|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Untracked |  0
|  71|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Untracked |  0
|  72|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Untracked |  0
|  73|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked |  0
|  74|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| O|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Untracked |  0
|  75|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Untracked |  0
|  76|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Untracked |  0
|  77|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked |  0
|  78|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Untracked |  0
|  79|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked |  0
|  80|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Untracked |  0
|  81|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| O|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked |  0
|  82|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| O|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Untracked |  0
|  83|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%| O|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Untracked |  0
|  84|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| O|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Untracked |  0
|  85|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked |  0
|  86|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| O|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked |  0
|  87|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| O|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Untracked |  0
|  88|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Untracked |  0
|  89|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Untracked |  0
|  90|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Untracked |  0
|  91|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| O|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Untracked |  0
|  92|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked |  0
|  93|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%|HS|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Complete |  0
|  94|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%|HC|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Complete |  0
|  95|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%|HC|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Complete |  0
|  96|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%|HC|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Complete |  0
|  97|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%|HC|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Complete |  0
|  98|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%|HC|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Complete |  0
|  99|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%|HC|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Complete |  0
| 100|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%|HC|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Complete |  0
| 101|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%|HC|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Complete |  0
| 102|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%|HC|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Complete |  0
| 103|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%|HC|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Complete |  0
| 104|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| O|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked |  0
| 105|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%| O|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked |  0
| 106|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%| O|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked |  0
| 107|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| O|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked |  0
| 108|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| O|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked |  0
| 109|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| O|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked |  0
| 110|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked |  0
| 111|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| O|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked |  0
| 112|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| O|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked |  0
| 113|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| O|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked |  0
| 114|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| O|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked |  0
| 115|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked |  0
| 116|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| O|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked |  0
| 117|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| O|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked |  0
| 118|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| O|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked |  0
| 119|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| O|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked |  0
| 120|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| O|  |TAMS 0x000000008c800000| PB 0x000000008c800000| Untracked |  0
| 121|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| O|  |TAMS 0x000000008c900000| PB 0x000000008c900000| Untracked |  0
| 122|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| O|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Untracked |  0
| 123|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| O|  |TAMS 0x000000008cb00000| PB 0x000000008cb00000| Untracked |  0
| 124|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| O|  |TAMS 0x000000008cc00000| PB 0x000000008cc00000| Untracked |  0
| 125|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| O|  |TAMS 0x000000008cd00000| PB 0x000000008cd00000| Untracked |  0
| 126|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| O|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Untracked |  0
| 127|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| O|  |TAMS 0x000000008cf00000| PB 0x000000008cf00000| Untracked |  0
| 128|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| O|  |TAMS 0x000000008d000000| PB 0x000000008d000000| Untracked |  0
| 129|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| O|  |TAMS 0x000000008d100000| PB 0x000000008d100000| Untracked |  0
| 130|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| O|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked |  0
| 131|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| O|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked |  0
| 132|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| O|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked |  0
| 133|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| O|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked |  0
| 134|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| O|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked |  0
| 135|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| O|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked |  0
| 136|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| O|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Untracked |  0
| 137|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| O|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked |  0
| 138|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| O|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Untracked |  0
| 139|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| O|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked |  0
| 140|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| O|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Untracked |  0
| 141|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| O|  |TAMS 0x000000008dd00000| PB 0x000000008dd00000| Untracked |  0
| 142|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| O|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked |  0
| 143|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| O|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Untracked |  0
| 144|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| O|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked |  0
| 145|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| O|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked |  0
| 146|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| O|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked |  0
| 147|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| O|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked |  0
| 148|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| O|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Untracked |  0
| 149|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| O|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Untracked |  0
| 150|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| O|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Untracked |  0
| 151|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| O|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Untracked |  0
| 152|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| O|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Untracked |  0
| 153|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| O|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Untracked |  0
| 154|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| O|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Untracked |  0
| 155|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| O|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Untracked |  0
| 156|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| O|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Untracked |  0
| 157|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| O|  |TAMS 0x000000008ed00000| PB 0x000000008ed00000| Untracked |  0
| 158|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| O|  |TAMS 0x000000008ee00000| PB 0x000000008ee00000| Untracked |  0
| 159|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| O|  |TAMS 0x000000008ef00000| PB 0x000000008ef00000| Untracked |  0
| 160|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| O|  |TAMS 0x000000008f000000| PB 0x000000008f000000| Untracked |  0
| 161|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%| O|  |TAMS 0x000000008f100000| PB 0x000000008f100000| Untracked |  0
| 162|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| O|  |TAMS 0x000000008f200000| PB 0x000000008f200000| Untracked |  0
| 163|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| O|  |TAMS 0x000000008f300000| PB 0x000000008f300000| Untracked |  0
| 164|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| O|  |TAMS 0x000000008f400000| PB 0x000000008f400000| Untracked |  0
| 165|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| O|  |TAMS 0x000000008f500000| PB 0x000000008f500000| Untracked |  0
| 166|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| O|  |TAMS 0x000000008f600000| PB 0x000000008f600000| Untracked |  0
| 167|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| O|  |TAMS 0x000000008f700000| PB 0x000000008f700000| Untracked |  0
| 168|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| O|  |TAMS 0x000000008f800000| PB 0x000000008f800000| Untracked |  0
| 169|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| O|  |TAMS 0x000000008f900000| PB 0x000000008f900000| Untracked |  0
| 170|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| O|  |TAMS 0x000000008fa00000| PB 0x000000008fa00000| Untracked |  0
| 171|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| O|  |TAMS 0x000000008fb00000| PB 0x000000008fb00000| Untracked |  0
| 172|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%|HS|  |TAMS 0x000000008fc00000| PB 0x000000008fc00000| Complete |  0
| 173|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%|HC|  |TAMS 0x000000008fd00000| PB 0x000000008fd00000| Complete |  0
| 174|0x000000008fe00000, 0x000000008ff00000, 0x000000008ff00000|100%|HS|  |TAMS 0x000000008fe00000| PB 0x000000008fe00000| Complete |  0
| 175|0x000000008ff00000, 0x0000000090000000, 0x0000000090000000|100%|HC|  |TAMS 0x000000008ff00000| PB 0x000000008ff00000| Complete |  0
| 176|0x0000000090000000, 0x0000000090100000, 0x0000000090100000|100%| O|  |TAMS 0x0000000090000000| PB 0x0000000090000000| Untracked |  0
| 177|0x0000000090100000, 0x0000000090200000, 0x0000000090200000|100%| O|  |TAMS 0x0000000090100000| PB 0x0000000090100000| Untracked |  0
| 178|0x0000000090200000, 0x0000000090300000, 0x0000000090300000|100%| O|  |TAMS 0x0000000090200000| PB 0x0000000090200000| Untracked |  0
| 179|0x0000000090300000, 0x0000000090400000, 0x0000000090400000|100%| O|  |TAMS 0x0000000090300000| PB 0x0000000090300000| Untracked |  0
| 180|0x0000000090400000, 0x0000000090500000, 0x0000000090500000|100%| O|  |TAMS 0x0000000090400000| PB 0x0000000090400000| Untracked |  0
| 181|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%| O|  |TAMS 0x0000000090500000| PB 0x0000000090500000| Untracked |  0
| 182|0x0000000090600000, 0x0000000090700000, 0x0000000090700000|100%| O|  |TAMS 0x0000000090600000| PB 0x0000000090600000| Untracked |  0
| 183|0x0000000090700000, 0x0000000090800000, 0x0000000090800000|100%| O|  |TAMS 0x0000000090700000| PB 0x0000000090700000| Untracked |  0
| 184|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%| O|  |TAMS 0x0000000090800000| PB 0x0000000090800000| Untracked |  0
| 185|0x0000000090900000, 0x0000000090a00000, 0x0000000090a00000|100%| O|  |TAMS 0x0000000090900000| PB 0x0000000090900000| Untracked |  0
| 186|0x0000000090a00000, 0x0000000090b00000, 0x0000000090b00000|100%| O|  |TAMS 0x0000000090a00000| PB 0x0000000090a00000| Untracked |  0
| 187|0x0000000090b00000, 0x0000000090c00000, 0x0000000090c00000|100%| O|  |TAMS 0x0000000090b00000| PB 0x0000000090b00000| Untracked |  0
| 188|0x0000000090c00000, 0x0000000090d00000, 0x0000000090d00000|100%| O|  |TAMS 0x0000000090c00000| PB 0x0000000090c00000| Untracked |  0
| 189|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%| O|  |TAMS 0x0000000090d00000| PB 0x0000000090d00000| Untracked |  0
| 190|0x0000000090e00000, 0x0000000090f00000, 0x0000000090f00000|100%| O|  |TAMS 0x0000000090e00000| PB 0x0000000090e00000| Untracked |  0
| 191|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%| O|  |TAMS 0x0000000090f00000| PB 0x0000000090f00000| Untracked |  0
| 192|0x0000000091000000, 0x0000000091100000, 0x0000000091100000|100%| O|  |TAMS 0x0000000091000000| PB 0x0000000091000000| Untracked |  0
| 193|0x0000000091100000, 0x0000000091200000, 0x0000000091200000|100%| O|  |TAMS 0x0000000091100000| PB 0x0000000091100000| Untracked |  0
| 194|0x0000000091200000, 0x0000000091300000, 0x0000000091300000|100%| O|  |TAMS 0x0000000091200000| PB 0x0000000091200000| Untracked |  0
| 195|0x0000000091300000, 0x0000000091400000, 0x0000000091400000|100%| O|  |TAMS 0x0000000091300000| PB 0x0000000091300000| Untracked |  0
| 196|0x0000000091400000, 0x0000000091500000, 0x0000000091500000|100%| O|  |TAMS 0x0000000091400000| PB 0x0000000091400000| Untracked |  0
| 197|0x0000000091500000, 0x0000000091600000, 0x0000000091600000|100%| O|  |TAMS 0x0000000091500000| PB 0x0000000091500000| Untracked |  0
| 198|0x0000000091600000, 0x0000000091700000, 0x0000000091700000|100%| O|  |TAMS 0x0000000091600000| PB 0x0000000091600000| Untracked |  0
| 199|0x0000000091700000, 0x0000000091800000, 0x0000000091800000|100%| O|  |TAMS 0x0000000091700000| PB 0x0000000091700000| Untracked |  0
| 200|0x0000000091800000, 0x0000000091900000, 0x0000000091900000|100%| O|  |TAMS 0x0000000091800000| PB 0x0000000091800000| Untracked |  0
| 201|0x0000000091900000, 0x0000000091a00000, 0x0000000091a00000|100%| O|  |TAMS 0x0000000091900000| PB 0x0000000091900000| Untracked |  0
| 202|0x0000000091a00000, 0x0000000091b00000, 0x0000000091b00000|100%| O|  |TAMS 0x0000000091a00000| PB 0x0000000091a00000| Untracked |  0
| 203|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%| O|  |TAMS 0x0000000091b00000| PB 0x0000000091b00000| Untracked |  0
| 204|0x0000000091c00000, 0x0000000091d00000, 0x0000000091d00000|100%| O|  |TAMS 0x0000000091c00000| PB 0x0000000091c00000| Untracked |  0
| 205|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%| O|  |TAMS 0x0000000091d00000| PB 0x0000000091d00000| Untracked |  0
| 206|0x0000000091e00000, 0x0000000091f00000, 0x0000000091f00000|100%| O|  |TAMS 0x0000000091e00000| PB 0x0000000091e00000| Untracked |  0
| 207|0x0000000091f00000, 0x0000000092000000, 0x0000000092000000|100%| O|  |TAMS 0x0000000091f00000| PB 0x0000000091f00000| Untracked |  0
| 208|0x0000000092000000, 0x0000000092100000, 0x0000000092100000|100%| O|  |TAMS 0x0000000092000000| PB 0x0000000092000000| Untracked |  0
| 209|0x0000000092100000, 0x0000000092200000, 0x0000000092200000|100%| O|  |TAMS 0x0000000092100000| PB 0x0000000092100000| Untracked |  0
| 210|0x0000000092200000, 0x0000000092300000, 0x0000000092300000|100%|HS|  |TAMS 0x0000000092200000| PB 0x0000000092200000| Complete |  0
| 211|0x0000000092300000, 0x0000000092400000, 0x0000000092400000|100%| O|  |TAMS 0x0000000092300000| PB 0x0000000092300000| Untracked |  0
| 212|0x0000000092400000, 0x0000000092500000, 0x0000000092500000|100%| O|  |TAMS 0x0000000092400000| PB 0x0000000092400000| Untracked |  0
| 213|0x0000000092500000, 0x0000000092600000, 0x0000000092600000|100%| O|  |TAMS 0x0000000092500000| PB 0x0000000092500000| Untracked |  0
| 214|0x0000000092600000, 0x0000000092700000, 0x0000000092700000|100%| O|  |TAMS 0x0000000092600000| PB 0x0000000092600000| Untracked |  0
| 215|0x0000000092700000, 0x0000000092800000, 0x0000000092800000|100%|HS|  |TAMS 0x0000000092700000| PB 0x0000000092700000| Complete |  0
| 216|0x0000000092800000, 0x0000000092900000, 0x0000000092900000|100%| O|  |TAMS 0x0000000092800000| PB 0x0000000092800000| Untracked |  0
| 217|0x0000000092900000, 0x0000000092a00000, 0x0000000092a00000|100%| O|  |TAMS 0x0000000092900000| PB 0x0000000092900000| Untracked |  0
| 218|0x0000000092a00000, 0x0000000092b00000, 0x0000000092b00000|100%| O|  |TAMS 0x0000000092a00000| PB 0x0000000092a00000| Untracked |  0
| 219|0x0000000092b00000, 0x0000000092c00000, 0x0000000092c00000|100%| O|  |TAMS 0x0000000092b00000| PB 0x0000000092b00000| Untracked |  0
| 220|0x0000000092c00000, 0x0000000092d00000, 0x0000000092d00000|100%| O|  |TAMS 0x0000000092c00000| PB 0x0000000092c00000| Untracked |  0
| 221|0x0000000092d00000, 0x0000000092e00000, 0x0000000092e00000|100%| O|  |TAMS 0x0000000092d00000| PB 0x0000000092d00000| Untracked |  0
| 222|0x0000000092e00000, 0x0000000092f00000, 0x0000000092f00000|100%| O|  |TAMS 0x0000000092e00000| PB 0x0000000092e00000| Untracked |  0
| 223|0x0000000092f00000, 0x0000000093000000, 0x0000000093000000|100%| O|  |TAMS 0x0000000092f00000| PB 0x0000000092f00000| Untracked |  0
| 224|0x0000000093000000, 0x0000000093100000, 0x0000000093100000|100%| O|  |TAMS 0x0000000093000000| PB 0x0000000093000000| Untracked |  0
| 225|0x0000000093100000, 0x0000000093200000, 0x0000000093200000|100%| O|  |TAMS 0x0000000093100000| PB 0x0000000093100000| Untracked |  0
| 226|0x0000000093200000, 0x0000000093300000, 0x0000000093300000|100%| O|  |TAMS 0x0000000093200000| PB 0x0000000093200000| Untracked |  0
| 227|0x0000000093300000, 0x0000000093400000, 0x0000000093400000|100%| O|  |TAMS 0x0000000093300000| PB 0x0000000093300000| Untracked |  0
| 228|0x0000000093400000, 0x0000000093500000, 0x0000000093500000|100%| O|  |TAMS 0x0000000093400000| PB 0x0000000093400000| Untracked |  0
| 229|0x0000000093500000, 0x0000000093600000, 0x0000000093600000|100%| O|  |TAMS 0x0000000093500000| PB 0x0000000093500000| Untracked |  0
| 230|0x0000000093600000, 0x0000000093700000, 0x0000000093700000|100%| O|  |TAMS 0x0000000093600000| PB 0x0000000093600000| Untracked |  0
| 231|0x0000000093700000, 0x0000000093800000, 0x0000000093800000|100%| O|  |TAMS 0x0000000093700000| PB 0x0000000093700000| Untracked |  0
| 232|0x0000000093800000, 0x0000000093900000, 0x0000000093900000|100%| O|  |TAMS 0x0000000093800000| PB 0x0000000093800000| Untracked |  0
| 233|0x0000000093900000, 0x0000000093a00000, 0x0000000093a00000|100%| O|  |TAMS 0x0000000093900000| PB 0x0000000093900000| Untracked |  0
| 234|0x0000000093a00000, 0x0000000093b00000, 0x0000000093b00000|100%| O|  |TAMS 0x0000000093a00000| PB 0x0000000093a00000| Untracked |  0
| 235|0x0000000093b00000, 0x0000000093c00000, 0x0000000093c00000|100%| O|  |TAMS 0x0000000093b00000| PB 0x0000000093b00000| Untracked |  0
| 236|0x0000000093c00000, 0x0000000093d00000, 0x0000000093d00000|100%| O|  |TAMS 0x0000000093c00000| PB 0x0000000093c00000| Untracked |  0
| 237|0x0000000093d00000, 0x0000000093e00000, 0x0000000093e00000|100%| O|  |TAMS 0x0000000093d00000| PB 0x0000000093d00000| Untracked |  0
| 238|0x0000000093e00000, 0x0000000093f00000, 0x0000000093f00000|100%| O|  |TAMS 0x0000000093e00000| PB 0x0000000093e00000| Untracked |  0
| 239|0x0000000093f00000, 0x0000000094000000, 0x0000000094000000|100%| O|  |TAMS 0x0000000093f00000| PB 0x0000000093f00000| Untracked |  0
| 240|0x0000000094000000, 0x0000000094100000, 0x0000000094100000|100%| O|  |TAMS 0x0000000094000000| PB 0x0000000094000000| Untracked |  0
| 241|0x0000000094100000, 0x0000000094200000, 0x0000000094200000|100%| O|  |TAMS 0x0000000094100000| PB 0x0000000094100000| Untracked |  0
| 242|0x0000000094200000, 0x0000000094300000, 0x0000000094300000|100%| O|  |TAMS 0x0000000094200000| PB 0x0000000094200000| Untracked |  0
| 243|0x0000000094300000, 0x0000000094400000, 0x0000000094400000|100%| O|Cm|TAMS 0x0000000094300000| PB 0x0000000094300000| Complete |  0
| 244|0x0000000094400000, 0x0000000094500000, 0x0000000094500000|100%| O|  |TAMS 0x0000000094400000| PB 0x0000000094400000| Untracked |  0
| 245|0x0000000094500000, 0x0000000094600000, 0x0000000094600000|100%| O|  |TAMS 0x0000000094500000| PB 0x0000000094500000| Untracked |  0
| 246|0x0000000094600000, 0x0000000094700000, 0x0000000094700000|100%| O|  |TAMS 0x0000000094600000| PB 0x0000000094600000| Untracked |  0
| 247|0x0000000094700000, 0x0000000094800000, 0x0000000094800000|100%| O|  |TAMS 0x0000000094700000| PB 0x0000000094700000| Untracked |  0
| 248|0x0000000094800000, 0x0000000094900000, 0x0000000094900000|100%| O|  |TAMS 0x0000000094800000| PB 0x0000000094800000| Untracked |  0
| 249|0x0000000094900000, 0x0000000094a00000, 0x0000000094a00000|100%| O|  |TAMS 0x0000000094900000| PB 0x0000000094900000| Untracked |  0
| 250|0x0000000094a00000, 0x0000000094b00000, 0x0000000094b00000|100%| O|  |TAMS 0x0000000094a00000| PB 0x0000000094a00000| Untracked |  0
| 251|0x0000000094b00000, 0x0000000094c00000, 0x0000000094c00000|100%| O|  |TAMS 0x0000000094b00000| PB 0x0000000094b00000| Untracked |  0
| 252|0x0000000094c00000, 0x0000000094d00000, 0x0000000094d00000|100%| O|  |TAMS 0x0000000094c00000| PB 0x0000000094c00000| Untracked |  0
| 253|0x0000000094d00000, 0x0000000094e00000, 0x0000000094e00000|100%| O|  |TAMS 0x0000000094d00000| PB 0x0000000094d00000| Untracked |  0
| 254|0x0000000094e00000, 0x0000000094f00000, 0x0000000094f00000|100%| O|  |TAMS 0x0000000094e00000| PB 0x0000000094e00000| Untracked |  0
| 255|0x0000000094f00000, 0x0000000095000000, 0x0000000095000000|100%| O|  |TAMS 0x0000000094f00000| PB 0x0000000094f00000| Untracked |  0
| 256|0x0000000095000000, 0x0000000095100000, 0x0000000095100000|100%| O|  |TAMS 0x0000000095000000| PB 0x0000000095000000| Untracked |  0
| 257|0x0000000095100000, 0x0000000095200000, 0x0000000095200000|100%| O|  |TAMS 0x0000000095100000| PB 0x0000000095100000| Untracked |  0
| 258|0x0000000095200000, 0x0000000095300000, 0x0000000095300000|100%| O|  |TAMS 0x0000000095200000| PB 0x0000000095200000| Untracked |  0
| 259|0x0000000095300000, 0x0000000095400000, 0x0000000095400000|100%| O|  |TAMS 0x0000000095300000| PB 0x0000000095300000| Untracked |  0
| 260|0x0000000095400000, 0x0000000095500000, 0x0000000095500000|100%| O|  |TAMS 0x0000000095400000| PB 0x0000000095400000| Untracked |  0
| 261|0x0000000095500000, 0x0000000095600000, 0x0000000095600000|100%| O|  |TAMS 0x0000000095500000| PB 0x0000000095500000| Untracked |  0
| 262|0x0000000095600000, 0x0000000095700000, 0x0000000095700000|100%| O|  |TAMS 0x0000000095600000| PB 0x0000000095600000| Untracked |  0
| 263|0x0000000095700000, 0x0000000095800000, 0x0000000095800000|100%| O|  |TAMS 0x0000000095700000| PB 0x0000000095700000| Untracked |  0
| 264|0x0000000095800000, 0x0000000095900000, 0x0000000095900000|100%| O|  |TAMS 0x0000000095800000| PB 0x0000000095800000| Untracked |  0
| 265|0x0000000095900000, 0x0000000095a00000, 0x0000000095a00000|100%| O|  |TAMS 0x0000000095900000| PB 0x0000000095900000| Untracked |  0
| 266|0x0000000095a00000, 0x0000000095b00000, 0x0000000095b00000|100%| O|  |TAMS 0x0000000095a00000| PB 0x0000000095a00000| Untracked |  0
| 267|0x0000000095b00000, 0x0000000095c00000, 0x0000000095c00000|100%| O|  |TAMS 0x0000000095b00000| PB 0x0000000095b00000| Untracked |  0
| 268|0x0000000095c00000, 0x0000000095d00000, 0x0000000095d00000|100%| O|  |TAMS 0x0000000095c00000| PB 0x0000000095c00000| Untracked |  0
| 269|0x0000000095d00000, 0x0000000095e00000, 0x0000000095e00000|100%| O|  |TAMS 0x0000000095d00000| PB 0x0000000095d00000| Untracked |  0
| 270|0x0000000095e00000, 0x0000000095f00000, 0x0000000095f00000|100%| O|  |TAMS 0x0000000095e00000| PB 0x0000000095e00000| Untracked |  0
| 271|0x0000000095f00000, 0x0000000096000000, 0x0000000096000000|100%| O|  |TAMS 0x0000000095f00000| PB 0x0000000095f00000| Untracked |  0
| 272|0x0000000096000000, 0x0000000096100000, 0x0000000096100000|100%| O|Cm|TAMS 0x0000000096000000| PB 0x0000000096000000| Complete |  0
| 273|0x0000000096100000, 0x0000000096200000, 0x0000000096200000|100%| O|  |TAMS 0x0000000096100000| PB 0x0000000096100000| Untracked |  0
| 274|0x0000000096200000, 0x0000000096300000, 0x0000000096300000|100%| O|Cm|TAMS 0x0000000096200000| PB 0x0000000096200000| Complete |  0
| 275|0x0000000096300000, 0x0000000096400000, 0x0000000096400000|100%| O|  |TAMS 0x0000000096300000| PB 0x0000000096300000| Untracked |  0
| 276|0x0000000096400000, 0x0000000096500000, 0x0000000096500000|100%| O|  |TAMS 0x0000000096400000| PB 0x0000000096400000| Untracked |  0
| 277|0x0000000096500000, 0x0000000096600000, 0x0000000096600000|100%| O|  |TAMS 0x0000000096500000| PB 0x0000000096500000| Untracked |  0
| 278|0x0000000096600000, 0x0000000096700000, 0x0000000096700000|100%| O|  |TAMS 0x0000000096600000| PB 0x0000000096600000| Untracked |  0
| 279|0x0000000096700000, 0x0000000096800000, 0x0000000096800000|100%| O|  |TAMS 0x0000000096700000| PB 0x0000000096700000| Untracked |  0
| 280|0x0000000096800000, 0x0000000096900000, 0x0000000096900000|100%| O|  |TAMS 0x0000000096800000| PB 0x0000000096800000| Untracked |  0
| 281|0x0000000096900000, 0x0000000096a00000, 0x0000000096a00000|100%|HS|  |TAMS 0x0000000096900000| PB 0x0000000096900000| Complete |  0
| 282|0x0000000096a00000, 0x0000000096b00000, 0x0000000096b00000|100%|HC|  |TAMS 0x0000000096a00000| PB 0x0000000096a00000| Complete |  0
| 283|0x0000000096b00000, 0x0000000096c00000, 0x0000000096c00000|100%|HC|  |TAMS 0x0000000096b00000| PB 0x0000000096b00000| Complete |  0
| 284|0x0000000096c00000, 0x0000000096d00000, 0x0000000096d00000|100%|HC|  |TAMS 0x0000000096c00000| PB 0x0000000096c00000| Complete |  0
| 285|0x0000000096d00000, 0x0000000096e00000, 0x0000000096e00000|100%|HC|  |TAMS 0x0000000096d00000| PB 0x0000000096d00000| Complete |  0
| 286|0x0000000096e00000, 0x0000000096f00000, 0x0000000096f00000|100%| O|  |TAMS 0x0000000096e00000| PB 0x0000000096e00000| Untracked |  0
| 287|0x0000000096f00000, 0x0000000097000000, 0x0000000097000000|100%| O|  |TAMS 0x0000000096f00000| PB 0x0000000096f00000| Untracked |  0
| 288|0x0000000097000000, 0x0000000097100000, 0x0000000097100000|100%|HS|  |TAMS 0x0000000097000000| PB 0x0000000097000000| Complete |  0
| 289|0x0000000097100000, 0x0000000097200000, 0x0000000097200000|100%| O|  |TAMS 0x0000000097100000| PB 0x0000000097100000| Untracked |  0
| 290|0x0000000097200000, 0x0000000097300000, 0x0000000097300000|100%| O|  |TAMS 0x0000000097200000| PB 0x0000000097200000| Untracked |  0
| 291|0x0000000097300000, 0x0000000097400000, 0x0000000097400000|100%| O|  |TAMS 0x0000000097300000| PB 0x0000000097300000| Untracked |  0
| 292|0x0000000097400000, 0x0000000097500000, 0x0000000097500000|100%| O|  |TAMS 0x0000000097400000| PB 0x0000000097400000| Untracked |  0
| 293|0x0000000097500000, 0x0000000097600000, 0x0000000097600000|100%| O|  |TAMS 0x0000000097500000| PB 0x0000000097500000| Untracked |  0
| 294|0x0000000097600000, 0x0000000097700000, 0x0000000097700000|100%| O|  |TAMS 0x0000000097600000| PB 0x0000000097600000| Untracked |  0
| 295|0x0000000097700000, 0x0000000097800000, 0x0000000097800000|100%| O|  |TAMS 0x0000000097700000| PB 0x0000000097700000| Untracked |  0
| 296|0x0000000097800000, 0x0000000097900000, 0x0000000097900000|100%|HS|  |TAMS 0x0000000097800000| PB 0x0000000097800000| Complete |  0
| 297|0x0000000097900000, 0x0000000097a00000, 0x0000000097a00000|100%|HC|  |TAMS 0x0000000097900000| PB 0x0000000097900000| Complete |  0
| 298|0x0000000097a00000, 0x0000000097b00000, 0x0000000097b00000|100%|HC|  |TAMS 0x0000000097a00000| PB 0x0000000097a00000| Complete |  0
| 299|0x0000000097b00000, 0x0000000097c00000, 0x0000000097c00000|100%|HC|  |TAMS 0x0000000097b00000| PB 0x0000000097b00000| Complete |  0
| 300|0x0000000097c00000, 0x0000000097d00000, 0x0000000097d00000|100%|HC|  |TAMS 0x0000000097c00000| PB 0x0000000097c00000| Complete |  0
| 301|0x0000000097d00000, 0x0000000097e00000, 0x0000000097e00000|100%| O|  |TAMS 0x0000000097d00000| PB 0x0000000097d00000| Untracked |  0
| 302|0x0000000097e00000, 0x0000000097f00000, 0x0000000097f00000|100%| O|  |TAMS 0x0000000097e00000| PB 0x0000000097e00000| Untracked |  0
| 303|0x0000000097f00000, 0x0000000098000000, 0x0000000098000000|100%| O|  |TAMS 0x0000000097f00000| PB 0x0000000097f00000| Untracked |  0
| 304|0x0000000098000000, 0x0000000098100000, 0x0000000098100000|100%| O|  |TAMS 0x0000000098000000| PB 0x0000000098000000| Untracked |  0
| 305|0x0000000098100000, 0x0000000098200000, 0x0000000098200000|100%| O|  |TAMS 0x0000000098100000| PB 0x0000000098100000| Untracked |  0
| 306|0x0000000098200000, 0x0000000098300000, 0x0000000098300000|100%| O|  |TAMS 0x0000000098200000| PB 0x0000000098200000| Untracked |  0
| 307|0x0000000098300000, 0x0000000098400000, 0x0000000098400000|100%| O|  |TAMS 0x0000000098300000| PB 0x0000000098300000| Untracked |  0
| 308|0x0000000098400000, 0x0000000098500000, 0x0000000098500000|100%| O|  |TAMS 0x0000000098400000| PB 0x0000000098400000| Untracked |  0
| 309|0x0000000098500000, 0x0000000098600000, 0x0000000098600000|100%| O|  |TAMS 0x0000000098500000| PB 0x0000000098500000| Untracked |  0
| 310|0x0000000098600000, 0x0000000098700000, 0x0000000098700000|100%| O|Cm|TAMS 0x0000000098600000| PB 0x0000000098600000| Complete |  0
| 311|0x0000000098700000, 0x0000000098800000, 0x0000000098800000|100%| O|  |TAMS 0x0000000098700000| PB 0x0000000098700000| Untracked |  0
| 312|0x0000000098800000, 0x0000000098900000, 0x0000000098900000|100%| O|  |TAMS 0x0000000098800000| PB 0x0000000098800000| Untracked |  0
| 313|0x0000000098900000, 0x0000000098a00000, 0x0000000098a00000|100%| O|  |TAMS 0x0000000098900000| PB 0x0000000098900000| Untracked |  0
| 314|0x0000000098a00000, 0x0000000098b00000, 0x0000000098b00000|100%| O|  |TAMS 0x0000000098a00000| PB 0x0000000098a00000| Untracked |  0
| 315|0x0000000098b00000, 0x0000000098c00000, 0x0000000098c00000|100%| O|  |TAMS 0x0000000098b00000| PB 0x0000000098b00000| Untracked |  0
| 316|0x0000000098c00000, 0x0000000098d00000, 0x0000000098d00000|100%| O|  |TAMS 0x0000000098c00000| PB 0x0000000098c00000| Untracked |  0
| 317|0x0000000098d00000, 0x0000000098e00000, 0x0000000098e00000|100%| O|  |TAMS 0x0000000098d00000| PB 0x0000000098d00000| Untracked |  0
| 318|0x0000000098e00000, 0x0000000098f00000, 0x0000000098f00000|100%| O|  |TAMS 0x0000000098e00000| PB 0x0000000098e00000| Untracked |  0
| 319|0x0000000098f00000, 0x0000000099000000, 0x0000000099000000|100%| O|  |TAMS 0x0000000098f00000| PB 0x0000000098f00000| Untracked |  0
| 320|0x0000000099000000, 0x0000000099100000, 0x0000000099100000|100%| O|  |TAMS 0x0000000099000000| PB 0x0000000099000000| Untracked |  0
| 321|0x0000000099100000, 0x0000000099200000, 0x0000000099200000|100%| O|  |TAMS 0x0000000099100000| PB 0x0000000099100000| Untracked |  0
| 322|0x0000000099200000, 0x0000000099300000, 0x0000000099300000|100%| O|  |TAMS 0x0000000099200000| PB 0x0000000099200000| Untracked |  0
| 323|0x0000000099300000, 0x0000000099400000, 0x0000000099400000|100%| O|  |TAMS 0x0000000099300000| PB 0x0000000099300000| Untracked |  0
| 324|0x0000000099400000, 0x0000000099500000, 0x0000000099500000|100%| O|  |TAMS 0x0000000099400000| PB 0x0000000099400000| Untracked |  0
| 325|0x0000000099500000, 0x0000000099600000, 0x0000000099600000|100%| O|  |TAMS 0x0000000099500000| PB 0x0000000099500000| Untracked |  0
| 326|0x0000000099600000, 0x0000000099700000, 0x0000000099700000|100%| O|  |TAMS 0x0000000099600000| PB 0x0000000099600000| Untracked |  0
| 327|0x0000000099700000, 0x0000000099800000, 0x0000000099800000|100%| O|  |TAMS 0x0000000099700000| PB 0x0000000099700000| Untracked |  0
| 328|0x0000000099800000, 0x0000000099900000, 0x0000000099900000|100%| O|  |TAMS 0x0000000099800000| PB 0x0000000099800000| Untracked |  0
| 329|0x0000000099900000, 0x0000000099a00000, 0x0000000099a00000|100%| O|  |TAMS 0x0000000099900000| PB 0x0000000099900000| Untracked |  0
| 330|0x0000000099a00000, 0x0000000099b00000, 0x0000000099b00000|100%| O|  |TAMS 0x0000000099a00000| PB 0x0000000099a00000| Untracked |  0
| 331|0x0000000099b00000, 0x0000000099c00000, 0x0000000099c00000|100%| O|  |TAMS 0x0000000099b00000| PB 0x0000000099b00000| Untracked |  0
| 332|0x0000000099c00000, 0x0000000099d00000, 0x0000000099d00000|100%| O|  |TAMS 0x0000000099c00000| PB 0x0000000099c00000| Untracked |  0
| 333|0x0000000099d00000, 0x0000000099e00000, 0x0000000099e00000|100%| O|  |TAMS 0x0000000099d00000| PB 0x0000000099d00000| Untracked |  0
| 334|0x0000000099e00000, 0x0000000099e1a5d8, 0x0000000099f00000| 10%| O|  |TAMS 0x0000000099e00000| PB 0x0000000099e00000| Untracked |  0
| 335|0x0000000099f00000, 0x000000009a000000, 0x000000009a000000|100%| O|  |TAMS 0x0000000099f00000| PB 0x0000000099f00000| Untracked |  0
| 336|0x000000009a000000, 0x000000009a100000, 0x000000009a100000|100%| O|  |TAMS 0x000000009a000000| PB 0x000000009a000000| Untracked |  0
| 337|0x000000009a100000, 0x000000009a200000, 0x000000009a200000|100%| O|  |TAMS 0x000000009a100000| PB 0x000000009a100000| Untracked |  0
| 338|0x000000009a200000, 0x000000009a300000, 0x000000009a300000|100%| O|  |TAMS 0x000000009a200000| PB 0x000000009a200000| Untracked |  0
| 339|0x000000009a300000, 0x000000009a400000, 0x000000009a400000|100%| O|  |TAMS 0x000000009a300000| PB 0x000000009a300000| Untracked |  0
| 340|0x000000009a400000, 0x000000009a500000, 0x000000009a500000|100%| O|  |TAMS 0x000000009a400000| PB 0x000000009a400000| Untracked |  0
| 341|0x000000009a500000, 0x000000009a600000, 0x000000009a600000|100%| O|  |TAMS 0x000000009a500000| PB 0x000000009a500000| Untracked |  0
| 342|0x000000009a600000, 0x000000009a700000, 0x000000009a700000|100%| O|  |TAMS 0x000000009a600000| PB 0x000000009a600000| Untracked |  0
| 343|0x000000009a700000, 0x000000009a800000, 0x000000009a800000|100%| O|  |TAMS 0x000000009a700000| PB 0x000000009a700000| Untracked |  0
| 344|0x000000009a800000, 0x000000009a900000, 0x000000009a900000|100%| O|Cm|TAMS 0x000000009a800000| PB 0x000000009a800000| Complete |  0
| 345|0x000000009a900000, 0x000000009a900000, 0x000000009aa00000|  0%| F|  |TAMS 0x000000009a900000| PB 0x000000009a900000| Untracked |  0
| 346|0x000000009aa00000, 0x000000009aa00000, 0x000000009ab00000|  0%| F|  |TAMS 0x000000009aa00000| PB 0x000000009aa00000| Untracked |  0
| 347|0x000000009ab00000, 0x000000009ab00000, 0x000000009ac00000|  0%| F|  |TAMS 0x000000009ab00000| PB 0x000000009ab00000| Untracked |  0
| 348|0x000000009ac00000, 0x000000009ac00000, 0x000000009ad00000|  0%| F|  |TAMS 0x000000009ac00000| PB 0x000000009ac00000| Untracked |  0
| 349|0x000000009ad00000, 0x000000009ad00000, 0x000000009ae00000|  0%| F|  |TAMS 0x000000009ad00000| PB 0x000000009ad00000| Untracked |  0
| 350|0x000000009ae00000, 0x000000009ae00000, 0x000000009af00000|  0%| F|  |TAMS 0x000000009ae00000| PB 0x000000009ae00000| Untracked |  0
| 351|0x000000009af00000, 0x000000009af00000, 0x000000009b000000|  0%| F|  |TAMS 0x000000009af00000| PB 0x000000009af00000| Untracked |  0
| 352|0x000000009b000000, 0x000000009b000000, 0x000000009b100000|  0%| F|  |TAMS 0x000000009b000000| PB 0x000000009b000000| Untracked |  0
| 353|0x000000009b100000, 0x000000009b100000, 0x000000009b200000|  0%| F|  |TAMS 0x000000009b100000| PB 0x000000009b100000| Untracked |  0
| 354|0x000000009b200000, 0x000000009b200000, 0x000000009b300000|  0%| F|  |TAMS 0x000000009b200000| PB 0x000000009b200000| Untracked |  0
| 355|0x000000009b300000, 0x000000009b300000, 0x000000009b400000|  0%| F|  |TAMS 0x000000009b300000| PB 0x000000009b300000| Untracked |  0
| 356|0x000000009b400000, 0x000000009b400000, 0x000000009b500000|  0%| F|  |TAMS 0x000000009b400000| PB 0x000000009b400000| Untracked |  0
| 357|0x000000009b500000, 0x000000009b500000, 0x000000009b600000|  0%| F|  |TAMS 0x000000009b500000| PB 0x000000009b500000| Untracked |  0
| 358|0x000000009b600000, 0x000000009b600000, 0x000000009b700000|  0%| F|  |TAMS 0x000000009b600000| PB 0x000000009b600000| Untracked |  0
| 359|0x000000009b700000, 0x000000009b800000, 0x000000009b800000|100%| O|  |TAMS 0x000000009b700000| PB 0x000000009b700000| Untracked |  0
| 360|0x000000009b800000, 0x000000009b900000, 0x000000009b900000|100%| O|  |TAMS 0x000000009b800000| PB 0x000000009b800000| Untracked |  0
| 361|0x000000009b900000, 0x000000009ba00000, 0x000000009ba00000|100%| O|  |TAMS 0x000000009b900000| PB 0x000000009b900000| Untracked |  0
| 362|0x000000009ba00000, 0x000000009bb00000, 0x000000009bb00000|100%| O|  |TAMS 0x000000009ba00000| PB 0x000000009ba00000| Untracked |  0
| 363|0x000000009bb00000, 0x000000009bc00000, 0x000000009bc00000|100%| O|Cm|TAMS 0x000000009bb00000| PB 0x000000009bb00000| Complete |  0
| 364|0x000000009bc00000, 0x000000009bd00000, 0x000000009bd00000|100%| O|Cm|TAMS 0x000000009bc00000| PB 0x000000009bc00000| Complete |  0
| 365|0x000000009bd00000, 0x000000009be00000, 0x000000009be00000|100%| O|Cm|TAMS 0x000000009bd00000| PB 0x000000009bd00000| Complete |  0
| 366|0x000000009be00000, 0x000000009be00000, 0x000000009bf00000|  0%| F|  |TAMS 0x000000009be00000| PB 0x000000009be00000| Untracked |  0
| 367|0x000000009bf00000, 0x000000009bf00000, 0x000000009c000000|  0%| F|  |TAMS 0x000000009bf00000| PB 0x000000009bf00000| Untracked |  0
| 368|0x000000009c000000, 0x000000009c000000, 0x000000009c100000|  0%| F|  |TAMS 0x000000009c000000| PB 0x000000009c000000| Untracked |  0
| 369|0x000000009c100000, 0x000000009c100000, 0x000000009c200000|  0%| F|  |TAMS 0x000000009c100000| PB 0x000000009c100000| Untracked |  0
| 370|0x000000009c200000, 0x000000009c200000, 0x000000009c300000|  0%| F|  |TAMS 0x000000009c200000| PB 0x000000009c200000| Untracked |  0
| 371|0x000000009c300000, 0x000000009c300000, 0x000000009c400000|  0%| F|  |TAMS 0x000000009c300000| PB 0x000000009c300000| Untracked |  0
| 372|0x000000009c400000, 0x000000009c400000, 0x000000009c500000|  0%| F|  |TAMS 0x000000009c400000| PB 0x000000009c400000| Untracked |  0
| 373|0x000000009c500000, 0x000000009c500000, 0x000000009c600000|  0%| F|  |TAMS 0x000000009c500000| PB 0x000000009c500000| Untracked |  0
| 374|0x000000009c600000, 0x000000009c600000, 0x000000009c700000|  0%| F|  |TAMS 0x000000009c600000| PB 0x000000009c600000| Untracked |  0
| 375|0x000000009c700000, 0x000000009c700000, 0x000000009c800000|  0%| F|  |TAMS 0x000000009c700000| PB 0x000000009c700000| Untracked |  0
| 376|0x000000009c800000, 0x000000009c800000, 0x000000009c900000|  0%| F|  |TAMS 0x000000009c800000| PB 0x000000009c800000| Untracked |  0
| 377|0x000000009c900000, 0x000000009c900000, 0x000000009ca00000|  0%| F|  |TAMS 0x000000009c900000| PB 0x000000009c900000| Untracked |  0
| 378|0x000000009ca00000, 0x000000009ca00000, 0x000000009cb00000|  0%| F|  |TAMS 0x000000009ca00000| PB 0x000000009ca00000| Untracked |  0
| 379|0x000000009cb00000, 0x000000009cb00000, 0x000000009cc00000|  0%| F|  |TAMS 0x000000009cb00000| PB 0x000000009cb00000| Untracked |  0
| 380|0x000000009cc00000, 0x000000009cc00000, 0x000000009cd00000|  0%| F|  |TAMS 0x000000009cc00000| PB 0x000000009cc00000| Untracked |  0
| 381|0x000000009cd00000, 0x000000009cd00000, 0x000000009ce00000|  0%| F|  |TAMS 0x000000009cd00000| PB 0x000000009cd00000| Untracked |  0
| 382|0x000000009ce00000, 0x000000009ce00000, 0x000000009cf00000|  0%| F|  |TAMS 0x000000009ce00000| PB 0x000000009ce00000| Untracked |  0
| 383|0x000000009cf00000, 0x000000009cf00000, 0x000000009d000000|  0%| F|  |TAMS 0x000000009cf00000| PB 0x000000009cf00000| Untracked |  0
| 384|0x000000009d000000, 0x000000009d000000, 0x000000009d100000|  0%| F|  |TAMS 0x000000009d000000| PB 0x000000009d000000| Untracked |  0
| 385|0x000000009d100000, 0x000000009d100000, 0x000000009d200000|  0%| F|  |TAMS 0x000000009d100000| PB 0x000000009d100000| Untracked |  0
| 386|0x000000009d200000, 0x000000009d200000, 0x000000009d300000|  0%| F|  |TAMS 0x000000009d200000| PB 0x000000009d200000| Untracked |  0
| 387|0x000000009d300000, 0x000000009d300000, 0x000000009d400000|  0%| F|  |TAMS 0x000000009d300000| PB 0x000000009d300000| Untracked |  0
| 388|0x000000009d400000, 0x000000009d400000, 0x000000009d500000|  0%| F|  |TAMS 0x000000009d400000| PB 0x000000009d400000| Untracked |  0
| 389|0x000000009d500000, 0x000000009d500000, 0x000000009d600000|  0%| F|  |TAMS 0x000000009d500000| PB 0x000000009d500000| Untracked |  0
| 390|0x000000009d600000, 0x000000009d600000, 0x000000009d700000|  0%| F|  |TAMS 0x000000009d600000| PB 0x000000009d600000| Untracked |  0
| 391|0x000000009d700000, 0x000000009d700000, 0x000000009d800000|  0%| F|  |TAMS 0x000000009d700000| PB 0x000000009d700000| Untracked |  0
| 392|0x000000009d800000, 0x000000009d800000, 0x000000009d900000|  0%| F|  |TAMS 0x000000009d800000| PB 0x000000009d800000| Untracked |  0
| 393|0x000000009d900000, 0x000000009d900000, 0x000000009da00000|  0%| F|  |TAMS 0x000000009d900000| PB 0x000000009d900000| Untracked |  0
| 394|0x000000009da00000, 0x000000009da00000, 0x000000009db00000|  0%| F|  |TAMS 0x000000009da00000| PB 0x000000009da00000| Untracked |  0
| 395|0x000000009db00000, 0x000000009db08018, 0x000000009dc00000|  3%| S|CS|TAMS 0x000000009db00000| PB 0x000000009db00000| Complete |  0
| 396|0x000000009dc00000, 0x000000009dd00000, 0x000000009dd00000|100%| S|CS|TAMS 0x000000009dc00000| PB 0x000000009dc00000| Complete |  0
| 397|0x000000009dd00000, 0x000000009de00000, 0x000000009de00000|100%| S|CS|TAMS 0x000000009dd00000| PB 0x000000009dd00000| Complete |  0
| 398|0x000000009de00000, 0x000000009df00000, 0x000000009df00000|100%| S|CS|TAMS 0x000000009de00000| PB 0x000000009de00000| Complete |  0
| 399|0x000000009df00000, 0x000000009e000000, 0x000000009e000000|100%| S|CS|TAMS 0x000000009df00000| PB 0x000000009df00000| Complete |  0
| 400|0x000000009e000000, 0x000000009e100000, 0x000000009e100000|100%| S|CS|TAMS 0x000000009e000000| PB 0x000000009e000000| Complete |  0
| 401|0x000000009e100000, 0x000000009e200000, 0x000000009e200000|100%| S|CS|TAMS 0x000000009e100000| PB 0x000000009e100000| Complete |  0
| 402|0x000000009e200000, 0x000000009e300000, 0x000000009e300000|100%| S|CS|TAMS 0x000000009e200000| PB 0x000000009e200000| Complete |  0
| 403|0x000000009e300000, 0x000000009e300000, 0x000000009e400000|  0%| F|  |TAMS 0x000000009e300000| PB 0x000000009e300000| Untracked |  0
| 404|0x000000009e400000, 0x000000009e400000, 0x000000009e500000|  0%| F|  |TAMS 0x000000009e400000| PB 0x000000009e400000| Untracked |  0
| 405|0x000000009e500000, 0x000000009e500000, 0x000000009e600000|  0%| F|  |TAMS 0x000000009e500000| PB 0x000000009e500000| Untracked |  0
| 406|0x000000009e600000, 0x000000009e600000, 0x000000009e700000|  0%| F|  |TAMS 0x000000009e600000| PB 0x000000009e600000| Untracked |  0
| 407|0x000000009e700000, 0x000000009e700000, 0x000000009e800000|  0%| F|  |TAMS 0x000000009e700000| PB 0x000000009e700000| Untracked |  0
| 408|0x000000009e800000, 0x000000009e800000, 0x000000009e900000|  0%| F|  |TAMS 0x000000009e800000| PB 0x000000009e800000| Untracked |  0
| 409|0x000000009e900000, 0x000000009e900000, 0x000000009ea00000|  0%| F|  |TAMS 0x000000009e900000| PB 0x000000009e900000| Untracked |  0
| 410|0x000000009ea00000, 0x000000009ea00000, 0x000000009eb00000|  0%| F|  |TAMS 0x000000009ea00000| PB 0x000000009ea00000| Untracked |  0
| 411|0x000000009eb00000, 0x000000009eb00000, 0x000000009ec00000|  0%| F|  |TAMS 0x000000009eb00000| PB 0x000000009eb00000| Untracked |  0
| 412|0x000000009ec00000, 0x000000009ec00000, 0x000000009ed00000|  0%| F|  |TAMS 0x000000009ec00000| PB 0x000000009ec00000| Untracked |  0
| 413|0x000000009ed00000, 0x000000009ed00000, 0x000000009ee00000|  0%| F|  |TAMS 0x000000009ed00000| PB 0x000000009ed00000| Untracked |  0
| 414|0x000000009ee00000, 0x000000009ee00000, 0x000000009ef00000|  0%| F|  |TAMS 0x000000009ee00000| PB 0x000000009ee00000| Untracked |  0
| 415|0x000000009ef00000, 0x000000009ef00000, 0x000000009f000000|  0%| F|  |TAMS 0x000000009ef00000| PB 0x000000009ef00000| Untracked |  0
| 416|0x000000009f000000, 0x000000009f000000, 0x000000009f100000|  0%| F|  |TAMS 0x000000009f000000| PB 0x000000009f000000| Untracked |  0
| 417|0x000000009f100000, 0x000000009f100000, 0x000000009f200000|  0%| F|  |TAMS 0x000000009f100000| PB 0x000000009f100000| Untracked |  0
| 418|0x000000009f200000, 0x000000009f200000, 0x000000009f300000|  0%| F|  |TAMS 0x000000009f200000| PB 0x000000009f200000| Untracked |  0
| 419|0x000000009f300000, 0x000000009f300000, 0x000000009f400000|  0%| F|  |TAMS 0x000000009f300000| PB 0x000000009f300000| Untracked |  0
| 420|0x000000009f400000, 0x000000009f400000, 0x000000009f500000|  0%| F|  |TAMS 0x000000009f400000| PB 0x000000009f400000| Untracked |  0
| 421|0x000000009f500000, 0x000000009f500000, 0x000000009f600000|  0%| F|  |TAMS 0x000000009f500000| PB 0x000000009f500000| Untracked |  0
| 422|0x000000009f600000, 0x000000009f600000, 0x000000009f700000|  0%| F|  |TAMS 0x000000009f600000| PB 0x000000009f600000| Untracked |  0
| 423|0x000000009f700000, 0x000000009f700000, 0x000000009f800000|  0%| F|  |TAMS 0x000000009f700000| PB 0x000000009f700000| Untracked |  0
| 424|0x000000009f800000, 0x000000009f800000, 0x000000009f900000|  0%| F|  |TAMS 0x000000009f800000| PB 0x000000009f800000| Untracked |  0
| 425|0x000000009f900000, 0x000000009f900000, 0x000000009fa00000|  0%| F|  |TAMS 0x000000009f900000| PB 0x000000009f900000| Untracked |  0
| 426|0x000000009fa00000, 0x000000009fa00000, 0x000000009fb00000|  0%| F|  |TAMS 0x000000009fa00000| PB 0x000000009fa00000| Untracked |  0
| 427|0x000000009fb00000, 0x000000009fb00000, 0x000000009fc00000|  0%| F|  |TAMS 0x000000009fb00000| PB 0x000000009fb00000| Untracked |  0
| 428|0x000000009fc00000, 0x000000009fc00000, 0x000000009fd00000|  0%| F|  |TAMS 0x000000009fc00000| PB 0x000000009fc00000| Untracked |  0
| 429|0x000000009fd00000, 0x000000009fd00000, 0x000000009fe00000|  0%| F|  |TAMS 0x000000009fd00000| PB 0x000000009fd00000| Untracked |  0
| 430|0x000000009fe00000, 0x000000009fe00000, 0x000000009ff00000|  0%| F|  |TAMS 0x000000009fe00000| PB 0x000000009fe00000| Untracked |  0
| 431|0x000000009ff00000, 0x00000000a0000000, 0x00000000a0000000|100%| E|  |TAMS 0x000000009ff00000| PB 0x000000009ff00000| Complete |  0
| 432|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| E|CS|TAMS 0x00000000a0000000| PB 0x00000000a0000000| Complete |  0
| 433|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| E|CS|TAMS 0x00000000a0100000| PB 0x00000000a0100000| Complete |  0
| 434|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| E|CS|TAMS 0x00000000a0200000| PB 0x00000000a0200000| Complete |  0
| 435|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%| E|CS|TAMS 0x00000000a0300000| PB 0x00000000a0300000| Complete |  0
| 436|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%| E|CS|TAMS 0x00000000a0400000| PB 0x00000000a0400000| Complete |  0
| 437|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%| E|CS|TAMS 0x00000000a0500000| PB 0x00000000a0500000| Complete |  0
| 438|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| E|CS|TAMS 0x00000000a0600000| PB 0x00000000a0600000| Complete |  0
| 439|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| E|CS|TAMS 0x00000000a0700000| PB 0x00000000a0700000| Complete |  0
| 440|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| E|CS|TAMS 0x00000000a0800000| PB 0x00000000a0800000| Complete |  0
| 441|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| E|CS|TAMS 0x00000000a0900000| PB 0x00000000a0900000| Complete |  0
| 442|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| E|CS|TAMS 0x00000000a0a00000| PB 0x00000000a0a00000| Complete |  0
| 443|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| E|CS|TAMS 0x00000000a0b00000| PB 0x00000000a0b00000| Complete |  0
| 444|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| E|CS|TAMS 0x00000000a0c00000| PB 0x00000000a0c00000| Complete |  0
| 445|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| E|CS|TAMS 0x00000000a0d00000| PB 0x00000000a0d00000| Complete |  0
| 446|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| E|CS|TAMS 0x00000000a0e00000| PB 0x00000000a0e00000| Complete |  0
| 447|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| E|CS|TAMS 0x00000000a0f00000| PB 0x00000000a0f00000| Complete |  0
| 448|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| E|CS|TAMS 0x00000000a1000000| PB 0x00000000a1000000| Complete |  0
| 449|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| E|CS|TAMS 0x00000000a1100000| PB 0x00000000a1100000| Complete |  0
| 450|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| E|CS|TAMS 0x00000000a1200000| PB 0x00000000a1200000| Complete |  0
| 451|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| E|CS|TAMS 0x00000000a1300000| PB 0x00000000a1300000| Complete |  0
| 452|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| E|CS|TAMS 0x00000000a1400000| PB 0x00000000a1400000| Complete |  0
| 453|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| E|CS|TAMS 0x00000000a1500000| PB 0x00000000a1500000| Complete |  0
| 454|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| E|CS|TAMS 0x00000000a1600000| PB 0x00000000a1600000| Complete |  0
| 455|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%| E|CS|TAMS 0x00000000a1700000| PB 0x00000000a1700000| Complete |  0
| 456|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| E|CS|TAMS 0x00000000a1800000| PB 0x00000000a1800000| Complete |  0
| 457|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| E|CS|TAMS 0x00000000a1900000| PB 0x00000000a1900000| Complete |  0
| 458|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%| E|CS|TAMS 0x00000000a1a00000| PB 0x00000000a1a00000| Complete |  0
| 459|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%| E|CS|TAMS 0x00000000a1b00000| PB 0x00000000a1b00000| Complete |  0
| 460|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%| E|CS|TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Complete |  0
| 461|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%| E|CS|TAMS 0x00000000a1d00000| PB 0x00000000a1d00000| Complete |  0
| 462|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| E|CS|TAMS 0x00000000a1e00000| PB 0x00000000a1e00000| Complete |  0
| 463|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| E|CS|TAMS 0x00000000a1f00000| PB 0x00000000a1f00000| Complete |  0
| 464|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| E|CS|TAMS 0x00000000a2000000| PB 0x00000000a2000000| Complete |  0
| 465|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| E|CS|TAMS 0x00000000a2100000| PB 0x00000000a2100000| Complete |  0
| 466|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| E|CS|TAMS 0x00000000a2200000| PB 0x00000000a2200000| Complete |  0
| 467|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| E|CS|TAMS 0x00000000a2300000| PB 0x00000000a2300000| Complete |  0
| 468|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| E|CS|TAMS 0x00000000a2400000| PB 0x00000000a2400000| Complete |  0
| 469|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| E|CS|TAMS 0x00000000a2500000| PB 0x00000000a2500000| Complete |  0
| 470|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| E|CS|TAMS 0x00000000a2600000| PB 0x00000000a2600000| Complete |  0
| 471|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| E|CS|TAMS 0x00000000a2700000| PB 0x00000000a2700000| Complete |  0
| 472|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| E|CS|TAMS 0x00000000a2800000| PB 0x00000000a2800000| Complete |  0
| 473|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| E|CS|TAMS 0x00000000a2900000| PB 0x00000000a2900000| Complete |  0
| 474|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| E|CS|TAMS 0x00000000a2a00000| PB 0x00000000a2a00000| Complete |  0
| 475|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| E|CS|TAMS 0x00000000a2b00000| PB 0x00000000a2b00000| Complete |  0
| 476|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| E|CS|TAMS 0x00000000a2c00000| PB 0x00000000a2c00000| Complete |  0
| 477|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| E|CS|TAMS 0x00000000a2d00000| PB 0x00000000a2d00000| Complete |  0
| 478|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| E|CS|TAMS 0x00000000a2e00000| PB 0x00000000a2e00000| Complete |  0
| 479|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| E|CS|TAMS 0x00000000a2f00000| PB 0x00000000a2f00000| Complete |  0
| 480|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| E|CS|TAMS 0x00000000a3000000| PB 0x00000000a3000000| Complete |  0
| 481|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| E|CS|TAMS 0x00000000a3100000| PB 0x00000000a3100000| Complete |  0
| 482|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| E|CS|TAMS 0x00000000a3200000| PB 0x00000000a3200000| Complete |  0
| 483|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| E|CS|TAMS 0x00000000a3300000| PB 0x00000000a3300000| Complete |  0
| 484|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| E|CS|TAMS 0x00000000a3400000| PB 0x00000000a3400000| Complete |  0
| 485|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| E|CS|TAMS 0x00000000a3500000| PB 0x00000000a3500000| Complete |  0
| 486|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| E|CS|TAMS 0x00000000a3600000| PB 0x00000000a3600000| Complete |  0
| 487|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| E|CS|TAMS 0x00000000a3700000| PB 0x00000000a3700000| Complete |  0
| 488|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| E|CS|TAMS 0x00000000a3800000| PB 0x00000000a3800000| Complete |  0
| 489|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| E|CS|TAMS 0x00000000a3900000| PB 0x00000000a3900000| Complete |  0
| 490|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| E|CS|TAMS 0x00000000a3a00000| PB 0x00000000a3a00000| Complete |  0
| 491|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| E|CS|TAMS 0x00000000a3b00000| PB 0x00000000a3b00000| Complete |  0
| 492|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| E|CS|TAMS 0x00000000a3c00000| PB 0x00000000a3c00000| Complete |  0
| 493|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| E|CS|TAMS 0x00000000a3d00000| PB 0x00000000a3d00000| Complete |  0
| 494|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| E|CS|TAMS 0x00000000a3e00000| PB 0x00000000a3e00000| Complete |  0
| 495|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| E|CS|TAMS 0x00000000a3f00000| PB 0x00000000a3f00000| Complete |  0
| 496|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| E|CS|TAMS 0x00000000a4000000| PB 0x00000000a4000000| Complete |  0
| 497|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| E|CS|TAMS 0x00000000a4100000| PB 0x00000000a4100000| Complete |  0
| 498|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| E|CS|TAMS 0x00000000a4200000| PB 0x00000000a4200000| Complete |  0
| 499|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| E|CS|TAMS 0x00000000a4300000| PB 0x00000000a4300000| Complete |  0
| 500|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| E|CS|TAMS 0x00000000a4400000| PB 0x00000000a4400000| Complete |  0
| 501|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| E|CS|TAMS 0x00000000a4500000| PB 0x00000000a4500000| Complete |  0
| 502|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| E|CS|TAMS 0x00000000a4600000| PB 0x00000000a4600000| Complete |  0
| 503|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| E|CS|TAMS 0x00000000a4700000| PB 0x00000000a4700000| Complete |  0
| 504|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| E|CS|TAMS 0x00000000a4800000| PB 0x00000000a4800000| Complete |  0
| 505|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| E|CS|TAMS 0x00000000a4900000| PB 0x00000000a4900000| Complete |  0
| 506|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| E|CS|TAMS 0x00000000a4a00000| PB 0x00000000a4a00000| Complete |  0
| 507|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| E|CS|TAMS 0x00000000a4b00000| PB 0x00000000a4b00000| Complete |  0
| 508|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| E|CS|TAMS 0x00000000a4c00000| PB 0x00000000a4c00000| Complete |  0
| 509|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| E|CS|TAMS 0x00000000a4d00000| PB 0x00000000a4d00000| Complete |  0
| 510|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| E|CS|TAMS 0x00000000a4e00000| PB 0x00000000a4e00000| Complete |  0
| 511|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| E|CS|TAMS 0x00000000a4f00000| PB 0x00000000a4f00000| Complete |  0
| 512|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| E|CS|TAMS 0x00000000a5000000| PB 0x00000000a5000000| Complete |  0
| 513|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%| E|CS|TAMS 0x00000000a5100000| PB 0x00000000a5100000| Complete |  0
| 514|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%| E|CS|TAMS 0x00000000a5200000| PB 0x00000000a5200000| Complete |  0
| 515|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%| E|CS|TAMS 0x00000000a5300000| PB 0x00000000a5300000| Complete |  0
| 516|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%| E|CS|TAMS 0x00000000a5400000| PB 0x00000000a5400000| Complete |  0
| 517|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%| E|CS|TAMS 0x00000000a5500000| PB 0x00000000a5500000| Complete |  0
| 518|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%| E|CS|TAMS 0x00000000a5600000| PB 0x00000000a5600000| Complete |  0
| 519|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%| E|CS|TAMS 0x00000000a5700000| PB 0x00000000a5700000| Complete |  0
| 520|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%| E|CS|TAMS 0x00000000a5800000| PB 0x00000000a5800000| Complete |  0
| 521|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%| E|CS|TAMS 0x00000000a5900000| PB 0x00000000a5900000| Complete |  0
| 522|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%| E|CS|TAMS 0x00000000a5a00000| PB 0x00000000a5a00000| Complete |  0
| 523|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%| E|CS|TAMS 0x00000000a5b00000| PB 0x00000000a5b00000| Complete |  0
| 524|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%| E|CS|TAMS 0x00000000a5c00000| PB 0x00000000a5c00000| Complete |  0
| 525|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%| E|CS|TAMS 0x00000000a5d00000| PB 0x00000000a5d00000| Complete |  0
| 526|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%| E|CS|TAMS 0x00000000a5e00000| PB 0x00000000a5e00000| Complete |  0
| 527|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%| E|CS|TAMS 0x00000000a5f00000| PB 0x00000000a5f00000| Complete |  0
| 528|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%| E|CS|TAMS 0x00000000a6000000| PB 0x00000000a6000000| Complete |  0
| 529|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| E|CS|TAMS 0x00000000a6100000| PB 0x00000000a6100000| Complete |  0
| 530|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| E|CS|TAMS 0x00000000a6200000| PB 0x00000000a6200000| Complete |  0
| 531|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| E|CS|TAMS 0x00000000a6300000| PB 0x00000000a6300000| Complete |  0
| 532|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| E|CS|TAMS 0x00000000a6400000| PB 0x00000000a6400000| Complete |  0
| 533|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| E|CS|TAMS 0x00000000a6500000| PB 0x00000000a6500000| Complete |  0
| 534|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%| E|CS|TAMS 0x00000000a6600000| PB 0x00000000a6600000| Complete |  0
| 535|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%| E|CS|TAMS 0x00000000a6700000| PB 0x00000000a6700000| Complete |  0
| 536|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| E|CS|TAMS 0x00000000a6800000| PB 0x00000000a6800000| Complete |  0
| 537|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| E|CS|TAMS 0x00000000a6900000| PB 0x00000000a6900000| Complete |  0
| 538|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%| E|CS|TAMS 0x00000000a6a00000| PB 0x00000000a6a00000| Complete |  0
| 539|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%| E|CS|TAMS 0x00000000a6b00000| PB 0x00000000a6b00000| Complete |  0
| 540|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%| E|CS|TAMS 0x00000000a6c00000| PB 0x00000000a6c00000| Complete |  0
| 541|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%| E|CS|TAMS 0x00000000a6d00000| PB 0x00000000a6d00000| Complete |  0
| 542|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%| E|CS|TAMS 0x00000000a6e00000| PB 0x00000000a6e00000| Complete |  0
| 543|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%| E|CS|TAMS 0x00000000a6f00000| PB 0x00000000a6f00000| Complete |  0
| 544|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%| E|CS|TAMS 0x00000000a7000000| PB 0x00000000a7000000| Complete |  0
| 545|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%| E|CS|TAMS 0x00000000a7100000| PB 0x00000000a7100000| Complete |  0
| 546|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%| E|CS|TAMS 0x00000000a7200000| PB 0x00000000a7200000| Complete |  0
| 547|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%| E|CS|TAMS 0x00000000a7300000| PB 0x00000000a7300000| Complete |  0
| 548|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%| E|CS|TAMS 0x00000000a7400000| PB 0x00000000a7400000| Complete |  0
| 549|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%| E|CS|TAMS 0x00000000a7500000| PB 0x00000000a7500000| Complete |  0
| 550|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%| E|CS|TAMS 0x00000000a7600000| PB 0x00000000a7600000| Complete |  0
| 551|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%| E|CS|TAMS 0x00000000a7700000| PB 0x00000000a7700000| Complete |  0
| 552|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%| E|CS|TAMS 0x00000000a7800000| PB 0x00000000a7800000| Complete |  0
| 553|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%| E|CS|TAMS 0x00000000a7900000| PB 0x00000000a7900000| Complete |  0
| 554|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%| E|CS|TAMS 0x00000000a7a00000| PB 0x00000000a7a00000| Complete |  0
| 555|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%| E|CS|TAMS 0x00000000a7b00000| PB 0x00000000a7b00000| Complete |  0
| 556|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%| E|CS|TAMS 0x00000000a7c00000| PB 0x00000000a7c00000| Complete |  0
| 557|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%| E|CS|TAMS 0x00000000a7d00000| PB 0x00000000a7d00000| Complete |  0
| 558|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%| E|CS|TAMS 0x00000000a7e00000| PB 0x00000000a7e00000| Complete |  0
| 559|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%| E|CS|TAMS 0x00000000a7f00000| PB 0x00000000a7f00000| Complete |  0
| 560|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%| E|CS|TAMS 0x00000000a8000000| PB 0x00000000a8000000| Complete |  0
| 561|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%| E|CS|TAMS 0x00000000a8100000| PB 0x00000000a8100000| Complete |  0
| 562|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%| E|CS|TAMS 0x00000000a8200000| PB 0x00000000a8200000| Complete |  0
| 563|0x00000000a8300000, 0x00000000a8400000, 0x00000000a8400000|100%| E|CS|TAMS 0x00000000a8300000| PB 0x00000000a8300000| Complete |  0
| 564|0x00000000a8400000, 0x00000000a8500000, 0x00000000a8500000|100%| E|CS|TAMS 0x00000000a8400000| PB 0x00000000a8400000| Complete |  0
| 565|0x00000000a8500000, 0x00000000a8600000, 0x00000000a8600000|100%| E|CS|TAMS 0x00000000a8500000| PB 0x00000000a8500000| Complete |  0
| 566|0x00000000a8600000, 0x00000000a8700000, 0x00000000a8700000|100%| E|CS|TAMS 0x00000000a8600000| PB 0x00000000a8600000| Complete |  0
| 567|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%| E|CS|TAMS 0x00000000a8700000| PB 0x00000000a8700000| Complete |  0
| 568|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%| E|CS|TAMS 0x00000000a8800000| PB 0x00000000a8800000| Complete |  0
| 569|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%| E|CS|TAMS 0x00000000a8900000| PB 0x00000000a8900000| Complete |  0
| 570|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%| E|CS|TAMS 0x00000000a8a00000| PB 0x00000000a8a00000| Complete |  0
| 571|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%| E|CS|TAMS 0x00000000a8b00000| PB 0x00000000a8b00000| Complete |  0
| 572|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| E|CS|TAMS 0x00000000a8c00000| PB 0x00000000a8c00000| Complete |  0
| 573|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| E|CS|TAMS 0x00000000a8d00000| PB 0x00000000a8d00000| Complete |  0
| 574|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| E|CS|TAMS 0x00000000a8e00000| PB 0x00000000a8e00000| Complete |  0
| 575|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%| E|CS|TAMS 0x00000000a8f00000| PB 0x00000000a8f00000| Complete |  0
| 576|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%| E|CS|TAMS 0x00000000a9000000| PB 0x00000000a9000000| Complete |  0
| 577|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%| E|CS|TAMS 0x00000000a9100000| PB 0x00000000a9100000| Complete |  0
| 578|0x00000000a9200000, 0x00000000a9300000, 0x00000000a9300000|100%| E|CS|TAMS 0x00000000a9200000| PB 0x00000000a9200000| Complete |  0
| 579|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%| E|CS|TAMS 0x00000000a9300000| PB 0x00000000a9300000| Complete |  0
| 580|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%| E|CS|TAMS 0x00000000a9400000| PB 0x00000000a9400000| Complete |  0
| 581|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%| E|CS|TAMS 0x00000000a9500000| PB 0x00000000a9500000| Complete |  0
| 582|0x00000000a9600000, 0x00000000a9700000, 0x00000000a9700000|100%| E|CS|TAMS 0x00000000a9600000| PB 0x00000000a9600000| Complete |  0
| 583|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%| E|CS|TAMS 0x00000000a9700000| PB 0x00000000a9700000| Complete |  0
| 584|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%| E|CS|TAMS 0x00000000a9800000| PB 0x00000000a9800000| Complete |  0
| 585|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%| E|CS|TAMS 0x00000000a9900000| PB 0x00000000a9900000| Complete |  0
| 586|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| E|CS|TAMS 0x00000000a9a00000| PB 0x00000000a9a00000| Complete |  0
| 587|0x00000000a9b00000, 0x00000000a9c00000, 0x00000000a9c00000|100%| E|CS|TAMS 0x00000000a9b00000| PB 0x00000000a9b00000| Complete |  0
| 588|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%| E|CS|TAMS 0x00000000a9c00000| PB 0x00000000a9c00000| Complete |  0
| 589|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%| E|CS|TAMS 0x00000000a9d00000| PB 0x00000000a9d00000| Complete |  0
| 590|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%| E|CS|TAMS 0x00000000a9e00000| PB 0x00000000a9e00000| Complete |  0
| 591|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%| E|CS|TAMS 0x00000000a9f00000| PB 0x00000000a9f00000| Complete |  0
| 592|0x00000000aa000000, 0x00000000aa100000, 0x00000000aa100000|100%| E|CS|TAMS 0x00000000aa000000| PB 0x00000000aa000000| Complete |  0
|1959|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| O|  |TAMS 0x00000000ff700000| PB 0x00000000ff700000| Untracked |  0
|1960|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| E|CS|TAMS 0x00000000ff800000| PB 0x00000000ff800000| Complete |  0
|1961|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| E|CS|TAMS 0x00000000ff900000| PB 0x00000000ff900000| Complete |  0
|1962|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| E|CS|TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Complete |  0
|1963|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Untracked |  0
|1964|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Complete |  0
|1965|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| E|CS|TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Complete |  0
|1966|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Complete |  0
|1967|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000| PB 0x00000000fff00000| Complete |  0

Card table byte_map: [0x000002f268150000,0x000002f268530000] _byte_map_base: 0x000002f267d28000

Marking Bits: (CMBitMap*) 0x000002f250367420
 Bits: [0x000002f268530000, 0x000002f26a3f0000)

Polling page: 0x000002f24e0c0000

Metaspace:

Usage:
  Non-class:    111.45 MB used.
      Class:     20.89 MB used.
       Both:    132.34 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     112.44 MB ( 88%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      21.62 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     134.06 MB ( 12%) committed. 

Chunk freelists:
   Non-Class:  14.67 MB
       Class:  10.28 MB
        Both:  24.95 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 223.38 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 2744.
num_arena_deaths: 44.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 2139.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 68.
num_chunks_taken_from_freelist: 8507.
num_chunk_merges: 24.
num_chunk_splits: 5561.
num_chunks_enlarged: 4062.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120064Kb used=12991Kb max_used=13538Kb free=107072Kb
 bounds [0x000002f260500000, 0x000002f261240000, 0x000002f267a40000]
CodeHeap 'profiled nmethods': size=120000Kb used=25641Kb max_used=28082Kb free=94358Kb
 bounds [0x000002f258a40000, 0x000002f25a5b0000, 0x000002f25ff70000]
CodeHeap 'non-nmethods': size=5696Kb used=3591Kb max_used=3629Kb free=2105Kb
 bounds [0x000002f25ff70000, 0x000002f260310000, 0x000002f260500000]
CodeCache: size=245760Kb, used=42223Kb, max_used=45249Kb, free=203535Kb
 total_blobs=21703, nmethods=19354, adapters=2249, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 923.720 Thread 0x000002f2450122e0 30380       3       com.mojang.logging.plugins.QueueLogAppender::append (48 bytes)
Event: 923.735 Thread 0x000002f2450122e0 nmethod 30380 0x000002f258d34a08 code [0x000002f258d34b80, 0x000002f258d35208]
Event: 923.735 Thread 0x000002f2450122e0 30381       3       org.apache.logging.log4j.core.layout.PatternLayout::toSerializable (6 bytes)
Event: 923.735 Thread 0x000002f2450122e0 nmethod 30381 0x000002f258a73788 code [0x000002f258a738c0, 0x000002f258a73af8]
Event: 923.735 Thread 0x000002f2450122e0 30382       3       org.apache.logging.log4j.core.layout.PatternLayout::toSerializable (11 bytes)
Event: 923.735 Thread 0x000002f2450122e0 nmethod 30382 0x000002f258b76888 code [0x000002f258b769a0, 0x000002f258b76b78]
Event: 923.735 Thread 0x000002f2450122e0 30383   !   3       org.apache.logging.log4j.core.layout.PatternLayout$PatternSelectorSerializer::toSerializable (29 bytes)
Event: 923.737 Thread 0x000002f2450122e0 nmethod 30383 0x000002f258d39208 code [0x000002f258d39420, 0x000002f258d39e78]
Event: 923.784 Thread 0x000002f2450122e0 30384       3       org.lwjgl.system.CustomBuffer::address (19 bytes)
Event: 923.785 Thread 0x000002f2450122e0 nmethod 30384 0x000002f258b20208 code [0x000002f258b20340, 0x000002f258b205a8]
Event: 924.025 Thread 0x000002f26c807740 30385       4       java.time.Instant::create (49 bytes)
Event: 924.028 Thread 0x000002f26c807740 nmethod 30385 0x000002f260636288 code [0x000002f2606363a0, 0x000002f260636520]
Event: 924.319 Thread 0x000002f26c807740 30386       4       sun.util.calendar.ZoneInfo::getOffsets (342 bytes)
Event: 924.329 Thread 0x000002f26c807740 nmethod 30386 0x000002f26050c988 code [0x000002f26050cac0, 0x000002f26050cfc0]
Event: 924.418 Thread 0x000002f26c807740 30387       4       java.lang.StringConcatHelper::prepend (17 bytes)
Event: 924.430 Thread 0x000002f26c807740 nmethod 30387 0x000002f2608e4a88 code [0x000002f2608e4bc0, 0x000002f2608e5178]
Event: 924.620 Thread 0x000002f26c807740 30388       4       org.apache.logging.log4j.message.ParameterFormatter::analyzePattern (171 bytes)
Event: 924.628 Thread 0x000002f26c807740 nmethod 30388 0x000002f260b4be88 code [0x000002f260b4bfe0, 0x000002f260b4c4a8]
Event: 924.851 Thread 0x000002f26c807740 30389       4       sun.util.locale.provider.LocaleProviderAdapter::getAdapter (177 bytes)
Event: 924.889 Thread 0x000002f26c807740 nmethod 30389 0x000002f2608c3708 code [0x000002f2608c3980, 0x000002f2608c4450]

GC Heap History (20 events):
Event: 122.101 GC heap before
{Heap before GC invocations=71 (full 0):
 garbage-first heap   total reserved 2015232K, committed 500736K, used 456465K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 161 young (164864K), 8 survivors (8192K)
 Metaspace       used 117557K, committed 118976K, reserved 1179648K
  class space    used 18716K, committed 19328K, reserved 1048576K
}
Event: 122.109 GC heap after
{Heap after GC invocations=72 (full 0):
 garbage-first heap   total reserved 2015232K, committed 500736K, used 300611K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 117557K, committed 118976K, reserved 1179648K
  class space    used 18716K, committed 19328K, reserved 1048576K
}
Event: 123.482 GC heap before
{Heap before GC invocations=72 (full 0):
 garbage-first heap   total reserved 2015232K, committed 500736K, used 474691K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 170 young (174080K), 9 survivors (9216K)
 Metaspace       used 123782K, committed 125248K, reserved 1179648K
  class space    used 19690K, committed 20352K, reserved 1048576K
}
Event: 123.492 GC heap after
{Heap after GC invocations=73 (full 0):
 garbage-first heap   total reserved 2015232K, committed 500736K, used 312221K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 123782K, committed 125248K, reserved 1179648K
  class space    used 19690K, committed 20352K, reserved 1048576K
}
Event: 124.543 GC heap before
{Heap before GC invocations=73 (full 0):
 garbage-first heap   total reserved 2015232K, committed 500736K, used 461725K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 159 young (162816K), 13 survivors (13312K)
 Metaspace       used 129298K, committed 130816K, reserved 1179648K
  class space    used 20732K, committed 21376K, reserved 1048576K
}
Event: 124.557 GC heap after
{Heap after GC invocations=74 (full 0):
 garbage-first heap   total reserved 2015232K, committed 500736K, used 323188K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 17 survivors (17408K)
 Metaspace       used 129298K, committed 130816K, reserved 1179648K
  class space    used 20732K, committed 21376K, reserved 1048576K
}
Event: 125.622 GC heap before
{Heap before GC invocations=75 (full 0):
 garbage-first heap   total reserved 2015232K, committed 541696K, used 455284K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 145 young (148480K), 17 survivors (17408K)
 Metaspace       used 131808K, committed 133440K, reserved 1179648K
  class space    used 21095K, committed 21824K, reserved 1048576K
}
Event: 125.637 GC heap after
{Heap after GC invocations=76 (full 0):
 garbage-first heap   total reserved 2015232K, committed 541696K, used 341270K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 19 survivors (19456K)
 Metaspace       used 131808K, committed 133440K, reserved 1179648K
  class space    used 21095K, committed 21824K, reserved 1048576K
}
Event: 126.588 GC heap before
{Heap before GC invocations=76 (full 0):
 garbage-first heap   total reserved 2015232K, committed 541696K, used 492822K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 168 young (172032K), 19 survivors (19456K)
 Metaspace       used 132915K, committed 134592K, reserved 1179648K
  class space    used 21216K, committed 21952K, reserved 1048576K
}
Event: 126.605 GC heap after
{Heap after GC invocations=77 (full 0):
 garbage-first heap   total reserved 2015232K, committed 541696K, used 357783K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 19 survivors (19456K)
 Metaspace       used 132915K, committed 134592K, reserved 1179648K
  class space    used 21216K, committed 21952K, reserved 1048576K
}
Event: 127.510 GC heap before
{Heap before GC invocations=77 (full 0):
 garbage-first heap   total reserved 2015232K, committed 541696K, used 492951K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 152 young (155648K), 19 survivors (19456K)
 Metaspace       used 133915K, committed 135616K, reserved 1179648K
  class space    used 21277K, committed 22016K, reserved 1048576K
}
Event: 127.520 GC heap after
{Heap after GC invocations=78 (full 0):
 garbage-first heap   total reserved 2015232K, committed 541696K, used 366482K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 133915K, committed 135616K, reserved 1179648K
  class space    used 21277K, committed 22016K, reserved 1048576K
}
Event: 133.112 GC heap before
{Heap before GC invocations=79 (full 0):
 garbage-first heap   total reserved 2015232K, committed 611328K, used 500626K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 140 young (143360K), 9 survivors (9216K)
 Metaspace       used 134494K, committed 136128K, reserved 1179648K
  class space    used 21309K, committed 22016K, reserved 1048576K
}
Event: 133.118 GC heap after
{Heap after GC invocations=80 (full 0):
 garbage-first heap   total reserved 2015232K, committed 611328K, used 370834K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 134494K, committed 136128K, reserved 1179648K
  class space    used 21309K, committed 22016K, reserved 1048576K
}
Event: 145.594 GC heap before
{Heap before GC invocations=80 (full 0):
 garbage-first heap   total reserved 2015232K, committed 611328K, used 561298K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 200 young (204800K), 14 survivors (14336K)
 Metaspace       used 135012K, committed 136704K, reserved 1179648K
  class space    used 21322K, committed 22080K, reserved 1048576K
}
Event: 145.602 GC heap after
{Heap after GC invocations=81 (full 0):
 garbage-first heap   total reserved 2015232K, committed 611328K, used 368364K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 135012K, committed 136704K, reserved 1179648K
  class space    used 21322K, committed 22080K, reserved 1048576K
}
Event: 172.848 GC heap before
{Heap before GC invocations=81 (full 0):
 garbage-first heap   total reserved 2015232K, committed 611328K, used 554732K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 196 young (200704K), 14 survivors (14336K)
 Metaspace       used 135366K, committed 137024K, reserved 1179648K
  class space    used 21375K, committed 22080K, reserved 1048576K
}
Event: 172.855 GC heap after
{Heap after GC invocations=82 (full 0):
 garbage-first heap   total reserved 2015232K, committed 611328K, used 368685K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 135366K, committed 137024K, reserved 1179648K
  class space    used 21375K, committed 22080K, reserved 1048576K
}
Event: 488.945 GC heap before
{Heap before GC invocations=83 (full 0):
 garbage-first heap   total reserved 2015232K, committed 616448K, used 563245K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 197 young (201728K), 7 survivors (7168K)
 Metaspace       used 135434K, committed 137216K, reserved 1179648K
  class space    used 21382K, committed 22144K, reserved 1048576K
}
Event: 488.951 GC heap after
{Heap after GC invocations=84 (full 0):
 garbage-first heap   total reserved 2015232K, committed 616448K, used 368777K [0x0000000085000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 135434K, committed 137216K, reserved 1179648K
  class space    used 21382K, committed 22144K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.012 Loaded shared library C:\Program Files\Java\jdk-23\bin\java.dll
Event: 0.050 Loaded shared library C:\Program Files\Java\jdk-23\bin\jsvml.dll
Event: 0.233 Loaded shared library C:\Program Files\Java\jdk-23\bin\net.dll
Event: 0.236 Loaded shared library C:\Program Files\Java\jdk-23\bin\nio.dll
Event: 0.242 Loaded shared library C:\Program Files\Java\jdk-23\bin\zip.dll
Event: 0.669 Loaded shared library C:\Program Files\Java\jdk-23\bin\jimage.dll
Event: 1.548 Loaded shared library C:\Program Files\Java\jdk-23\bin\management.dll
Event: 1.551 Loaded shared library C:\Program Files\Java\jdk-23\bin\management_ext.dll
Event: 2.056 Loaded shared library C:\Program Files\Java\jdk-23\bin\verify.dll
Event: 8.495 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-98539053\jna16824192169101908308.dll
Event: 23.943 Loaded shared library C:\Program Files\Java\jdk-23\bin\sunmscapi.dll
Event: 24.017 Loaded shared library C:\Program Files\Java\jdk-23\bin\extnet.dll
Event: 24.651 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\lwjgl_gonza\3.3.3-snapshot\x64\lwjgl.dll
Event: 25.488 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\lwjgl_gonza\3.3.3-snapshot\x64\lwjgl_stb.dll
Event: 25.584 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\lwjgl_gonza\3.3.3-snapshot\x64\lwjgl_opengl.dll

Deoptimization events (20 events):
Event: 919.092 Thread 0x000002f25030cf40 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002f261214f78 relative=0x00000000000043b8
Event: 919.093 Thread 0x000002f25030cf40 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002f261214f78 method=net.minecraft.client.gl.GlGpuFence.close()V @ 6 c2
Event: 919.093 Thread 0x000002f25030cf40 DEOPT PACKING pc=0x000002f261214f78 sp=0x000000fab2cfe940
Event: 919.093 Thread 0x000002f25030cf40 DEOPT UNPACKING pc=0x000002f25ffc4402 sp=0x000000fab2cfe7d8 mode 2
Event: 919.099 Thread 0x000002f25030cf40 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002f260c95460 relative=0x0000000000000220
Event: 919.099 Thread 0x000002f25030cf40 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002f260c95460 method=net.minecraft.client.gl.GlGpuFence.close()V @ 6 c2
Event: 919.099 Thread 0x000002f25030cf40 DEOPT PACKING pc=0x000002f260c95460 sp=0x000000fab2cfe7e0
Event: 919.099 Thread 0x000002f25030cf40 DEOPT UNPACKING pc=0x000002f25ffc4402 sp=0x000000fab2cfe740 mode 2
Event: 919.102 Thread 0x000002f25030cf40 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002f2612006a0 relative=0x00000000000045e0
Event: 919.102 Thread 0x000002f25030cf40 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002f2612006a0 method=net.minecraft.client.gl.GlGpuFence.close()V @ 6 c2
Event: 919.102 Thread 0x000002f25030cf40 DEOPT PACKING pc=0x000002f2612006a0 sp=0x000000fab2cfe5d0
Event: 919.102 Thread 0x000002f25030cf40 DEOPT UNPACKING pc=0x000002f25ffc4402 sp=0x000000fab2cfe4c8 mode 2
Event: 919.111 Thread 0x000002f25030cf40 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002f260cbd81c relative=0x000000000000051c
Event: 919.111 Thread 0x000002f25030cf40 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002f260cbd81c method=net.minecraft.client.gl.GlGpuFence.close()V @ 6 c2
Event: 919.111 Thread 0x000002f25030cf40 DEOPT PACKING pc=0x000002f260cbd81c sp=0x000000fab2cfe7d0
Event: 919.111 Thread 0x000002f25030cf40 DEOPT UNPACKING pc=0x000002f25ffc4402 sp=0x000000fab2cfe748 mode 2
Event: 919.113 Thread 0x000002f25030cf40 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002f261205814 relative=0x0000000000000f14
Event: 919.113 Thread 0x000002f25030cf40 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002f261205814 method=net.minecraft.client.gl.GlGpuFence.close()V @ 6 c2
Event: 919.114 Thread 0x000002f25030cf40 DEOPT PACKING pc=0x000002f261205814 sp=0x000000fab2cfe910
Event: 919.114 Thread 0x000002f25030cf40 DEOPT UNPACKING pc=0x000002f25ffc4402 sp=0x000000fab2cfe780 mode 2

Classes loaded (20 events):
Event: 124.226 Loading class java/util/stream/ReferencePipeline$15$1
Event: 124.226 Loading class java/util/stream/ReferencePipeline$15$1 done
Event: 124.567 Loading class java/util/Spliterators$LongIteratorSpliterator
Event: 124.567 Loading class java/util/Spliterators$LongIteratorSpliterator done
Event: 124.841 Loading class java/util/IdentityHashMap$KeySpliterator
Event: 124.841 Loading class java/util/IdentityHashMap$KeySpliterator done
Event: 125.116 Loading class java/util/ReverseOrderListView$DescendingIterator
Event: 125.117 Loading class java/util/ReverseOrderListView$DescendingIterator done
Event: 172.718 Loading class com/sun/management/internal/HotSpotThreadImpl
Event: 172.718 Loading class com/sun/management/ThreadMXBean
Event: 172.718 Loading class com/sun/management/ThreadMXBean done
Event: 172.718 Loading class sun/management/ThreadImpl
Event: 172.719 Loading class sun/management/ThreadImpl done
Event: 172.719 Loading class com/sun/management/internal/HotSpotThreadImpl done
Event: 172.837 Loading class java/lang/management/ThreadInfo
Event: 172.837 Loading class java/lang/management/ThreadInfo done
Event: 172.837 Loading class java/lang/management/MonitorInfo
Event: 172.837 Loading class java/lang/management/LockInfo
Event: 172.838 Loading class java/lang/management/LockInfo done
Event: 172.838 Loading class java/lang/management/MonitorInfo done

Classes unloaded (20 events):
Event: 81.025 Thread 0x000002f26c7d8b20 Unloading class 0x000002f202010400 'java/lang/invoke/LambdaForm$DMH+0x000002f202010400'
Event: 81.025 Thread 0x000002f26c7d8b20 Unloading class 0x000002f202010000 'java/lang/invoke/LambdaForm$DMH+0x000002f202010000'
Event: 81.025 Thread 0x000002f26c7d8b20 Unloading class 0x000002f202009000 'java/lang/invoke/LambdaForm$DMH+0x000002f202009000'
Event: 81.025 Thread 0x000002f26c7d8b20 Unloading class 0x000002f202009c00 'java/lang/invoke/LambdaForm$DMH+0x000002f202009c00'
Event: 81.025 Thread 0x000002f26c7d8b20 Unloading class 0x000002f202009800 'java/lang/invoke/LambdaForm$DMH+0x000002f202009800'
Event: 81.025 Thread 0x000002f26c7d8b20 Unloading class 0x000002f202009400 'java/lang/invoke/LambdaForm$DMH+0x000002f202009400'
Event: 120.368 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2020aec00 'java/lang/invoke/LambdaForm$MH+0x000002f2020aec00'
Event: 120.368 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2020ae800 'java/lang/invoke/LambdaForm$MH+0x000002f2020ae800'
Event: 120.368 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2020ae400 'java/lang/invoke/LambdaForm$MH+0x000002f2020ae400'
Event: 120.368 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2020ae000 'java/lang/invoke/LambdaForm$MH+0x000002f2020ae000'
Event: 120.368 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2020adc00 'java/lang/invoke/LambdaForm$MH+0x000002f2020adc00'
Event: 120.368 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2020ad400 'java/lang/invoke/LambdaForm$DMH+0x000002f2020ad400'
Event: 120.368 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2020ad000 'java/lang/invoke/LambdaForm$DMH+0x000002f2020ad000'
Event: 120.368 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2020acc00 'java/lang/invoke/LambdaForm$DMH+0x000002f2020acc00'
Event: 120.368 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2020ac400 'java/lang/invoke/LambdaForm$DMH+0x000002f2020ac400'
Event: 120.368 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2020ac800 'java/lang/invoke/LambdaForm$DMH+0x000002f2020ac800'
Event: 121.939 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2022cb000 'java/lang/invoke/LambdaForm$MH+0x000002f2022cb000'
Event: 121.939 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2022cac00 'java/lang/invoke/LambdaForm$MH+0x000002f2022cac00'
Event: 121.939 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2022ca000 'java/lang/invoke/LambdaForm$MH+0x000002f2022ca000'
Event: 121.939 Thread 0x000002f26c7d8b20 Unloading class 0x000002f2022c9800 'java/lang/invoke/LambdaForm$MH+0x000002f2022c9800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 127.535 Thread 0x000002f24cd457c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffc1b858}> (0x00000000ffc1b858) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 127.537 Thread 0x000002f24b483ff0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffc25990}> (0x00000000ffc25990) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 127.539 Thread 0x000002f24cd457c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffcaaf50}> (0x00000000ffcaaf50) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 127.541 Thread 0x000002f24b483ff0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffcb41c0}> (0x00000000ffcb41c0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 127.542 Thread 0x000002f25030cf40 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffc6a900}: 'float java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, float)'> (0x00000000ffc6a900) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 127.544 Thread 0x000002f24cd457c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffcb82f0}> (0x00000000ffcb82f0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 127.547 Thread 0x000002f24b483ff0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffcc29f0}> (0x00000000ffcc29f0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 127.611 Thread 0x000002f25030cf40 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a55d6c10}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, java.lang.Object)'> (0x00000000a55d6c10) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 127.848 Thread 0x000002f24cd37f30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a9618730}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000000a9618730) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 127.849 Thread 0x000002f24cd37f30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a961c6b8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000000a961c6b8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 127.849 Thread 0x000002f24cd37f30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a961fe30}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000a961fe30) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 127.889 Thread 0x000002f24cd45130 Exception <a 'sun/nio/fs/WindowsException'{0x00000000a4304c58}> (0x00000000a4304c58) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 134.939 Thread 0x000002f25030cf40 Implicit null exception at 0x000002f261070115 to 0x000002f261073c94
Event: 141.327 Thread 0x000002f25030cf40 Implicit null exception at 0x000002f260e4423f to 0x000002f260e452d0
Event: 141.632 Thread 0x000002f25030cf40 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a2aa4f18}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000a2aa4f18) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 156.548 Thread 0x000002f25030cf40 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a0dd7d00}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000a0dd7d00) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 173.003 Thread 0x000002f29752f830 Implicit null exception at 0x000002f26122c801 to 0x000002f26122cb5c
Event: 173.076 Thread 0x000002f29752f830 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffcec8a8}> (0x00000000ffcec8a8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 918.595 Thread 0x000002f25030cf40 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a02ae518}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a02ae518) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 918.599 Thread 0x000002f25030cf40 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a02b1b80}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a02b1b80) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]

VM Operations (20 events):
Event: 151.812 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 151.813 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 172.719 Executing VM operation: ThreadDump
Event: 172.837 Executing VM operation: ThreadDump done
Event: 172.837 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 172.837 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 172.838 Executing VM operation: RendezvousGCThreads
Event: 172.838 Executing VM operation: RendezvousGCThreads done
Event: 172.848 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 172.855 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 173.111 Executing VM operation: G1PauseRemark
Event: 173.123 Executing VM operation: G1PauseRemark done
Event: 173.206 Executing VM operation: G1PauseCleanup
Event: 173.206 Executing VM operation: G1PauseCleanup done
Event: 232.981 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 232.981 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 232.981 Executing VM operation: RendezvousGCThreads
Event: 232.981 Executing VM operation: RendezvousGCThreads done
Event: 488.945 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 488.952 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f259ef5e08
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f259f07088
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f259f07e08
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f259f08108
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f259f0ad08
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f259f53e08
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f259f54288
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f259f56d88
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f259fe5008
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f259fe5308
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f25a01e908
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f25a079308
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f25a079788
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f25a07ab08
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f25a07af08
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f25a07b308
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f25a09fe08
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f25a0a0708
Event: 173.119 Thread 0x000002f26c7d8b20 flushing  nmethod 0x000002f25a14a088
Event: 173.119 Thread 0x000002f26c7d8b20 flushing osr nmethod 0x000002f25a1f7f08

Events (20 events):
Event: 157.730 Thread 0x000002f28396d300 Thread exited: 0x000002f28396d300
Event: 157.730 Thread 0x000002f25030cf40 Thread added: 0x000002f29752e480
Event: 172.865 Thread 0x000002f2450122e0 Thread added: 0x000002f29740a750
Event: 173.078 Thread 0x000002f29752f830 Thread exited: 0x000002f29752f830
Event: 174.777 Thread 0x000002f29740a750 Thread exited: 0x000002f29740a750
Event: 179.126 Thread 0x000002f24cd43d80 Thread exited: 0x000002f24cd43d80
Event: 187.744 Thread 0x000002f24cd429d0 Thread exited: 0x000002f24cd429d0
Event: 187.744 Thread 0x000002f24cd457c0 Thread exited: 0x000002f24cd457c0
Event: 187.907 Thread 0x000002f24cd45130 Thread exited: 0x000002f24cd45130
Event: 187.908 Thread 0x000002f24cd44aa0 Thread exited: 0x000002f24cd44aa0
Event: 187.920 Thread 0x000002f24cd44410 Thread exited: 0x000002f24cd44410
Event: 215.835 Thread 0x000002f28396aba0 Thread exited: 0x000002f28396aba0
Event: 216.332 Thread 0x000002f28396f3d0 Thread exited: 0x000002f28396f3d0
Event: 216.352 Thread 0x000002f28396e6b0 Thread exited: 0x000002f28396e6b0
Event: 216.372 Thread 0x000002f283968ad0 Thread exited: 0x000002f283968ad0
Event: 216.393 Thread 0x000002f28396fa60 Thread exited: 0x000002f28396fa60
Event: 216.414 Thread 0x000002f283970780 Thread exited: 0x000002f283970780
Event: 216.434 Thread 0x000002f2839700f0 Thread exited: 0x000002f2839700f0
Event: 216.454 Thread 0x000002f283970e10 Thread exited: 0x000002f283970e10
Event: 217.335 Thread 0x000002f24b483ff0 Thread exited: 0x000002f24b483ff0


Dynamic libraries:
0x00007ff663e30000 - 0x00007ff663e40000 	C:\Program Files\Java\jdk-23\bin\java.exe
0x00007ffa83440000 - 0x00007ffa836a5000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa82550000 - 0x00007ffa82619000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa807f0000 - 0x00007ffa80bd8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa80ec0000 - 0x00007ffa8100b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa1e320000 - 0x00007ffa1e337000 	C:\Program Files\Java\jdk-23\bin\jli.dll
0x00007ffa1e300000 - 0x00007ffa1e31b000 	C:\Program Files\Java\jdk-23\bin\VCRUNTIME140.dll
0x00007ffa81e30000 - 0x00007ffa81ffa000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa80590000 - 0x00007ffa805b7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa5d950000 - 0x00007ffa5dbea000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffa812e0000 - 0x00007ffa8130b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa82000000 - 0x00007ffa820a9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa81010000 - 0x00007ffa81147000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa805c0000 - 0x00007ffa80663000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa816a0000 - 0x00007ffa816d0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa570f0000 - 0x00007ffa570fc000 	C:\Program Files\Java\jdk-23\bin\vcruntime140_1.dll
0x00007ffa1d8e0000 - 0x00007ffa1d96e000 	C:\Program Files\Java\jdk-23\bin\msvcp140.dll
0x00007ffa16b00000 - 0x00007ffa17850000 	C:\Program Files\Java\jdk-23\bin\server\jvm.dll
0x00007ffa82490000 - 0x00007ffa82543000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa82d70000 - 0x00007ffa82e16000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa832c0000 - 0x00007ffa833d5000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa82970000 - 0x00007ffa829e4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa7f170000 - 0x00007ffa7f1ce000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffa65210000 - 0x00007ffa6521b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa7a4c0000 - 0x00007ffa7a4f5000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa7f150000 - 0x00007ffa7f164000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffa7f430000 - 0x00007ffa7f44b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa52c00000 - 0x00007ffa52c0a000 	C:\Program Files\Java\jdk-23\bin\jimage.dll
0x00007ffa7ded0000 - 0x00007ffa7e111000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa81310000 - 0x00007ffa81695000 	C:\WINDOWS\System32\combase.dll
0x00007ffa82620000 - 0x00007ffa82701000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa529e0000 - 0x00007ffa52a19000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa80be0000 - 0x00007ffa80c79000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa1e2e0000 - 0x00007ffa1e2fe000 	C:\Program Files\Java\jdk-23\bin\java.dll
0x00007ffa822d0000 - 0x00007ffa8246e000 	C:\WINDOWS\System32\ole32.dll
0x00007ffa816e0000 - 0x00007ffa81e22000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa80c80000 - 0x00007ffa80df4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffa7e1f0000 - 0x00007ffa7ea48000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa82c70000 - 0x00007ffa82d61000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa81240000 - 0x00007ffa812aa000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa804a0000 - 0x00007ffa804cf000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa167e0000 - 0x00007ffa168b7000 	C:\Program Files\Java\jdk-23\bin\jsvml.dll
0x00007ffa2b220000 - 0x00007ffa2b230000 	C:\Program Files\Java\jdk-23\bin\net.dll
0x00007ffa7f980000 - 0x00007ffa7f9ea000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa1e2a0000 - 0x00007ffa1e2b6000 	C:\Program Files\Java\jdk-23\bin\nio.dll
0x00007ffa1e2c0000 - 0x00007ffa1e2d7000 	C:\Program Files\Java\jdk-23\bin\zip.dll
0x00007ffa570e0000 - 0x00007ffa570ea000 	C:\Program Files\Java\jdk-23\bin\management.dll
0x00007ffa53eb0000 - 0x00007ffa53ebb000 	C:\Program Files\Java\jdk-23\bin\management_ext.dll
0x00007ffa82b60000 - 0x00007ffa82b68000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa0cba0000 - 0x00007ffa0cbb8000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffa7eea0000 - 0x00007ffa7efc7000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffa7edb0000 - 0x00007ffa7ede3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa82e20000 - 0x00007ffa82e2a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa030b0000 - 0x00007ffa030c2000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffa0d1c0000 - 0x00007ffa0d1f0000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffa79840000 - 0x00007ffa79860000 	C:\WINDOWS\System32\wshbth.dll
0x00007ffa6b740000 - 0x00007ffa6b74b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffa75e70000 - 0x00007ffa75ef6000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffa28ad0000 - 0x00007ffa28ae0000 	C:\Program Files\Java\jdk-23\bin\verify.dll
0x00007ffa7fd60000 - 0x00007ffa7fd7b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa7f390000 - 0x00007ffa7f3ca000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa7fa20000 - 0x00007ffa7fa4b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa80470000 - 0x00007ffa80496000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffa7fbc0000 - 0x00007ffa7fbcc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa20660000 - 0x00007ffa206a9000 	C:\Users\<USER>\AppData\Local\Temp\jna-98539053\jna16824192169101908308.dll
0x00007ffa82b70000 - 0x00007ffa82c18000 	C:\WINDOWS\System32\clbcatq.dll
0x00007ffa57440000 - 0x00007ffa5745d000 	C:\WINDOWS\SYSTEM32\amsi.dll
0x00007ffa57210000 - 0x00007ffa572ab000 	C:\ProgramData\Microsoft\Windows Defender\Platform\4.18.25050.5-0\MpOav.dll
0x00007ffa5be00000 - 0x00007ffa5be4e000 	C:\WINDOWS\SYSTEM32\Pdh.dll
0x00007ffa53c30000 - 0x00007ffa53c41000 	C:\WINDOWS\System32\perfos.dll
0x00007ffa7ad80000 - 0x00007ffa7ad92000 	C:\WINDOWS\SYSTEM32\pfclient.dll
0x00007ffa4d9c0000 - 0x00007ffa4d9ce000 	C:\Program Files\Java\jdk-23\bin\sunmscapi.dll
0x00007ffa80670000 - 0x00007ffa807e7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffa7feb0000 - 0x00007ffa7fee0000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffa7fe60000 - 0x00007ffa7fe9f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffa52030000 - 0x00007ffa52039000 	C:\Program Files\Java\jdk-23\bin\extnet.dll
0x00007ffa1d730000 - 0x00007ffa1d7ab000 	C:\Users\<USER>\AppData\Local\Temp\lwjgl_gonza\3.3.3-snapshot\x64\lwjgl.dll
0x00007ffa1d6a0000 - 0x00007ffa1d722000 	C:\Users\<USER>\AppData\Local\Temp\lwjgl_gonza\3.3.3-snapshot\x64\glfw.dll
0x00007ffa1d430000 - 0x00007ffa1d6a0000 	C:\Users\<USER>\AppData\Local\Temp\lwjgl_gonza\3.3.3-snapshot\x64\jemalloc.dll
0x00007ffa1e380000 - 0x00007ffa1e3cb000 	C:\WINDOWS\SYSTEM32\dinput8.dll
0x00007ffa27060000 - 0x00007ffa27072000 	C:\WINDOWS\SYSTEM32\xinput1_4.dll
0x00007ffa80250000 - 0x00007ffa8027d000 	C:\WINDOWS\SYSTEM32\DEVOBJ.dll
0x00007ffa801f0000 - 0x00007ffa80247000 	C:\WINDOWS\SYSTEM32\cfgmgr32.dll
0x00007ffa6ae70000 - 0x00007ffa6b051000 	C:\WINDOWS\SYSTEM32\inputhost.dll
0x00007ffa7a060000 - 0x00007ffa7a185000 	C:\WINDOWS\SYSTEM32\CoreMessaging.dll
0x00007ffa7ad40000 - 0x00007ffa7ad76000 	C:\WINDOWS\SYSTEM32\dwmapi.dll
0x00007ffa7a970000 - 0x00007ffa7aa1f000 	C:\WINDOWS\system32\uxtheme.dll
0x00007ffa829f0000 - 0x00007ffa82b50000 	C:\WINDOWS\System32\MSCTF.dll
0x00007ffa004c0000 - 0x00007ffa005d4000 	C:\WINDOWS\SYSTEM32\opengl32.dll
0x00007ffa26400000 - 0x00007ffa2642d000 	C:\WINDOWS\SYSTEM32\GLU32.dll
0x00007ffa7ada0000 - 0x00007ffa7aded000 	C:\WINDOWS\SYSTEM32\dxcore.dll
0x00007ffa7ab20000 - 0x00007ffa7ab81000 	C:\WINDOWS\SYSTEM32\directxdatabasehelper.dll
0x00007ffa6b750000 - 0x00007ffa6b76a000 	C:\WINDOWS\SYSTEM32\windows.staterepositorycore.dll
0x00007ffa14e10000 - 0x00007ffa1637b000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_9c81a3482aa553fb\igxelpicd64.dll
0x00007ffa7abc0000 - 0x00007ffa7acf5000 	C:\WINDOWS\SYSTEM32\dxgi.dll
0x00007ffa20470000 - 0x00007ffa20493000 	C:\WINDOWS\SYSTEM32\d3d12.dll
0x00007ffa178a0000 - 0x00007ffa17a42000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_9c81a3482aa553fb\igdml64.dll
0x00007ffa73360000 - 0x00007ffa7390a000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_9c81a3482aa553fb\igdgmm64.dll
0x00007ffa6e130000 - 0x00007ffa732d7000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_9c81a3482aa553fb\igc64.dll
0x00007ffa6c220000 - 0x00007ffa6c370000 	C:\WINDOWS\SYSTEM32\textinputframework.dll
0x00007ffa14d80000 - 0x00007ffa14e02000 	C:\Users\<USER>\AppData\Local\Temp\lwjgl_gonza\3.3.3-snapshot\x64\lwjgl_stb.dll
0x00007ffa45450000 - 0x00007ffa45518000 	C:\WINDOWS\SYSTEM32\mscms.dll
0x00007ffa1e210000 - 0x00007ffa1e259000 	C:\WINDOWS\SYSTEM32\icm32.dll
0x00007ffa14d20000 - 0x00007ffa14d7e000 	C:\Users\<USER>\AppData\Local\Temp\lwjgl_gonza\3.3.3-snapshot\x64\lwjgl_opengl.dll
0x00007ffa14b50000 - 0x00007ffa14d17000 	C:\Users\<USER>\AppData\Local\Temp\lwjgl_gonza\3.3.3-snapshot\x64\OpenAL.dll
0x00007ffa6de60000 - 0x00007ffa6df00000 	C:\WINDOWS\System32\MMDevApi.dll
0x00007ffa1e3d0000 - 0x00007ffa1e543000 	C:\WINDOWS\System32\Speech\Common\sapi.dll
0x00007ffa6c790000 - 0x00007ffa6c94e000 	C:\WINDOWS\SYSTEM32\AUDIOSES.DLL
0x00007ffa7b320000 - 0x00007ffa7b334000 	C:\WINDOWS\SYSTEM32\resourcepolicyclient.dll
0x00007ffa75f00000 - 0x00007ffa761e3000 	C:\WINDOWS\SYSTEM32\CoreUIComponents.dll
0x00007ffa5c870000 - 0x00007ffa5c878000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffa663b0000 - 0x00007ffa663c2000 	C:\WINDOWS\system32\wbem\wbemprox.dll
0x00007ffa66330000 - 0x00007ffa663aa000 	C:\WINDOWS\SYSTEM32\wbemcomn.dll
0x00007ffa5bfa0000 - 0x00007ffa5bfb5000 	C:\WINDOWS\system32\wbem\wbemsvc.dll
0x00007ffa595d0000 - 0x00007ffa596c0000 	C:\WINDOWS\system32\wbem\fastprox.dll
0x00007ffa4b250000 - 0x00007ffa4b307000 	C:\WINDOWS\SYSTEM32\winspool.drv

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-23\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Java\jdk-23\bin\server;C:\Users\<USER>\AppData\Local\Temp\jna-98539053;C:\ProgramData\Microsoft\Windows Defender\Platform\4.18.25050.5-0;C:\Users\<USER>\AppData\Local\Temp\lwjgl_gonza\3.3.3-snapshot\x64;C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_9c81a3482aa553fb;C:\WINDOWS\System32\Speech\Common;C:\WINDOWS\system32\wbem

VM Arguments:
jvm_args: -Dfabric.dli.config=C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\launch.cfg -Dfabric.dli.env=client -Dfabric.dli.main=net.fabricmc.loader.impl.launch.knot.KnotClient -Dfile.encoding=UTF-8 -Duser.country=ES -Duser.language=es -Duser.variant 
java_command: net.fabricmc.devlaunchinjector.Main
java_class_path (initial): C:\MyWorks\MyMods\AiVillagersFabric\build\classes\java\client;C:\MyWorks\MyMods\AiVillagersFabric\build\resources\client;C:\Users\<USER>\.gradle\caches\fabric-loom\1.21.6\net.fabricmc.yarn.1_21_6.1.21.6+build.1-v2\mappings.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm\9.8\dc19ecb3f7889b7860697215cae99c0f9b6f6b4b\asm-9.8.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-analysis\9.8\b9747a320844b6cb1eacd90d8ecfd260a16c01d3\asm-analysis-9.8.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-commons\9.8\36e4d212970388e5bd2c5180292012502df461bb\asm-commons-9.8.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-tree\9.8\18419ca5b77a2f81097c741e7872e6ab8d2f40d\asm-tree-9.8.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-util\9.8\395f1c1f035258511f27bc9b2583d76e4b143f59\asm-util-9.8.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.fabricmc\sponge-mixin\0.15.5+mixin.0.8.7\22f9eb729e216a091673a574a5906dc1b9027fb3\sponge-mixin-0.15.5+mixin.0.8.7.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.github.llamalad7\mixinextras-fabric\0.4.1\8d1a9e96afb990367fa1f904d17580d164da72e3\mixinextras-fabric-0.4.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.fabricmc\dev-launch-injector\0.2.1+build.8\da8bef7e6e2f952da707f282bdb46882a0fce5e3\dev-launch-injector-0.2.1+build.8.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecrell\terminalconsoleappender\1.3.0\b562e9bb61235c9520e26282cdee71f8f802d1fc\terminalconsoleappender-1.3.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-annotations\2.13.4\858c6cc78e1f08a885b1613e1d817c829df70a6e\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-core\2.13.4\cf934c681294b97ef6d80082faeefbe1edadf56\jackson-core-2.13.4.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-databind\********\325c06bdfeb628cfb80ebaaf1a26cc1eb558a585\jackson-databind-********.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.oshi\oshi-core\6.6.5\e1099981fd15dc4236c4499d82aba1276fb43a9a\oshi-core-6.6.5.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.stephenc.jcip\jcip-annotations\1.0-1\ef31541dd28ae2cefdd17c7ebf352d93e9058c63\jcip-annotations-1.0-1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.11.0\527175ca6d81050b53bdd4c457a6d6e017626b0e\gson-2.11.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.2\c4a06a64e650562f30b7bf9aaec1bfed43aca12b\failureaccess-1.0.2.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\33.3.1-jre\852f8b363da0111e819460021ca693cacca3e8db\guava-33.3.1-jre.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.ibm.icu\icu4j\76.1\215f3a8e936d4069344bd75f2b1368fd58112894\icu4j-76.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.microsoft.azure\msal4j\1.17.2\a6211e3d71d0388929babaa0ff0951b30d001852\msal4j-1.17.2.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\authlib\6.0.58\9261a6d53e629469fab20136b968bf5202d9c0f7\authlib-6.0.58.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\blocklist\1.0.10\5c685c5ffa94c4cd39496c7184c1d122e515ecef\blocklist-1.0.10.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\brigadier\1.3.10\d15b53a14cf20fdcaa98f731af5dda654452c010\brigadier-1.3.10.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\datafixerupper\8.0.16\67d4de6d7f95d89bcf5862995fb854ebaec02a34\datafixerupper-8.0.16.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\jtracy\1.0.29\6f07dcb6a2e595c7ee2ca43b67e5d1c018ca0770\jtracy-1.0.29.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\jtracy\1.0.29\e05332cb31c7ae582dc8d8bd1bffd47c2ff7636f\jtracy-1.0.29-natives-windows.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\logging\1.5.10\9ab1202793717af9df9c1704d0a02892067001eb\logging-1.5.10.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\patchy\2.2.10\da05971b07cbb379d002cf7eaec6a2048211fefc\patchy-2.2.10.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\text2speech\1.18.11\e853a12cdd6ba4f4836e8f4bf3b37844a13482b6\text2speech-1.18.11.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.nimbusds\content-type\2.3\e3aa0be212d7a42839a8f3f506f5b990bcce0222\content-type-2.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.nimbusds\lang-tag\1.7\97c73ecd70bc7e8eefb26c5eea84f251a63f1031\lang-tag-1.7.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.nimbusds\nimbus-jose-jwt\9.40\42b1dfa0360e4062951b070bac52dd8d96fd7b38\nimbus-jose-jwt-9.40.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.nimbusds\oauth2-oidc-sdk\11.18\7c7ec4f4066625ff07a711ad856fa04da1ff9de\oauth2-oidc-sdk-11.18.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.17.1\973638b7149d333563584137ebf13a691bb60579\commons-codec-1.17.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.17.0\ddcc8433eb019fb48fe25207c0278143f3e1d7e2\commons-io-2.17.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-logging\commons-logging\1.3.4\b9fc14968d63a8b8a8a2c1885fe3e90564239708\commons-logging-1.3.4.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-buffer\4.1.118.Final\7022990af1e0d449f9d5322035899745e19735c5\netty-buffer-4.1.118.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-codec\4.1.118.Final\307f665c08ce57333121de4f460479fc0c3c94d4\netty-codec-4.1.118.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-common\4.1.118.Final\4bb0f9899146484fa89f7b9bc27389d5b8e2ecde\netty-common-4.1.118.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-handler\4.1.118.Final\30ebb05b6b0fb071dbfcf713017c4a767a97bb9b\netty-handler-4.1.118.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-resolver\4.1.118.Final\28c378c19c1779eca1104b400452627f3ebc4aea\netty-resolver-4.1.118.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-classes-epoll\4.1.118.Final\376ce95507066f0e755d97c1c8bcd6c33f657617\netty-transport-classes-epoll-4.1.118.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-native-unix-common\4.1.118.Final\9da25a94e6a0edac90da0bc7894e5a54efcb866b\netty-transport-native-unix-common-4.1.118.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport\4.1.118.Final\5a27232e5d08218722d94ca14f0b1b4576e7711c\netty-transport-4.1.118.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\it.unimi.dsi\fastutil\8.5.15\1e885b40c9563ab0d3899b871fd0b30e958705dc\fastutil-8.5.15.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.java.dev.jna\jna-platform\5.15.0\86b502cad57d45da172b5e3231c537b042e296ef\jna-platform-5.15.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.java.dev.jna\jna\5.15.0\1ee1d80ff44f08280188f7c0e740d57207841ac\jna-5.15.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minidev\accessors-smart\2.5.1\19b820261eb2e7de7d5bde11d1c06e4501dd7e5f\accessors-smart-2.5.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minidev\json-smart\2.5.1\4c11d2808d009132dfbbf947ebf37de6bf266c8e\json-smart-2.5.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.sf.jopt-simple\jopt-simple\5.0.4\4fdac2fbe92dfad86aa6e9301736f6b4342a3f5c\jopt-simple-5.0.4.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-compress\1.27.1\a19151084758e2fbb6b41eddaa88e7b8ff4e6599\commons-compress-1.27.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-lang3\3.17.0\b17d2136f0460dcc0d2016ceefca8723bdf4ee70\commons-lang3-3.17.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.httpcomponents\httpclient\4.5.14\1194890e6f56ec29177673f2f12d0b8e627dec98\httpclient-4.5.14.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.httpcomponents\httpcore\4.4.16\51cf043c87253c9f58b539c9f7e44c8894223850\httpcore-4.4.16.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-core\2.24.1\c85285146f28d8c8962384f786e2dff04172fb43\log4j-core-2.24.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-api\2.24.1\7ebeb12c20606373005af4232cd0ecca72613dda\log4j-api-2.24.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-slf4j2-impl\2.24.1\8e3ddc96464ef7f768823e7e001a52b23de8cd0a\log4j-slf4j2-impl-2.24.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jcraft\jorbis\0.0.17\8872d22b293e8f5d7d56ff92be966e6dc28ebdc6\jorbis-0.0.17.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.joml\joml\1.10.8\fc0a71dad90a2cf41d82a76156a0e700af8e4f8d\joml-1.10.8.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-freetype\3.3.3\a0db6c84a8becc8ca05f9dbfa985edc348a824c7\lwjgl-freetype-3.3.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-freetype\3.3.3\81091b006dbb43fab04c8c638e9ac87c51b4096d\lwjgl-freetype-3.3.3-natives-windows.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-freetype\3.3.3\82028265a0a2ff33523ca75137ada7dc176e5210\lwjgl-freetype-3.3.3-natives-windows-arm64.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-freetype\3.3.3\15a8c1de7f51d07a92eae7ce1222557073a0c0c3\lwjgl-freetype-3.3.3-natives-windows-x86.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.3\efa1eb78c5ccd840e9f329717109b5e892d72f8e\lwjgl-glfw-3.3.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.3\e449e28b4891fc423c54c85fbc5bb0b9efece67a\lwjgl-glfw-3.3.3-natives-windows.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.3\f27018dc74f6289574502b46cce55d52817554e2\lwjgl-glfw-3.3.3-natives-windows-arm64.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.3\32334f3fd5270a59bad9939a93115acb6de36dcf\lwjgl-glfw-3.3.3-natives-windows-x86.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.3\b543467b7ff3c6920539a88ee602d34098628be5\lwjgl-jemalloc-3.3.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.3\426222fc027602a5f21b9c0fe79cde6a4c7a011f\lwjgl-jemalloc-3.3.3-natives-windows.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.3\ba1f3fed0ee4be0217eaa41c5bbfb4b9b1383c33\lwjgl-jemalloc-3.3.3-natives-windows-arm64.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.3\f6063b6e0f23be483c5c88d84ce51b39dc69126c\lwjgl-jemalloc-3.3.3-natives-windows-x86.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.3\daada81ceb5fc0c291fbfdd4433cb8d9423577f2\lwjgl-openal-3.3.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.3\cf83862ae95d98496b26915024c7e666d8ab1c8f\lwjgl-openal-3.3.3-natives-windows.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.3\8e0615235116b9e4160dfe87bec90f5f6378bf72\lwjgl-openal-3.3.3-natives-windows-arm64.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.3\87b8d5050e3adb46bb58fe1cb2669a4a48fce10d\lwjgl-openal-3.3.3-natives-windows-x86.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.3\2f6b0147078396a58979125a4c947664e98293a\lwjgl-opengl-3.3.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.3\e6c1eec8be8a71951b830a4d69efc01c6531900c\lwjgl-opengl-3.3.3-natives-windows.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.3\65e956d3735a1abdc82eff4baec1b61174697d4b\lwjgl-opengl-3.3.3-natives-windows-arm64.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.3\d32d833dcaa2f355a886eaf21f0408b5f03241d\lwjgl-opengl-3.3.3-natives-windows-x86.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.3\25dd6161988d7e65f71d5065c99902402ee32746\lwjgl-stb-3.3.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.3\1d9facdf6541de114b0f963be33505b7679c78cb\lwjgl-stb-3.3.3-natives-windows.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.3\a584ab44de569708871f0a79561f4d8c37487f2c\lwjgl-stb-3.3.3-natives-windows-arm64.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.3\b5c874687b9aac1a936501d4ed2c49567fd1b575\lwjgl-stb-3.3.3-natives-windows-x86.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.3\82d755ca94b102e9ca77283b9e2dc46d1b15fbe5\lwjgl-tinyfd-3.3.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.3\a6697981b0449a5087c1d546fc08b4f73e8f98c9\lwjgl-tinyfd-3.3.3-natives-windows.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.3\a88c494f3006eb91a7433b12a3a55a9a6c20788b\lwjgl-tinyfd-3.3.3-natives-windows-arm64.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.3\c336c84ee88cccb495c6ffa112395509e7378e8a\lwjgl-tinyfd-3.3.3-natives-windows-x86.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.3\29589b5f87ed335a6c7e7ee6a5775f81f97ecb84\lwjgl-3.3.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.3\a5ed18a2b82fc91b81f40d717cb1f64c9dcb0540\lwjgl-3.3.3-natives-windows.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.3\e9aca8c5479b520a2a7f0d542a118140e812c5e8\lwjgl-3.3.3-natives-windows-arm64.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.3\9e670718e050aeaeea0c2d5b907cffb142f2e58f\lwjgl-3.3.3-natives-windows-x86.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lz4\lz4-java\1.8.0\4b986a99445e49ea5fbf5d149c4b63f6ed6c6780\lz4-java-1.8.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\2.0.16\172931663a09a1fa515567af5fbef00897d3c04\slf4j-api-2.0.16.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\minecraftMaven\net\minecraft\minecraft-clientOnly-b07cf08c30\1.21.6-net.fabricmc.yarn.1_21_6.1.21.6+build.1-v2\minecraft-clientOnly-b07cf08c30-1.21.6-net.fabricmc.yarn.1_21_6.1.21.6+build.1-v2.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\minecraftMaven\net\minecraft\minecraft-common-b07cf08c30\1.21.6-net.fabricmc.yarn.1_21_6.1.21.6+build.1-v2\minecraft-common-b07cf08c30-1.21.6-net.fabricmc.yarn.1_21_6.1.21.6+build.1-v2.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-native-epoll\4.1.118.Final\7e6b89e3746acb7cf6f0aad993bbd058fc6d912e\netty-transport-native-epoll-4.1.118.Final-linux-x86_64.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-native-epoll\4.1.118.Final\82f94d0a9d837f6b6a580379373310ff7288c0f8\netty-transport-native-epoll-4.1.118.Final-linux-aarch_64.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-transfer-api-v1-client\6.0.4+074c84ee9c\fabric-transfer-api-v1-client-6.0.4+074c84ee9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-command-api-v2-client\2.2.52+b39a696a9c\fabric-command-api-v2-client-2.2.52+b39a696a9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-data-generation-api-v1-client\23.2.0+a12c79229c\fabric-data-generation-api-v1-client-23.2.0+a12c79229c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-data-attachment-api-v1-client\1.8.8+d9a896309c\fabric-data-attachment-api-v1-client-1.8.8+d9a896309c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-events-interaction-v0-client\4.0.22+0d4d74479c\fabric-events-interaction-v0-client-4.0.22+0d4d74479c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-item-api-v1-client\11.4.2+5e29f1899c\fabric-item-api-v1-client-11.4.2+5e29f1899c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-item-group-api-v1-client\4.2.12+9ec45cd89c\fabric-item-group-api-v1-client-4.2.12+9ec45cd89c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-lifecycle-events-v1-client\2.6.2+db4dfd859c\fabric-lifecycle-events-v1-client-2.6.2+db4dfd859c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-message-api-v1-client\6.1.0+8efa0e499c\fabric-message-api-v1-client-6.1.0+8efa0e499c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-model-loading-api-v1-client\5.2.4+ae8be2b89c\fabric-model-loading-api-v1-client-5.2.4+ae8be2b89c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-particles-v1-client\4.1.6+c1dce2189c\fabric-particles-v1-client-4.1.6+c1dce2189c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-recipe-api-v1-client\8.1.13+39ce47f59c\fabric-recipe-api-v1-client-8.1.13+39ce47f59c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-object-builder-api-v1-client\21.1.5+946cf7899c\fabric-object-builder-api-v1-client-21.1.5+946cf7899c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-screen-handler-api-v1-client\1.3.134+d32f812d9c\fabric-screen-handler-api-v1-client-1.3.134+d32f812d9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-registry-sync-v0-client\6.1.25+9ec45cd89c\fabric-registry-sync-v0-client-6.1.25+9ec45cd89c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-networking-api-v1-client\5.0.0+d32f812d9c\fabric-networking-api-v1-client-5.0.0+d32f812d9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-renderer-indigo-client\4.0.1+2516f2229c\fabric-renderer-indigo-client-4.0.1+2516f2229c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-renderer-api-v1-client\7.0.1+a0cfcc829c\fabric-renderer-api-v1-client-7.0.1+a0cfcc829c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-rendering-fluids-v1-client\3.1.29+fa6cb72b9c\fabric-rendering-fluids-v1-client-3.1.29+fa6cb72b9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-rendering-v1-client\12.3.0+ac3e15d19c\fabric-rendering-v1-client-12.3.0+ac3e15d19c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-tag-api-v1-client\1.2.0+75110b049c\fabric-tag-api-v1-client-1.2.0+75110b049c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-resource-loader-v0-client\3.1.10+fa6cb72b9c\fabric-resource-loader-v0-client-3.1.10+fa6cb72b9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-screen-api-v1-client\2.0.50+908cbc919c\fabric-screen-api-v1-client-2.0.50+908cbc919c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-block-view-api-v2-client\1.0.30+d32f812d9c\fabric-block-view-api-v2-client-1.0.30+d32f812d9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-client-gametest-api-v1-client\4.2.3+458b8c9a9c\fabric-client-gametest-api-v1-client-4.2.3+458b8c9a9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-game-rule-api-v1-client\1.0.71+9ec45cd89c\fabric-game-rule-api-v1-client-1.0.71+9ec45cd89c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-key-binding-api-v1-client\1.0.64+9ec45cd89c\fabric-key-binding-api-v1-client-1.0.64+9ec45cd89c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-sound-api-v1-client\1.0.41+d32f812d9c\fabric-sound-api-v1-client-1.0.41+d32f812d9c.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.fabricmc\fabric-loader\0.16.14\5778d47f536bf2c63ed2abc1a56f5a1c129e34a\fabric-loader-0.16.14.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-api-common\0.127.0+1.21.6\fabric-api-common-0.127.0+1.21.6.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-transfer-api-v1-common\6.0.4+074c84ee9c\fabric-transfer-api-v1-common-6.0.4+074c84ee9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-api-lookup-api-v1-common\1.6.99+9ec45cd89c\fabric-api-lookup-api-v1-common-1.6.99+9ec45cd89c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-command-api-v2-common\2.2.52+b39a696a9c\fabric-command-api-v2-common-2.2.52+b39a696a9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-content-registries-v0-common\10.0.17+fa6cb72b9c\fabric-content-registries-v0-common-10.0.17+fa6cb72b9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-data-generation-api-v1-common\23.2.0+a12c79229c\fabric-data-generation-api-v1-common-23.2.0+a12c79229c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-convention-tags-v1-common\2.1.37+7f945d5b9c\fabric-convention-tags-v1-common-2.1.37+7f945d5b9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-convention-tags-v2-common\2.15.2+d9a896309c\fabric-convention-tags-v2-common-2.15.2+d9a896309c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-data-attachment-api-v1-common\1.8.8+d9a896309c\fabric-data-attachment-api-v1-common-1.8.8+d9a896309c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-entity-events-v1-common\2.1.0+c9e472739c\fabric-entity-events-v1-common-2.1.0+c9e472739c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-events-interaction-v0-common\4.0.22+0d4d74479c\fabric-events-interaction-v0-common-4.0.22+0d4d74479c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-gametest-api-v1-common\3.1.8+39ce47f59c\fabric-gametest-api-v1-common-3.1.8+39ce47f59c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-item-api-v1-common\11.4.2+5e29f1899c\fabric-item-api-v1-common-11.4.2+5e29f1899c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-item-group-api-v1-common\4.2.12+9ec45cd89c\fabric-item-group-api-v1-common-4.2.12+9ec45cd89c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-lifecycle-events-v1-common\2.6.2+db4dfd859c\fabric-lifecycle-events-v1-common-2.6.2+db4dfd859c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-loot-api-v2-common\3.0.54+3f89f5a59c\fabric-loot-api-v2-common-3.0.54+3f89f5a59c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-loot-api-v3-common\2.0.1+f40817309c\fabric-loot-api-v3-common-2.0.1+f40817309c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-message-api-v1-common\6.1.0+8efa0e499c\fabric-message-api-v1-common-6.1.0+8efa0e499c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-particles-v1-common\4.1.6+c1dce2189c\fabric-particles-v1-common-4.1.6+c1dce2189c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-recipe-api-v1-common\8.1.13+39ce47f59c\fabric-recipe-api-v1-common-8.1.13+39ce47f59c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-object-builder-api-v1-common\21.1.5+946cf7899c\fabric-object-builder-api-v1-common-21.1.5+946cf7899c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-screen-handler-api-v1-common\1.3.134+d32f812d9c\fabric-screen-handler-api-v1-common-1.3.134+d32f812d9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-registry-sync-v0-common\6.1.25+9ec45cd89c\fabric-registry-sync-v0-common-6.1.25+9ec45cd89c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-networking-api-v1-common\5.0.0+d32f812d9c\fabric-networking-api-v1-common-5.0.0+d32f812d9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-rendering-fluids-v1-common\3.1.29+fa6cb72b9c\fabric-rendering-fluids-v1-common-3.1.29+fa6cb72b9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-tag-api-v1-common\1.2.0+75110b049c\fabric-tag-api-v1-common-1.2.0+75110b049c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-resource-loader-v0-common\3.1.10+fa6cb72b9c\fabric-resource-loader-v0-common-3.1.10+fa6cb72b9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-api-base-common\0.4.63+9ec45cd89c\fabric-api-base-common-0.4.63+9ec45cd89c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-biome-api-v1-common\16.0.10+fa6cb72b9c\fabric-biome-api-v1-common-16.0.10+fa6cb72b9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-block-api-v1-common\1.1.2+78dbe4fb9c\fabric-block-api-v1-common-1.1.2+78dbe4fb9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-block-view-api-v2-common\1.0.30+d32f812d9c\fabric-block-view-api-v2-common-1.0.30+d32f812d9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-crash-report-info-v1-common\0.3.14+fa6cb72b9c\fabric-crash-report-info-v1-common-0.3.14+fa6cb72b9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-dimensions-v1-common\4.0.18+75fa737a9c\fabric-dimensions-v1-common-4.0.18+75fa737a9c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-game-rule-api-v1-common\1.0.71+9ec45cd89c\fabric-game-rule-api-v1-common-1.0.71+9ec45cd89c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-resource-conditions-api-v1-common\5.0.23+908cbc919c\fabric-resource-conditions-api-v1-common-5.0.23+908cbc919c.jar;C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_6_1_21_6_build_1_v2\net\fabricmc\fabric-api\fabric-transitive-access-wideners-v1-common\6.4.0+ac3e15d19c\fabric-transitive-access-wideners-v1-common-6.4.0+ac3e15d19c.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-reader\3.20.0\8f15415b022a25b473e8e16c28ae913186ffb9c4\jline-reader-3.20.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-terminal\3.20.0\d0ddcc708ddf527a3454c941b7b9225cc83a15ff\jline-terminal-3.20.0.jar;C:\MyWorks\MyMods\AiVillagersFabric\build\classes\java\main;C:\MyWorks\MyMods\AiVillagersFabric\build\resources\main
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
   size_t InitialHeapSize                          = 130023424                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MarkStackSizeMax                         = 536870912                                 {product} {ergonomic}
   size_t MaxHeapSize                              = 2063597568                                {product} {ergonomic}
   size_t MaxNewSize                               = 1237319680                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832704                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122945536                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122880000                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2063597568                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\AutoFirma\AutoFirma;C:\Program Files\dotnet\;C:\Program Files\Micron Technology\Micron Storage Executive;C:\Program Files\Git\cmd;C:\Users\<USER>\Downloads\platform-tools-latest-windows\platform-tools;C:\Program Files\AzureConnectedMachineAgent\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\bin;
USERNAME=gonza
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 190 Stepping 0, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 0 days 6:27 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 190 stepping 0 microcode 0xe, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv, serialize, rdtscp, rdpid, fsrm, gfni, f16c, cet_ibt, cet_ss
Processor Information for the first 8 processors :
  Max Mhz: 1800, Current Mhz: 1800, Mhz Limit: 1800

Memory: 4k page, system-wide physical 7865M (1980M free)
TotalPageFile size 16057M (AvailPageFile size 6084M)
current process WorkingSet (physical memory assigned to process): 386M, peak: 1282M
current process commit charge ("private bytes"): 1387M, peak: 1424M

vm_info: Java HotSpot(TM) 64-Bit Server VM (23.0.2+7-58) for windows-amd64 JRE (23.0.2+7-58), built on 2024-11-29T09:34:55Z with MS VC++ 17.6 (VS2022)

END.
