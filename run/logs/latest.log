[20:03:32] [main/INFO] (FabricLoader/GameProvider) Loading Minecraft 1.21.8 with Fabric Loader 0.16.14
[20:03:32] [main/INFO] (FabricLoader) Loading 49 mods:
	- ai_villagers 1.0.0
	- fabric-api 0.129.0+1.21.8
	- fabric-api-base 0.4.64+9ec45cd8f3
	- fabric-api-lookup-api-v1 1.6.100+946bf4c3f3
	- fabric-biome-api-v1 16.0.11+946bf4c3f3
	- fabric-block-api-v1 1.1.3+946bf4c3f3
	- fabric-block-view-api-v2 1.0.31+946bf4c3f3
	- fabric-client-gametest-api-v1 4.2.5+8a98c3fcf3
	- fabric-command-api-v2 2.2.53+946bf4c3f3
	- fabric-content-registries-v0 10.0.18+946bf4c3f3
	- fabric-convention-tags-v1 2.1.40+7f945d5bf3
	- fabric-convention-tags-v2 2.15.5+eb5df52ff3
	- fabric-crash-report-info-v1 0.3.15+946bf4c3f3
	- fabric-data-attachment-api-v1 1.8.10+946bf4c3f3
	- fabric-data-generation-api-v1 23.2.4+55e55d29f3
	- fabric-dimensions-v1 4.0.19+946bf4c3f3
	- fabric-entity-events-v1 2.1.1+c9e47273f3
	- fabric-events-interaction-v0 4.0.23+946bf4c3f3
	- fabric-game-rule-api-v1 1.0.73+c64c9c5bf3
	- fabric-gametest-api-v1 3.1.9+39ce47f5f3
	- fabric-item-api-v1 11.4.3+946bf4c3f3
	- fabric-item-group-api-v1 4.2.13+946bf4c3f3
	- fabric-key-binding-api-v1 1.0.65+946bf4c3f3
	- fabric-lifecycle-events-v1 2.6.3+db4dfd85f3
	- fabric-loot-api-v2 3.0.55+3f89f5a5f3
	- fabric-loot-api-v3 2.0.2+946bf4c3f3
	- fabric-message-api-v1 6.1.1+946bf4c3f3
	- fabric-model-loading-api-v1 5.2.5+946bf4c3f3
	- fabric-networking-api-v1 5.0.1+946bf4c3f3
	- fabric-object-builder-api-v1 21.1.7+946bf4c3f3
	- fabric-particles-v1 4.1.7+946bf4c3f3
	- fabric-recipe-api-v1 8.1.14+946bf4c3f3
	- fabric-registry-sync-v0 6.1.27+946bf4c3f3
	- fabric-renderer-api-v1 7.0.2+946bf4c3f3
	- fabric-renderer-indigo 4.0.2+946bf4c3f3
	- fabric-rendering-fluids-v1 3.1.30+fa6cb72bf3
	- fabric-rendering-v1 12.4.0+e8d43c76f3
	- fabric-resource-conditions-api-v1 5.0.24+946bf4c3f3
	- fabric-resource-loader-v0 3.1.11+946bf4c3f3
	- fabric-screen-api-v1 2.1.0+277ecf7df3
	- fabric-screen-handler-api-v1 1.3.136+946bf4c3f3
	- fabric-sound-api-v1 1.0.42+946bf4c3f3
	- fabric-tag-api-v1 1.2.1+946bf4c3f3
	- fabric-transfer-api-v1 6.0.5+946bf4c3f3
	- fabric-transitive-access-wideners-v1 6.4.1+ac3e15d1f3
	- fabricloader 0.16.14
	- java 23
	- minecraft 1.21.8
	- mixinextras 0.4.1
[20:03:33] [main/INFO] (FabricLoader/Mixin) SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.5+mixin.0.8.7/22f9eb729e216a091673a574a5906dc1b9027fb3/sponge-mixin-0.15.5+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[20:03:33] [main/INFO] (FabricLoader/Mixin) Loaded Fabric development mappings for mixin remapper!
[20:03:33] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_21
[20:03:33] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_22
[20:03:38] [main/INFO] (FabricLoader/MixinExtras|Service) Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[20:03:43] [Datafixer Bootstrap/INFO] (Minecraft) 269 Datafixer optimizations took 2806 milliseconds
[20:03:59] [Render thread/INFO] (Minecraft) Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[20:03:59] [Render thread/INFO] (Minecraft) Setting user: Player517
[20:03:59] [Render thread/INFO] (ai_villagers) Inicializando ai_villagers!
[20:03:59] [Render thread/INFO] (ai_villagers) Items personalizados de AiVillagers registrados
[20:03:59] [Render thread/INFO] (ai_villagers) Entidades personalizadas de AiVillagers registradas
[20:04:00] [Render thread/INFO] (Indigo) [Indigo] Registering Indigo renderer!
[20:04:00] [Render thread/INFO] (ai_villagers) Inicializando cliente de AiVillagers
[20:04:00] [Render thread/INFO] (ai_villagers) Cliente de AiVillagers inicializado correctamente
[20:04:01] [Render thread/INFO] (Minecraft) Backend library: LWJGL version 3.3.3-snapshot
[20:04:01] [Render thread/INFO] (Minecraft) Using optional rendering extensions: GL_ARB_buffer_storage, GL_KHR_debug, GL_ARB_vertex_attrib_binding, GL_ARB_direct_state_access
[20:04:03] [Render thread/INFO] (Minecraft) Reloading ResourceManager: vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader
[20:04:03] [Worker-Main-7/INFO] (Minecraft) Found unifont_all_no_pua-16.0.03.hex, loading
[20:04:03] [Worker-Main-3/INFO] (Minecraft) Found unifont_pua-16.0.03.hex, loading
[20:04:04] [Worker-Main-7/INFO] (Minecraft) Found unifont_jp_patch-16.0.03.hex, loading
[20:04:07] [Render thread/INFO] (Minecraft) OpenAL initialized on device OpenAL Soft on Altavoces (2- Realtek(R) Audio)
[20:04:07] [Render thread/INFO] (Minecraft) Sound engine started
[20:04:07] [Render thread/INFO] (Minecraft) Created: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[20:04:09] [Render thread/INFO] (Minecraft) Created: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[20:04:09] [Render thread/INFO] (Minecraft) Created: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[20:04:09] [Render thread/INFO] (Minecraft) Created: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[20:04:09] [Render thread/INFO] (Minecraft) Created: 2048x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[20:04:10] [Render thread/INFO] (Minecraft) Created: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[20:04:10] [Render thread/INFO] (Minecraft) Created: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[20:04:10] [Render thread/INFO] (Minecraft) Created: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[20:04:10] [Render thread/INFO] (Minecraft) Created: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[20:04:10] [Render thread/INFO] (Minecraft) Created: 64x64x0 minecraft:textures/atlas/map_decorations.png-atlas
[20:04:10] [Render thread/INFO] (Minecraft) Created: 512x256x0 minecraft:textures/atlas/particles.png-atlas
[20:04:10] [Render thread/INFO] (Minecraft) Created: 512x256x0 minecraft:textures/atlas/paintings.png-atlas
[20:04:10] [Render thread/INFO] (Minecraft) Created: 1024x512x0 minecraft:textures/atlas/gui.png-atlas
[20:04:19] [Render thread/INFO] (Minecraft) Loaded 1407 recipes
[20:04:19] [Render thread/INFO] (Minecraft) Loaded 1520 advancements
[20:04:19] [Render thread/INFO] (BiomeModificationImpl) Applied 0 biome modifications to 0 of 65 new biomes in 2.573 ms
[20:04:19] [Server thread/INFO] (Minecraft) Starting integrated minecraft server version 1.21.8
[20:04:19] [Server thread/INFO] (Minecraft) Generating keypair
[20:04:20] [Server thread/INFO] (Minecraft) Preparing start region for dimension minecraft:overworld
[20:04:21] [Render thread/INFO] (Minecraft) Preparando zona de aparición: 0 %
[20:04:21] [Render thread/INFO] (Minecraft) Preparando zona de aparición: 0 %
[20:04:21] [Render thread/INFO] (Minecraft) Time elapsed: 1064 ms
[20:04:21] [Server thread/INFO] (Minecraft) Changing view distance to 6, from 10
[20:04:21] [Server thread/INFO] (Minecraft) Changing simulation distance to 10, from 0
[20:04:25] [Server thread/INFO] (Minecraft) Player517[local:E:16911297] logged in with entity id 19 at (0.02835200428284522, -58.62967185861312, 3.2814784931900367)
[20:04:25] [Server thread/INFO] (Minecraft) Player517 se ha unido a la partida
[20:04:25] [Server thread/INFO] (Minecraft) Player517 ha conseguido el progreso [La Edad de Hierro]
[20:04:25] [Render thread/INFO] (Minecraft) [System] [CHAT] Player517 ha conseguido el progreso [La Edad de Hierro]
[20:04:26] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 2 reached during a single frame. New capacity will be 4.
[20:04:26] [Render thread/INFO] (Minecraft) Loaded 74 advancements
[20:04:26] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 4 reached during a single frame. New capacity will be 8.
[20:04:27] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 8 reached during a single frame. New capacity will be 16.
[20:04:27] [Server thread/INFO] (Minecraft) Saving and pausing game...
[20:04:27] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 16 reached during a single frame. New capacity will be 32.
[20:04:27] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 32 reached during a single frame. New capacity will be 64.
[20:04:27] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:overworld
[20:04:27] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 64 reached during a single frame. New capacity will be 128.
[20:04:27] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:the_end
[20:04:27] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:the_nether
[20:04:48] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 128 reached during a single frame. New capacity will be 256.
[20:04:52] [Render thread/INFO] (Minecraft) Loaded 84 advancements
[20:05:01] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[20:05:01] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=0, y=-60, z=1} (se liberará automáticamente en 20 segundos)
[20:05:01] [Server thread/INFO] (ai_villagers) Pescador Pescador reutilizando spot exitoso: BlockPos{x=0, y=-60, z=1}
[20:05:03] [Server thread/INFO] (Minecraft) [Player517: Se ha cambiado la vel. de tics objetivo a 120.0 por segundo]
[20:05:03] [Render thread/INFO] (Minecraft) [System] [CHAT] Se ha cambiado la vel. de tics objetivo a 120.0 por segundo
[20:05:03] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó agua inicial Mutable{x=0, y=-61, z=0} - no es 100% agua
[20:05:03] [Server thread/ERROR] (Minecraft) Encountered an unexpected exception
net.minecraft.util.crash.CrashException: Ticking entity
	at knot/net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1158) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.server.MinecraftServer.tick(MinecraftServer.java:1039) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.server.integrated.IntegratedServer.tick(IntegratedServer.java:114) ~[minecraft-clientOnly-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:773) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.server.MinecraftServer.method_29739(MinecraftServer.java:300) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at java.base/java.lang.Thread.run(Thread.java:1575) [?:?]
Caused by: java.lang.NullPointerException: Cannot invoke "net.minecraft.util.math.BlockPos.getX()" because "this.state.targetWaterPosition" is null
	at knot/net.minecraft.entity.passive.VillagerEntity.initiateFishingSequence(VillagerEntity.java:7773) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.entity.passive.VillagerEntity.executeNavigationToFishingSpotTarget(VillagerEntity.java:7380) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.entity.passive.VillagerEntity.processFishingStateMachine(VillagerEntity.java:7290) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.entity.passive.VillagerEntity.handler$znn000$ai_villagers$processFisherVillagerBehavior(VillagerEntity.java:7235) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.entity.passive.VillagerEntity.tick(VillagerEntity.java) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.server.world.ServerWorld.tickEntity(ServerWorld.java:771) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.world.World.tickEntity(World.java:547) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.server.world.ServerWorld.method_31420(ServerWorld.java:387) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.world.EntityList.forEach(EntityList.java:82) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.server.world.ServerWorld.tick(ServerWorld.java:370) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	at knot/net.minecraft.server.MinecraftServer.tickWorlds(MinecraftServer.java:1154) ~[minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar:?]
	... 5 more
[20:05:03] [Server thread/ERROR] (Minecraft) This crash report has been saved to: C:\MyWorks\MyMods\AiVillagersFabric\run\crash-reports\crash-2025-08-01_20.05.03-server.txt
[20:05:03] [Server thread/INFO] (Minecraft) Stopping server
[20:05:03] [Server thread/INFO] (Minecraft) Saving players
[20:05:03] [Server thread/INFO] (Minecraft) Saving worlds
[20:05:03] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:overworld
