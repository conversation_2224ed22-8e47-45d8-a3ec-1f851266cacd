[20:03:32] [main/INFO] (FabricLoader/GameProvider) Loading Minecraft 1.21.8 with Fabric Loader 0.16.14
[20:03:32] [main/DEBUG] (FabricLoader/GamePatch) Found game constructor: net.minecraft.client.main.Main -> net.minecraft.client.MinecraftClient
[20:03:32] [main/DEBUG] (FabricLoader/GamePatch) Patching game constructor <init>(Lnet/minecraft/client/RunArgs;)V
[20:03:32] [main/DEBUG] (FabricLoader/GamePatch) Run directory field is thought to be net/minecraft/client/MinecraftClient/runDirectory
[20:03:32] [main/DEBUG] (FabricLoader/GamePatch) Applying brand name hook to net/minecraft/client/ClientBrandRetriever::getClientModName
[20:03:32] [main/DEBUG] (FabricLoader/GamePatch) Applying brand name hook to net/minecraft/server/MinecraftServer::getServerModName
[20:03:32] [main/DEBUG] (FabricLoader/Mappings) Loading mappings took 293 ms
[20:03:32] [main/DEBUG] (FabricLoader/GamePatch) Patched 3 classs
[20:03:32] [main/DEBUG] (FabricLoader/Discovery) Skipping missing class path group entry C:\MyWorks\MyMods\AiVillagersFabric\out\production\classes
[20:03:32] [main/DEBUG] (FabricLoader/Discovery) Skipping missing class path group entry C:\MyWorks\MyMods\AiVillagersFabric\out\production\resources
[20:03:32] [main/DEBUG] (FabricLoader/Discovery) Skipping missing class path group entry C:\MyWorks\MyMods\AiVillagersFabric\out\client\classes
[20:03:32] [main/DEBUG] (FabricLoader/Discovery) Skipping missing class path group entry C:\MyWorks\MyMods\AiVillagersFabric\out\client\resources
[20:03:32] [main/DEBUG] (FabricLoader/Discovery) Mod discovery time: 52,8 ms
[20:03:32] [main/DEBUG] (FabricLoader/Resolution) Mod resolution time: 134,8 ms
[20:03:32] [main/INFO] (FabricLoader) Loading 49 mods:
	- ai_villagers 1.0.0
	- fabric-api 0.129.0+1.21.8
	- fabric-api-base 0.4.64+9ec45cd8f3
	- fabric-api-lookup-api-v1 1.6.100+946bf4c3f3
	- fabric-biome-api-v1 16.0.11+946bf4c3f3
	- fabric-block-api-v1 1.1.3+946bf4c3f3
	- fabric-block-view-api-v2 1.0.31+946bf4c3f3
	- fabric-client-gametest-api-v1 4.2.5+8a98c3fcf3
	- fabric-command-api-v2 2.2.53+946bf4c3f3
	- fabric-content-registries-v0 10.0.18+946bf4c3f3
	- fabric-convention-tags-v1 2.1.40+7f945d5bf3
	- fabric-convention-tags-v2 2.15.5+eb5df52ff3
	- fabric-crash-report-info-v1 0.3.15+946bf4c3f3
	- fabric-data-attachment-api-v1 1.8.10+946bf4c3f3
	- fabric-data-generation-api-v1 23.2.4+55e55d29f3
	- fabric-dimensions-v1 4.0.19+946bf4c3f3
	- fabric-entity-events-v1 2.1.1+c9e47273f3
	- fabric-events-interaction-v0 4.0.23+946bf4c3f3
	- fabric-game-rule-api-v1 1.0.73+c64c9c5bf3
	- fabric-gametest-api-v1 3.1.9+39ce47f5f3
	- fabric-item-api-v1 11.4.3+946bf4c3f3
	- fabric-item-group-api-v1 4.2.13+946bf4c3f3
	- fabric-key-binding-api-v1 1.0.65+946bf4c3f3
	- fabric-lifecycle-events-v1 2.6.3+db4dfd85f3
	- fabric-loot-api-v2 3.0.55+3f89f5a5f3
	- fabric-loot-api-v3 2.0.2+946bf4c3f3
	- fabric-message-api-v1 6.1.1+946bf4c3f3
	- fabric-model-loading-api-v1 5.2.5+946bf4c3f3
	- fabric-networking-api-v1 5.0.1+946bf4c3f3
	- fabric-object-builder-api-v1 21.1.7+946bf4c3f3
	- fabric-particles-v1 4.1.7+946bf4c3f3
	- fabric-recipe-api-v1 8.1.14+946bf4c3f3
	- fabric-registry-sync-v0 6.1.27+946bf4c3f3
	- fabric-renderer-api-v1 7.0.2+946bf4c3f3
	- fabric-renderer-indigo 4.0.2+946bf4c3f3
	- fabric-rendering-fluids-v1 3.1.30+fa6cb72bf3
	- fabric-rendering-v1 12.4.0+e8d43c76f3
	- fabric-resource-conditions-api-v1 5.0.24+946bf4c3f3
	- fabric-resource-loader-v0 3.1.11+946bf4c3f3
	- fabric-screen-api-v1 2.1.0+277ecf7df3
	- fabric-screen-handler-api-v1 1.3.136+946bf4c3f3
	- fabric-sound-api-v1 1.0.42+946bf4c3f3
	- fabric-tag-api-v1 1.2.1+946bf4c3f3
	- fabric-transfer-api-v1 6.0.5+946bf4c3f3
	- fabric-transitive-access-wideners-v1 6.4.1+ac3e15d1f3
	- fabricloader 0.16.14
	- java 23
	- minecraft 1.21.8
	- mixinextras 0.4.1
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-renderer-api-v1-client\7.0.2+946bf4c3f3\fabric-renderer-api-v1-client-7.0.2+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-crash-report-info-v1-common\0.3.15+946bf4c3f3\fabric-crash-report-info-v1-common-0.3.15+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-biome-api-v1-common\16.0.11+946bf4c3f3\fabric-biome-api-v1-common-16.0.11+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-screen-handler-api-v1-common\1.3.136+946bf4c3f3\fabric-screen-handler-api-v1-common-1.3.136+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-screen-handler-api-v1-client\1.3.136+946bf4c3f3\fabric-screen-handler-api-v1-client-1.3.136+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-resource-loader-v0-common\3.1.11+946bf4c3f3\fabric-resource-loader-v0-common-3.1.11+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-resource-loader-v0-client\3.1.11+946bf4c3f3\fabric-resource-loader-v0-client-3.1.11+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-renderer-indigo-client\4.0.2+946bf4c3f3\fabric-renderer-indigo-client-4.0.2+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-client-gametest-api-v1-client\4.2.5+8a98c3fcf3\fabric-client-gametest-api-v1-client-4.2.5+8a98c3fcf3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-content-registries-v0-common\10.0.18+946bf4c3f3\fabric-content-registries-v0-common-10.0.18+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-loot-api-v3-common\2.0.2+946bf4c3f3\fabric-loot-api-v3-common-2.0.2+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-message-api-v1-common\6.1.1+946bf4c3f3\fabric-message-api-v1-common-6.1.1+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-message-api-v1-client\6.1.1+946bf4c3f3\fabric-message-api-v1-client-6.1.1+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-item-api-v1-common\11.4.3+946bf4c3f3\fabric-item-api-v1-common-11.4.3+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-item-api-v1-client\11.4.3+946bf4c3f3\fabric-item-api-v1-client-11.4.3+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-rendering-fluids-v1-common\3.1.30+fa6cb72bf3\fabric-rendering-fluids-v1-common-3.1.30+fa6cb72bf3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-rendering-fluids-v1-client\3.1.30+fa6cb72bf3\fabric-rendering-fluids-v1-client-3.1.30+fa6cb72bf3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-screen-api-v1-client\2.1.0+277ecf7df3\fabric-screen-api-v1-client-2.1.0+277ecf7df3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-key-binding-api-v1-client\1.0.65+946bf4c3f3\fabric-key-binding-api-v1-client-1.0.65+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-lifecycle-events-v1-common\2.6.3+db4dfd85f3\fabric-lifecycle-events-v1-common-2.6.3+db4dfd85f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-lifecycle-events-v1-client\2.6.3+db4dfd85f3\fabric-lifecycle-events-v1-client-2.6.3+db4dfd85f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-tag-api-v1-common\1.2.1+946bf4c3f3\fabric-tag-api-v1-common-1.2.1+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-tag-api-v1-client\1.2.1+946bf4c3f3\fabric-tag-api-v1-client-1.2.1+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-recipe-api-v1-common\8.1.14+946bf4c3f3\fabric-recipe-api-v1-common-8.1.14+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-recipe-api-v1-client\8.1.14+946bf4c3f3\fabric-recipe-api-v1-client-8.1.14+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-game-rule-api-v1-common\1.0.73+c64c9c5bf3\fabric-game-rule-api-v1-common-1.0.73+c64c9c5bf3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-game-rule-api-v1-client\1.0.73+c64c9c5bf3\fabric-game-rule-api-v1-client-1.0.73+c64c9c5bf3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-model-loading-api-v1-client\5.2.5+946bf4c3f3\fabric-model-loading-api-v1-client-5.2.5+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-sound-api-v1-client\1.0.42+946bf4c3f3\fabric-sound-api-v1-client-1.0.42+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-particles-v1-common\4.1.7+946bf4c3f3\fabric-particles-v1-common-4.1.7+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-particles-v1-client\4.1.7+946bf4c3f3\fabric-particles-v1-client-4.1.7+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-object-builder-api-v1-common\21.1.7+946bf4c3f3\fabric-object-builder-api-v1-common-21.1.7+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-object-builder-api-v1-client\21.1.7+946bf4c3f3\fabric-object-builder-api-v1-client-21.1.7+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-gametest-api-v1-common\3.1.9+39ce47f5f3\fabric-gametest-api-v1-common-3.1.9+39ce47f5f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-data-generation-api-v1-common\23.2.4+55e55d29f3\fabric-data-generation-api-v1-common-23.2.4+55e55d29f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-data-generation-api-v1-client\23.2.4+55e55d29f3\fabric-data-generation-api-v1-client-23.2.4+55e55d29f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-dimensions-v1-common\4.0.19+946bf4c3f3\fabric-dimensions-v1-common-4.0.19+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-loot-api-v2-common\3.0.55+3f89f5a5f3\fabric-loot-api-v2-common-3.0.55+3f89f5a5f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-registry-sync-v0-common\6.1.27+946bf4c3f3\fabric-registry-sync-v0-common-6.1.27+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-registry-sync-v0-client\6.1.27+946bf4c3f3\fabric-registry-sync-v0-client-6.1.27+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-entity-events-v1-common\2.1.1+c9e47273f3\fabric-entity-events-v1-common-2.1.1+c9e47273f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-resource-conditions-api-v1-common\5.0.24+946bf4c3f3\fabric-resource-conditions-api-v1-common-5.0.24+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-events-interaction-v0-common\4.0.23+946bf4c3f3\fabric-events-interaction-v0-common-4.0.23+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-events-interaction-v0-client\4.0.23+946bf4c3f3\fabric-events-interaction-v0-client-4.0.23+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-command-api-v2-common\2.2.53+946bf4c3f3\fabric-command-api-v2-common-2.2.53+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-command-api-v2-client\2.2.53+946bf4c3f3\fabric-command-api-v2-client-2.2.53+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-api-common\0.129.0+1.21.8\fabric-api-common-0.129.0+1.21.8.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-networking-api-v1-common\5.0.1+946bf4c3f3\fabric-networking-api-v1-common-5.0.1+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-networking-api-v1-client\5.0.1+946bf4c3f3\fabric-networking-api-v1-client-5.0.1+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-block-api-v1-common\1.1.3+946bf4c3f3\fabric-block-api-v1-common-1.1.3+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\build\classes\java\main to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\build\resources\main to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\build\classes\java\client to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\build\resources\client to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-data-attachment-api-v1-common\1.8.10+946bf4c3f3\fabric-data-attachment-api-v1-common-1.8.10+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-data-attachment-api-v1-client\1.8.10+946bf4c3f3\fabric-data-attachment-api-v1-client-1.8.10+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-api-base-common\0.4.64+9ec45cd8f3\fabric-api-base-common-0.4.64+9ec45cd8f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-item-group-api-v1-common\4.2.13+946bf4c3f3\fabric-item-group-api-v1-common-4.2.13+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-item-group-api-v1-client\4.2.13+946bf4c3f3\fabric-item-group-api-v1-client-4.2.13+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-api-lookup-api-v1-common\1.6.100+946bf4c3f3\fabric-api-lookup-api-v1-common-1.6.100+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-transfer-api-v1-common\6.0.5+946bf4c3f3\fabric-transfer-api-v1-common-6.0.5+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-transfer-api-v1-client\6.0.5+946bf4c3f3\fabric-transfer-api-v1-client-6.0.5+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-convention-tags-v2-common\2.15.5+eb5df52ff3\fabric-convention-tags-v2-common-2.15.5+eb5df52ff3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-block-view-api-v2-common\1.0.31+946bf4c3f3\fabric-block-view-api-v2-common-1.0.31+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-block-view-api-v2-client\1.0.31+946bf4c3f3\fabric-block-view-api-v2-client-1.0.31+946bf4c3f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-rendering-v1-client\12.4.0+e8d43c76f3\fabric-rendering-v1-client-12.4.0+e8d43c76f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.github.llamalad7\mixinextras-fabric\0.4.1\8d1a9e96afb990367fa1f904d17580d164da72e3\mixinextras-fabric-0.4.1.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-transitive-access-wideners-v1-common\6.4.1+ac3e15d1f3\fabric-transitive-access-wideners-v1-common-6.4.1+ac3e15d1f3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-convention-tags-v1-common\2.1.40+7f945d5bf3\fabric-convention-tags-v1-common-2.1.40+7f945d5bf3.jar to classpath.
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.renderer.DebugHudClient for mod fabric-renderer-api-v1 (key client)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.screenhandler.client.ClientNetworking for mod fabric-screen-handler-api-v1 (key client)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.screenhandler.Networking for mod fabric-screen-handler-api-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.client.indigo.Indigo for mod fabric-renderer-indigo (key client)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.client.event.lifecycle.ClientLifecycleEventsImpl for mod fabric-lifecycle-events-v1 (key client)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.event.lifecycle.LifecycleEventsImpl for mod fabric-lifecycle-events-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.tag.TagInit for mod fabric-tag-api-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.recipe.ingredient.client.CustomIngredientSyncClient for mod fabric-recipe-api-v1 (key client)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.recipe.ingredient.CustomIngredientInit for mod fabric-recipe-api-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.recipe.ingredient.CustomIngredientSync for mod fabric-recipe-api-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.client.model.loading.CustomUnbakedBlockStateModelInit for mod fabric-model-loading-api-v1 (key client)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.client.particle.ExtendedBlockStateParticleEffectSyncClient for mod fabric-particles-v1 (key client)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.particle.ExtendedBlockStateParticleEffectSync for mod fabric-particles-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.gametest.FabricGameTestModInitializer for mod fabric-gametest-api-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.loot.v2.LootInitializer for mod fabric-loot-api-v2 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.client.registry.sync.FabricRegistryClientInit for mod fabric-registry-sync-v0 (key client)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.registry.sync.FabricRegistryInit for mod fabric-registry-sync-v0 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.resource.conditions.ResourceConditionsImpl for mod fabric-resource-conditions-api-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.event.interaction.InteractionEventsRouter for mod fabric-events-interaction-v0 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.networking.client.ClientNetworkingImpl::clientInit for mod fabric-networking-api-v1 (key client)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.networking.CommonPacketsImpl::init for mod fabric-networking-api-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.networking.NetworkingImpl::init for mod fabric-networking-api-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer AiVillagers.core.AiVillagersClient for mod ai_villagers (key client)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer AiVillagers.core.AiVillagersMod for mod ai_villagers (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.attachment.client.AttachmentSyncClient for mod fabric-data-attachment-api-v1 (key client)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.attachment.AttachmentEntrypoint for mod fabric-data-attachment-api-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.attachment.sync.AttachmentSync for mod fabric-data-attachment-api-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.lookup.ApiLookupImpl for mod fabric-api-lookup-api-v1 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.tag.convention.v2.TranslationConventionLogWarnings for mod fabric-convention-tags-v2 (key main)
[20:03:32] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.tag.convention.ConventionLogWarnings for mod fabric-convention-tags-v1 (key main)
[20:03:33] [main/INFO] (FabricLoader/Mixin) SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.5+mixin.0.8.7/22f9eb729e216a091673a574a5906dc1b9027fb3/sponge-mixin-0.15.5+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Initialising Mixin Platform Manager
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Adding mixin platform agents for container ContainerHandleURI(file:///C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.14/5778d47f536bf2c63ed2abc1a56f5a1c129e34a/fabric-loader-0.16.14.jar)
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Instancing new MixinPlatformAgentDefault for ContainerHandleURI(file:///C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.14/5778d47f536bf2c63ed2abc1a56f5a1c129e34a/fabric-loader-0.16.14.jar)
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) MixinPlatformAgentDefault accepted container ContainerHandleURI(file:///C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.14/5778d47f536bf2c63ed2abc1a56f5a1c129e34a/fabric-loader-0.16.14.jar)
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Instancing new MixinPlatformAgentDefault for ContainerHandleURI(file:///C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.14/5778d47f536bf2c63ed2abc1a56f5a1c129e34a/fabric-loader-0.16.14.jar)
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) MixinPlatformAgentDefault accepted container ContainerHandleURI(file:///C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.14/5778d47f536bf2c63ed2abc1a56f5a1c129e34a/fabric-loader-0.16.14.jar)
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Processing prepare() for PlatformAgent[MixinPlatformAgentDefault:ContainerHandleURI(file:///C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.14/5778d47f536bf2c63ed2abc1a56f5a1c129e34a/fabric-loader-0.16.14.jar)]
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Processing prepare() for PlatformAgent[MixinPlatformAgentDefault:ContainerHandleURI(file:///C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.14/5778d47f536bf2c63ed2abc1a56f5a1c129e34a/fabric-loader-0.16.14.jar)]
[20:03:33] [main/INFO] (FabricLoader/Mixin) Loaded Fabric development mappings for mixin remapper!
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_21 specified by fabric-renderer-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_21
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-renderer-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-crash-report-info-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-biome-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-screen-handler-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-resource-loader-v0.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-resource-loader-v0.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-renderer-indigo.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-client-gametest-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-content-registries-v0.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-loot-api-v3.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-message-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-message-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-item-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-item-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-rendering-fluids-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-screen-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-key-binding-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-lifecycle-events-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-lifecycle-events-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-tag-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-recipe-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-game-rule-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-game-rule-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-model-loading-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-sound-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-particles-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-particles-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-object-builder-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-object-builder-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-gametest-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-data-generation-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-data-generation-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-dimensions-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-loot-api-v2.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-registry-sync-v0.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-registry-sync-v0.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-entity-events-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-resource-conditions-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-events-interaction-v0.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-events-interaction-v0.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-command-api-v2.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-command-api-v2.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-networking-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-networking-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-block-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_22 specified by ai_villagers.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_22
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config ai_villagers.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config ai_villagers.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_21 specified by fabric-data-attachment-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-data-attachment-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_21 specified by fabric-data-attachment-api-v1.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-data-attachment-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_21 specified by fabric-item-group-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-item-group-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_21 specified by fabric-item-group-api-v1.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-item-group-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_21 specified by fabric-api-lookup-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-api-lookup-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_21 specified by fabric-transfer-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-transfer-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_21 specified by fabric-convention-tags-api-v2.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-convention-tags-api-v2.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_21 specified by fabric-block-view-api-v2.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-block-view-api-v2.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_21 specified by fabric-block-view-api-v2.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-block-view-api-v2.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_21 specified by fabric-rendering-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-rendering-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\minecraftMaven\net\minecraft\minecraft-clientOnly-b07cf08c30\1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2\minecraft-clientOnly-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\minecraftMaven\net\minecraft\minecraft-common-b07cf08c30\1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2\minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\build\classes\java\client to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\build\resources\client to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\fabric-loom\1.21.8\net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2\mappings.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.github.llamalad7\mixinextras-fabric\0.4.1\8d1a9e96afb990367fa1f904d17580d164da72e3\mixinextras-fabric-0.4.1.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.fabricmc\dev-launch-injector\0.2.1+build.8\da8bef7e6e2f952da707f282bdb46882a0fce5e3\dev-launch-injector-0.2.1+build.8.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-annotations\2.13.4\858c6cc78e1f08a885b1613e1d817c829df70a6e\jackson-annotations-2.13.4.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-core\2.13.4\cf934c681294b97ef6d80082faeefbe1edadf56\jackson-core-2.13.4.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-databind\********\325c06bdfeb628cfb80ebaaf1a26cc1eb558a585\jackson-databind-********.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.oshi\oshi-core\6.6.5\e1099981fd15dc4236c4499d82aba1276fb43a9a\oshi-core-6.6.5.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.stephenc.jcip\jcip-annotations\1.0-1\ef31541dd28ae2cefdd17c7ebf352d93e9058c63\jcip-annotations-1.0-1.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.2\c4a06a64e650562f30b7bf9aaec1bfed43aca12b\failureaccess-1.0.2.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\33.3.1-jre\852f8b363da0111e819460021ca693cacca3e8db\guava-33.3.1-jre.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.ibm.icu\icu4j\76.1\215f3a8e936d4069344bd75f2b1368fd58112894\icu4j-76.1.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.microsoft.azure\msal4j\1.17.2\a6211e3d71d0388929babaa0ff0951b30d001852\msal4j-1.17.2.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\blocklist\1.0.10\5c685c5ffa94c4cd39496c7184c1d122e515ecef\blocklist-1.0.10.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\brigadier\1.3.10\d15b53a14cf20fdcaa98f731af5dda654452c010\brigadier-1.3.10.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\datafixerupper\8.0.16\67d4de6d7f95d89bcf5862995fb854ebaec02a34\datafixerupper-8.0.16.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\jtracy\1.0.29\6f07dcb6a2e595c7ee2ca43b67e5d1c018ca0770\jtracy-1.0.29.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\jtracy\1.0.29\e05332cb31c7ae582dc8d8bd1bffd47c2ff7636f\jtracy-1.0.29-natives-windows.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\patchy\2.2.10\da05971b07cbb379d002cf7eaec6a2048211fefc\patchy-2.2.10.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\text2speech\1.18.11\e853a12cdd6ba4f4836e8f4bf3b37844a13482b6\text2speech-1.18.11.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.nimbusds\content-type\2.3\e3aa0be212d7a42839a8f3f506f5b990bcce0222\content-type-2.3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.nimbusds\lang-tag\1.7\97c73ecd70bc7e8eefb26c5eea84f251a63f1031\lang-tag-1.7.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.nimbusds\nimbus-jose-jwt\9.40\42b1dfa0360e4062951b070bac52dd8d96fd7b38\nimbus-jose-jwt-9.40.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.nimbusds\oauth2-oidc-sdk\11.18\7c7ec4f4066625ff07a711ad856fa04da1ff9de\oauth2-oidc-sdk-11.18.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.17.1\973638b7149d333563584137ebf13a691bb60579\commons-codec-1.17.1.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.17.0\ddcc8433eb019fb48fe25207c0278143f3e1d7e2\commons-io-2.17.0.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-logging\commons-logging\1.3.4\b9fc14968d63a8b8a8a2c1885fe3e90564239708\commons-logging-1.3.4.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-buffer\4.1.118.Final\7022990af1e0d449f9d5322035899745e19735c5\netty-buffer-4.1.118.Final.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-codec\4.1.118.Final\307f665c08ce57333121de4f460479fc0c3c94d4\netty-codec-4.1.118.Final.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-common\4.1.118.Final\4bb0f9899146484fa89f7b9bc27389d5b8e2ecde\netty-common-4.1.118.Final.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-handler\4.1.118.Final\30ebb05b6b0fb071dbfcf713017c4a767a97bb9b\netty-handler-4.1.118.Final.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-resolver\4.1.118.Final\28c378c19c1779eca1104b400452627f3ebc4aea\netty-resolver-4.1.118.Final.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-classes-epoll\4.1.118.Final\376ce95507066f0e755d97c1c8bcd6c33f657617\netty-transport-classes-epoll-4.1.118.Final.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-native-unix-common\4.1.118.Final\9da25a94e6a0edac90da0bc7894e5a54efcb866b\netty-transport-native-unix-common-4.1.118.Final.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport\4.1.118.Final\5a27232e5d08218722d94ca14f0b1b4576e7711c\netty-transport-4.1.118.Final.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\it.unimi.dsi\fastutil\8.5.15\1e885b40c9563ab0d3899b871fd0b30e958705dc\fastutil-8.5.15.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.java.dev.jna\jna-platform\5.15.0\86b502cad57d45da172b5e3231c537b042e296ef\jna-platform-5.15.0.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.java.dev.jna\jna\5.15.0\1ee1d80ff44f08280188f7c0e740d57207841ac\jna-5.15.0.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minidev\accessors-smart\2.5.1\19b820261eb2e7de7d5bde11d1c06e4501dd7e5f\accessors-smart-2.5.1.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minidev\json-smart\2.5.1\4c11d2808d009132dfbbf947ebf37de6bf266c8e\json-smart-2.5.1.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.sf.jopt-simple\jopt-simple\5.0.4\4fdac2fbe92dfad86aa6e9301736f6b4342a3f5c\jopt-simple-5.0.4.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-compress\1.27.1\a19151084758e2fbb6b41eddaa88e7b8ff4e6599\commons-compress-1.27.1.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-lang3\3.17.0\b17d2136f0460dcc0d2016ceefca8723bdf4ee70\commons-lang3-3.17.0.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.httpcomponents\httpclient\4.5.14\1194890e6f56ec29177673f2f12d0b8e627dec98\httpclient-4.5.14.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.httpcomponents\httpcore\4.4.16\51cf043c87253c9f58b539c9f7e44c8894223850\httpcore-4.4.16.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jcraft\jorbis\0.0.17\8872d22b293e8f5d7d56ff92be966e6dc28ebdc6\jorbis-0.0.17.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.joml\joml\1.10.8\fc0a71dad90a2cf41d82a76156a0e700af8e4f8d\joml-1.10.8.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-freetype\3.3.3\a0db6c84a8becc8ca05f9dbfa985edc348a824c7\lwjgl-freetype-3.3.3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-freetype\3.3.3\81091b006dbb43fab04c8c638e9ac87c51b4096d\lwjgl-freetype-3.3.3-natives-windows.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-freetype\3.3.3\82028265a0a2ff33523ca75137ada7dc176e5210\lwjgl-freetype-3.3.3-natives-windows-arm64.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-freetype\3.3.3\15a8c1de7f51d07a92eae7ce1222557073a0c0c3\lwjgl-freetype-3.3.3-natives-windows-x86.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.3\efa1eb78c5ccd840e9f329717109b5e892d72f8e\lwjgl-glfw-3.3.3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.3\e449e28b4891fc423c54c85fbc5bb0b9efece67a\lwjgl-glfw-3.3.3-natives-windows.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.3\f27018dc74f6289574502b46cce55d52817554e2\lwjgl-glfw-3.3.3-natives-windows-arm64.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.3\32334f3fd5270a59bad9939a93115acb6de36dcf\lwjgl-glfw-3.3.3-natives-windows-x86.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.3\b543467b7ff3c6920539a88ee602d34098628be5\lwjgl-jemalloc-3.3.3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.3\426222fc027602a5f21b9c0fe79cde6a4c7a011f\lwjgl-jemalloc-3.3.3-natives-windows.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.3\ba1f3fed0ee4be0217eaa41c5bbfb4b9b1383c33\lwjgl-jemalloc-3.3.3-natives-windows-arm64.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.3\f6063b6e0f23be483c5c88d84ce51b39dc69126c\lwjgl-jemalloc-3.3.3-natives-windows-x86.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.3\daada81ceb5fc0c291fbfdd4433cb8d9423577f2\lwjgl-openal-3.3.3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.3\cf83862ae95d98496b26915024c7e666d8ab1c8f\lwjgl-openal-3.3.3-natives-windows.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.3\8e0615235116b9e4160dfe87bec90f5f6378bf72\lwjgl-openal-3.3.3-natives-windows-arm64.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.3\87b8d5050e3adb46bb58fe1cb2669a4a48fce10d\lwjgl-openal-3.3.3-natives-windows-x86.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.3\2f6b0147078396a58979125a4c947664e98293a\lwjgl-opengl-3.3.3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.3\e6c1eec8be8a71951b830a4d69efc01c6531900c\lwjgl-opengl-3.3.3-natives-windows.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.3\65e956d3735a1abdc82eff4baec1b61174697d4b\lwjgl-opengl-3.3.3-natives-windows-arm64.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.3\d32d833dcaa2f355a886eaf21f0408b5f03241d\lwjgl-opengl-3.3.3-natives-windows-x86.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.3\25dd6161988d7e65f71d5065c99902402ee32746\lwjgl-stb-3.3.3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.3\1d9facdf6541de114b0f963be33505b7679c78cb\lwjgl-stb-3.3.3-natives-windows.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.3\a584ab44de569708871f0a79561f4d8c37487f2c\lwjgl-stb-3.3.3-natives-windows-arm64.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.3\b5c874687b9aac1a936501d4ed2c49567fd1b575\lwjgl-stb-3.3.3-natives-windows-x86.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.3\82d755ca94b102e9ca77283b9e2dc46d1b15fbe5\lwjgl-tinyfd-3.3.3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.3\a6697981b0449a5087c1d546fc08b4f73e8f98c9\lwjgl-tinyfd-3.3.3-natives-windows.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.3\a88c494f3006eb91a7433b12a3a55a9a6c20788b\lwjgl-tinyfd-3.3.3-natives-windows-arm64.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.3\c336c84ee88cccb495c6ffa112395509e7378e8a\lwjgl-tinyfd-3.3.3-natives-windows-x86.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.3\29589b5f87ed335a6c7e7ee6a5775f81f97ecb84\lwjgl-3.3.3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.3\a5ed18a2b82fc91b81f40d717cb1f64c9dcb0540\lwjgl-3.3.3-natives-windows.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.3\e9aca8c5479b520a2a7f0d542a118140e812c5e8\lwjgl-3.3.3-natives-windows-arm64.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.3\9e670718e050aeaeea0c2d5b907cffb142f2e58f\lwjgl-3.3.3-natives-windows-x86.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lz4\lz4-java\1.8.0\4b986a99445e49ea5fbf5d149c4b63f6ed6c6780\lz4-java-1.8.0.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\minecraftMaven\net\minecraft\minecraft-clientOnly-b07cf08c30\1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2\minecraft-clientOnly-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\minecraftMaven\net\minecraft\minecraft-common-b07cf08c30\1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2\minecraft-common-b07cf08c30-1.21.8-net.fabricmc.yarn.1_21_8.1.21.8+build.1-v2.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-native-epoll\4.1.118.Final\7e6b89e3746acb7cf6f0aad993bbd058fc6d912e\netty-transport-native-epoll-4.1.118.Final-linux-x86_64.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-native-epoll\4.1.118.Final\82f94d0a9d837f6b6a580379373310ff7288c0f8\netty-transport-native-epoll-4.1.118.Final-linux-aarch_64.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-transfer-api-v1-client\6.0.5+946bf4c3f3\fabric-transfer-api-v1-client-6.0.5+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-command-api-v2-client\2.2.53+946bf4c3f3\fabric-command-api-v2-client-2.2.53+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-data-generation-api-v1-client\23.2.4+55e55d29f3\fabric-data-generation-api-v1-client-23.2.4+55e55d29f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-data-attachment-api-v1-client\1.8.10+946bf4c3f3\fabric-data-attachment-api-v1-client-1.8.10+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-events-interaction-v0-client\4.0.23+946bf4c3f3\fabric-events-interaction-v0-client-4.0.23+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-item-api-v1-client\11.4.3+946bf4c3f3\fabric-item-api-v1-client-11.4.3+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-item-group-api-v1-client\4.2.13+946bf4c3f3\fabric-item-group-api-v1-client-4.2.13+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-lifecycle-events-v1-client\2.6.3+db4dfd85f3\fabric-lifecycle-events-v1-client-2.6.3+db4dfd85f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-message-api-v1-client\6.1.1+946bf4c3f3\fabric-message-api-v1-client-6.1.1+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-model-loading-api-v1-client\5.2.5+946bf4c3f3\fabric-model-loading-api-v1-client-5.2.5+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-particles-v1-client\4.1.7+946bf4c3f3\fabric-particles-v1-client-4.1.7+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-recipe-api-v1-client\8.1.14+946bf4c3f3\fabric-recipe-api-v1-client-8.1.14+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-object-builder-api-v1-client\21.1.7+946bf4c3f3\fabric-object-builder-api-v1-client-21.1.7+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-screen-handler-api-v1-client\1.3.136+946bf4c3f3\fabric-screen-handler-api-v1-client-1.3.136+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-registry-sync-v0-client\6.1.27+946bf4c3f3\fabric-registry-sync-v0-client-6.1.27+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-networking-api-v1-client\5.0.1+946bf4c3f3\fabric-networking-api-v1-client-5.0.1+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-renderer-indigo-client\4.0.2+946bf4c3f3\fabric-renderer-indigo-client-4.0.2+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-renderer-api-v1-client\7.0.2+946bf4c3f3\fabric-renderer-api-v1-client-7.0.2+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-rendering-fluids-v1-client\3.1.30+fa6cb72bf3\fabric-rendering-fluids-v1-client-3.1.30+fa6cb72bf3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-rendering-v1-client\12.4.0+e8d43c76f3\fabric-rendering-v1-client-12.4.0+e8d43c76f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-tag-api-v1-client\1.2.1+946bf4c3f3\fabric-tag-api-v1-client-1.2.1+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-resource-loader-v0-client\3.1.11+946bf4c3f3\fabric-resource-loader-v0-client-3.1.11+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-screen-api-v1-client\2.1.0+277ecf7df3\fabric-screen-api-v1-client-2.1.0+277ecf7df3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-block-view-api-v2-client\1.0.31+946bf4c3f3\fabric-block-view-api-v2-client-1.0.31+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-client-gametest-api-v1-client\4.2.5+8a98c3fcf3\fabric-client-gametest-api-v1-client-4.2.5+8a98c3fcf3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-game-rule-api-v1-client\1.0.73+c64c9c5bf3\fabric-game-rule-api-v1-client-1.0.73+c64c9c5bf3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-key-binding-api-v1-client\1.0.65+946bf4c3f3\fabric-key-binding-api-v1-client-1.0.65+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-sound-api-v1-client\1.0.42+946bf4c3f3\fabric-sound-api-v1-client-1.0.42+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-api-common\0.129.0+1.21.8\fabric-api-common-0.129.0+1.21.8.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-transfer-api-v1-common\6.0.5+946bf4c3f3\fabric-transfer-api-v1-common-6.0.5+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-api-lookup-api-v1-common\1.6.100+946bf4c3f3\fabric-api-lookup-api-v1-common-1.6.100+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-command-api-v2-common\2.2.53+946bf4c3f3\fabric-command-api-v2-common-2.2.53+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-content-registries-v0-common\10.0.18+946bf4c3f3\fabric-content-registries-v0-common-10.0.18+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-data-generation-api-v1-common\23.2.4+55e55d29f3\fabric-data-generation-api-v1-common-23.2.4+55e55d29f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-convention-tags-v1-common\2.1.40+7f945d5bf3\fabric-convention-tags-v1-common-2.1.40+7f945d5bf3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-convention-tags-v2-common\2.15.5+eb5df52ff3\fabric-convention-tags-v2-common-2.15.5+eb5df52ff3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-data-attachment-api-v1-common\1.8.10+946bf4c3f3\fabric-data-attachment-api-v1-common-1.8.10+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-entity-events-v1-common\2.1.1+c9e47273f3\fabric-entity-events-v1-common-2.1.1+c9e47273f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-events-interaction-v0-common\4.0.23+946bf4c3f3\fabric-events-interaction-v0-common-4.0.23+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-gametest-api-v1-common\3.1.9+39ce47f5f3\fabric-gametest-api-v1-common-3.1.9+39ce47f5f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-item-api-v1-common\11.4.3+946bf4c3f3\fabric-item-api-v1-common-11.4.3+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-item-group-api-v1-common\4.2.13+946bf4c3f3\fabric-item-group-api-v1-common-4.2.13+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-lifecycle-events-v1-common\2.6.3+db4dfd85f3\fabric-lifecycle-events-v1-common-2.6.3+db4dfd85f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-loot-api-v2-common\3.0.55+3f89f5a5f3\fabric-loot-api-v2-common-3.0.55+3f89f5a5f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-loot-api-v3-common\2.0.2+946bf4c3f3\fabric-loot-api-v3-common-2.0.2+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-message-api-v1-common\6.1.1+946bf4c3f3\fabric-message-api-v1-common-6.1.1+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-particles-v1-common\4.1.7+946bf4c3f3\fabric-particles-v1-common-4.1.7+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-recipe-api-v1-common\8.1.14+946bf4c3f3\fabric-recipe-api-v1-common-8.1.14+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-object-builder-api-v1-common\21.1.7+946bf4c3f3\fabric-object-builder-api-v1-common-21.1.7+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-screen-handler-api-v1-common\1.3.136+946bf4c3f3\fabric-screen-handler-api-v1-common-1.3.136+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-registry-sync-v0-common\6.1.27+946bf4c3f3\fabric-registry-sync-v0-common-6.1.27+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-networking-api-v1-common\5.0.1+946bf4c3f3\fabric-networking-api-v1-common-5.0.1+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-rendering-fluids-v1-common\3.1.30+fa6cb72bf3\fabric-rendering-fluids-v1-common-3.1.30+fa6cb72bf3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-tag-api-v1-common\1.2.1+946bf4c3f3\fabric-tag-api-v1-common-1.2.1+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-resource-loader-v0-common\3.1.11+946bf4c3f3\fabric-resource-loader-v0-common-3.1.11+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-api-base-common\0.4.64+9ec45cd8f3\fabric-api-base-common-0.4.64+9ec45cd8f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-biome-api-v1-common\16.0.11+946bf4c3f3\fabric-biome-api-v1-common-16.0.11+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-block-api-v1-common\1.1.3+946bf4c3f3\fabric-block-api-v1-common-1.1.3+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-block-view-api-v2-common\1.0.31+946bf4c3f3\fabric-block-view-api-v2-common-1.0.31+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-crash-report-info-v1-common\0.3.15+946bf4c3f3\fabric-crash-report-info-v1-common-0.3.15+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-dimensions-v1-common\4.0.19+946bf4c3f3\fabric-dimensions-v1-common-4.0.19+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-game-rule-api-v1-common\1.0.73+c64c9c5bf3\fabric-game-rule-api-v1-common-1.0.73+c64c9c5bf3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-resource-conditions-api-v1-common\5.0.24+946bf4c3f3\fabric-resource-conditions-api-v1-common-5.0.24+946bf4c3f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\.gradle\loom-cache\remapped_mods\net_fabricmc_yarn_1_21_8_1_21_8_build_1_v2\net\fabricmc\fabric-api\fabric-transitive-access-wideners-v1-common\6.4.1+ac3e15d1f3\fabric-transitive-access-wideners-v1-common-6.4.1+ac3e15d1f3.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-reader\3.20.0\8f15415b022a25b473e8e16c28ae913186ffb9c4\jline-reader-3.20.0.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-terminal\3.20.0\d0ddcc708ddf527a3454c941b7b9225cc83a15ff\jline-terminal-3.20.0.jar to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\build\classes\java\main to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Knot) Adding C:\MyWorks\MyMods\AiVillagersFabric\build\resources\main to classpath.
[20:03:33] [main/DEBUG] (FabricLoader/Entrypoint) No subscribers for entrypoint 'preLaunch'
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Preparing mixins for MixinEnvironment[DEFAULT]
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-renderer-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-crash-report-info-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-biome-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-screen-handler-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-resource-loader-v0.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-resource-loader-v0.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-renderer-indigo.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-client-gametest-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-content-registries-v0.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-loot-api-v3.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-message-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-message-api-v1.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-item-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-item-api-v1.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-rendering-fluids-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-screen-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-key-binding-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-lifecycle-events-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-lifecycle-events-v1.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-tag-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-recipe-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-game-rule-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-game-rule-api-v1.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-model-loading-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-sound-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-particles-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-particles-v1.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-object-builder-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-object-builder-v1.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-gametest-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-data-generation-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-data-generation-api-v1.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-dimensions-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-loot-api-v2.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-registry-sync-v0.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-registry-sync-v0.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-entity-events-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-resource-conditions-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-events-interaction-v0.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-events-interaction-v0.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-command-api-v2.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-command-api-v2.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-networking-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-networking-api-v1.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-block-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config ai_villagers.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config ai_villagers.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-data-attachment-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-data-attachment-api-v1.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-item-group-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-item-group-api-v1.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-api-lookup-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-transfer-api-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-convention-tags-api-v2.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-block-view-api-v2.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-block-view-api-v2.client.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-rendering-v1.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Selecting config mixinextras.init.mixins.json
[20:03:33] [main/DEBUG] (FabricLoader/MixinExtras|Service) com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1) is taking over from null
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @Inject with org.spongepowered.asm.mixin.injection.struct.CallbackInjectionInfo
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @ModifyArg with org.spongepowered.asm.mixin.injection.struct.ModifyArgInjectionInfo
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @ModifyArgs with org.spongepowered.asm.mixin.injection.struct.ModifyArgsInjectionInfo
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @Redirect with org.spongepowered.asm.mixin.injection.struct.RedirectInjectionInfo
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @ModifyVariable with org.spongepowered.asm.mixin.injection.struct.ModifyVariableInjectionInfo
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @ModifyConstant with org.spongepowered.asm.mixin.injection.struct.ModifyConstantInjectionInfo
[20:03:33] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-renderer-api-v1.mixins.json (26)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/client/render/model/BakedModelManager$1 is public in fabric-renderer-api-v1.mixins.json:client.sprite.BakedModelManager1Mixin from mod fabric-renderer-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-crash-report-info-v1.mixins.json (2)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-biome-api-v1.mixins.json (9)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/world/biome/source/MultiNoiseBiomeSourceParameterList$Preset$1 is public in fabric-biome-api-v1.mixins.json:NetherBiomePresetMixin from mod fabric-biome-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-screen-handler-api-v1.mixins.json (2)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-resource-loader-v0.mixins.json (15)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-resource-loader-v0.client.mixins.json (8)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/client/option/GameOptions$3 is public in fabric-resource-loader-v0.client.mixins.json:GameOptionsWriteVisitorMixin from mod fabric-resource-loader-v0 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-renderer-indigo.mixins.json (8)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-client-gametest-api-v1.mixins.json (25)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-content-registries-v0.mixins.json (17)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-loot-api-v3.mixins.json (8)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-message-api-v1.mixins.json (2)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-message-api-v1.client.mixins.json (2)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-item-api-v1.mixins.json (17)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-item-api-v1.client.mixins.json (3)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-rendering-fluids-v1.mixins.json (1)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-screen-api-v1.mixins.json (7)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-key-binding-api-v1.mixins.json (2)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-lifecycle-events-v1.mixins.json (11)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/server/world/ServerWorld$ServerEntityHandler is public in fabric-lifecycle-events-v1.mixins.json:ServerWorldServerEntityHandlerMixin from mod fabric-lifecycle-events-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-lifecycle-events-v1.client.mixins.json (7)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/client/world/ClientWorld$ClientEntityHandler is public in fabric-lifecycle-events-v1.client.mixins.json:ClientWorldClientEntityHandlerMixin from mod fabric-lifecycle-events-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-tag-api-v1.mixins.json (5)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/registry/SimpleRegistry$2 is public in fabric-tag-api-v1.mixins.json:SimpleRegistry2Mixin from mod fabric-tag-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/registry/SimpleRegistry$3 is public in fabric-tag-api-v1.mixins.json:SimpleRegistry3Mixin from mod fabric-tag-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/registry/SimpleRegistry$TagLookup$2 is public in fabric-tag-api-v1.mixins.json:SimpleRegistryTagLookup2Accessor from mod fabric-tag-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-recipe-api-v1.mixins.json (5)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-game-rule-api-v1.mixins.json (5)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/server/command/GameRuleCommand$1 is public in fabric-game-rule-api-v1.mixins.json:GameRuleCommandVisitorMixin from mod fabric-game-rule-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-game-rule-api-v1.client.mixins.json (3)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/client/gui/screen/world/EditGameRulesScreen$RuleListWidget$1 is public in fabric-game-rule-api-v1.client.mixins.json:RuleListWidgetVisitorMixin from mod fabric-game-rule-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-model-loading-api-v1.mixins.json (6)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-sound-api-v1.mixins.json (2)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-particles-v1.mixins.json (8)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-particles-v1.client.mixins.json (4)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-object-builder-v1.mixins.json (10)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-object-builder-v1.client.mixins.json (4)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-gametest-api-v1.mixins.json (4)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-data-generation-api-v1.mixins.json (17)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/data/DataCache$CachedData is public in fabric-data-generation-api-v1.mixins.json:DataCacheCachedDataMixin from mod fabric-data-generation-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/data/tag/ProvidedTagBuilder$1 is public in fabric-data-generation-api-v1.mixins.json:ProvidedTagBuilderMixin$ProvidedTagBuilder1Mixin from mod fabric-data-generation-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/data/tag/ProvidedTagBuilder$2 is public in fabric-data-generation-api-v1.mixins.json:ProvidedTagBuilderMixin$ProvidedTagBuilder2Mixin from mod fabric-data-generation-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-data-generation-api-v1.client.mixins.json (4)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-dimensions-v1.mixins.json (4)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-loot-api-v2.mixins.json (2)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-registry-sync-v0.mixins.json (16)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-registry-sync-v0.client.mixins.json (5)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/client/network/ClientRegistries$DynamicRegistries is public in fabric-registry-sync-v0.client.mixins.json:ClientRegistriesDynamicRegistriesAccessor from mod fabric-registry-sync-v0 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-entity-events-v1.mixins.json (7)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-resource-conditions-api-v1.mixins.json (8)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-events-interaction-v0.mixins.json (5)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/server/network/ServerPlayNetworkHandler$1 is public in fabric-events-interaction-v0.mixins.json:ServerPlayNetworkHandlerInteractEntityHandlerMixin from mod fabric-events-interaction-v0 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-events-interaction-v0.client.mixins.json (3)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-command-api-v2.mixins.json (5)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-command-api-v2.client.mixins.json (2)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-networking-api-v1.mixins.json (20)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/network/packet/CustomPayload$1 is public in fabric-networking-api-v1.mixins.json:CustomPayloadPacketCodecMixin from mod fabric-networking-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/server/world/ServerChunkLoadingManager$EntityTracker is public in fabric-networking-api-v1.mixins.json:accessor.EntityTrackerAccessor from mod fabric-networking-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-networking-api-v1.client.mixins.json (9)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-block-api-v1.mixins.json (5)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/world/chunk/ChunkSection$BlockStateCounter is public in fabric-block-api-v1.mixins.json:ChunkSectionBlockStateCounterMixin from mod fabric-block-api-v1 and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing ai_villagers.mixins.json (28)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net.minecraft.server.world.ServerChunkLoadingManager$EntityTracker is public in ai_villagers.mixins.json:core.ExperienceOrbTrackingMixin from mod ai_villagers and should be specified in value
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing ai_villagers.client.mixins.json (4)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-data-attachment-api-v1.mixins.json (14)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-data-attachment-api-v1.client.mixins.json (1)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-item-group-api-v1.mixins.json (3)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-item-group-api-v1.client.mixins.json (1)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-api-lookup-api-v1.mixins.json (2)
[20:03:34] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-transfer-api-v1.mixins.json (15)
[20:03:35] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-convention-tags-api-v2.mixins.json (1)
[20:03:35] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-block-view-api-v2.mixins.json (3)
[20:03:35] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-block-view-api-v2.client.mixins.json (2)
[20:03:35] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-rendering-v1.mixins.json (21)
[20:03:35] [main/DEBUG] (FabricLoader/Mixin) Preparing mixinextras.init.mixins.json (0)
[20:03:35] [main/DEBUG] (FabricLoader/Mixin) Inner class net/fabricmc/fabric/mixin/renderer/client/block/model/MultipartBlockStateModelMixin$1Key in net/fabricmc/fabric/mixin/renderer/client/block/model/MultipartBlockStateModelMixin on net/minecraft/client/render/model/MultipartBlockStateModel gets unique name net/minecraft/client/render/model/MultipartBlockStateModel$1Key$8091be187f474d3f9c096cd3b16f666d
[20:03:35] [main/DEBUG] (FabricLoader/Mixin) Inner class net/fabricmc/fabric/mixin/itemgroup/ItemGroupsMixin$1ItemGroupPosition in net/fabricmc/fabric/mixin/itemgroup/ItemGroupsMixin on net/minecraft/item/ItemGroups gets unique name net/minecraft/item/ItemGroups$1ItemGroupPosition$4b516f0e189f44bb95988a9e12374fbe
[20:03:35] [main/DEBUG] (FabricLoader/Mixin) Inner class net/fabricmc/fabric/mixin/transfer/ChiseledBookshelfBlockEntityMixin$1 in net/fabricmc/fabric/mixin/transfer/ChiseledBookshelfBlockEntityMixin on net/minecraft/block/entity/ChiseledBookshelfBlockEntity gets unique name net/minecraft/block/entity/ChiseledBookshelfBlockEntity$Anonymous$d2bde9883bdf43c585c5075440734812
[20:03:35] [main/DEBUG] (FabricLoader/Mixin) Prepared 406 mixins in 1,695 sec (4,2ms avg) (0ms load, 0ms transform, 0ms plugin)
[20:03:36] [main/DEBUG] (io.netty.util.internal.logging.InternalLoggerFactory) Using SLF4J as the default logging framework
[20:03:36] [main/DEBUG] (io.netty.util.ResourceLeakDetector) -Dio.netty.leakDetection.level: simple
[20:03:36] [main/DEBUG] (io.netty.util.ResourceLeakDetector) -Dio.netty.leakDetection.targetRecords: 4
[20:03:38] [main/INFO] (FabricLoader/MixinExtras|Service) Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[20:03:38] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @SugarWrapper with com.llamalad7.mixinextras.sugar.impl.SugarWrapperInjectionInfo
[20:03:38] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @FactoryRedirectWrapper with com.llamalad7.mixinextras.wrapper.factory.FactoryRedirectWrapperInjectionInfo
[20:03:38] [main/DEBUG] (FabricLoader/Mixin) Mixing TaggedChoiceMixin from fabric-dimensions-v1.mixins.json into com.mojang.datafixers.types.templates.TaggedChoice
[20:03:38] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:TaggedChoiceMixin from mod fabric-dimensions-v1->@Inject::onApply(Lcom/mojang/datafixers/util/Pair;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:38] [main/DEBUG] (FabricLoader/Mixin) Mixing TaggedChoiceTaggedChoiceTypeMixin from fabric-dimensions-v1.mixins.json into com.mojang.datafixers.types.templates.TaggedChoice$TaggedChoiceType
[20:03:38] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:TaggedChoiceTaggedChoiceTypeMixin from mod fabric-dimensions-v1->@Inject::onGetCodec(Ljava/lang/Object;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:40] [main/DEBUG] (FabricLoader/Mixin) Mixing Schema2832Mixin from fabric-dimensions-v1.mixins.json into net.minecraft.datafixer.schema.Schema2832
[20:03:41] [main/DEBUG] (FabricLoader/Mixin) Mixing SystemDetailsMixin from fabric-crash-report-info-v1.mixins.json into net.minecraft.util.SystemDetails
[20:03:41] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$appendMods$1(Lnet/fabricmc/loader/api/ModContainer;)Ljava/lang/String; to md3f1c3d$fabric-crash-report-info-v1$lambda$appendMods$1$0 in fabric-crash-report-info-v1.mixins.json:SystemDetailsMixin from mod fabric-crash-report-info-v1
[20:03:41] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fillSystemDetails$0()Ljava/lang/String; to md3f1c3d$fabric-crash-report-info-v1$lambda$fillSystemDetails$0$1 in fabric-crash-report-info-v1.mixins.json:SystemDetailsMixin from mod fabric-crash-report-info-v1
[20:03:41] [main/DEBUG] (FabricLoader/Mixin) fabric-crash-report-info-v1.mixins.json:SystemDetailsMixin from mod fabric-crash-report-info-v1->@Inject::fillSystemDetails(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:41] [main/DEBUG] (FabricLoader/Mixin) fabric-crash-report-info-v1.mixins.json:SystemDetailsMixin from mod fabric-crash-report-info-v1->@Inject::fillSystemDetails(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:41] [main/DEBUG] (FabricLoader/Mixin) fabric-crash-report-info-v1.mixins.json:SystemDetailsMixin from mod fabric-crash-report-info-v1->@Inject::fillSystemDetails(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:43] [Datafixer Bootstrap/INFO] (com.mojang.datafixers.DataFixerBuilder) 269 Datafixer optimizations took 2806 milliseconds
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Mixing BootstrapMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.Bootstrap
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$initialize$1(Lnet/minecraft/fluid/Fluid;)Ljava/util/Collection; to md3f1c3d$fabric-registry-sync-v0$lambda$initialize$1$0 in fabric-registry-sync-v0.mixins.json:BootstrapMixin from mod fabric-registry-sync-v0
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$initialize$0(Lnet/minecraft/block/Block;)Ljava/util/Collection; to md3f1c3d$fabric-registry-sync-v0$lambda$initialize$0$1 in fabric-registry-sync-v0.mixins.json:BootstrapMixin from mod fabric-registry-sync-v0
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:BootstrapMixin from mod fabric-registry-sync-v0->@Inject::initialize(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Mixing RegistryMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.registry.Registry
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Mixing RegistriesMixin from fabric-item-api-v1.mixins.json into net.minecraft.registry.Registries
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Mixing RegistriesAccessor from fabric-registry-sync-v0.mixins.json into net.minecraft.registry.Registries
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getROOT()Lnet/minecraft/registry/MutableRegistry; to getROOT$fabric-registry-sync-v0_$md$3f1c3d$0 in fabric-registry-sync-v0.mixins.json:RegistriesAccessor from mod fabric-registry-sync-v0
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Mixing RegistriesMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.registry.Registries
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:RegistriesMixin from mod fabric-item-api-v1->@Inject::modifyDefaultItemComponents(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:RegistriesMixin from mod fabric-registry-sync-v0->@Inject::init(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Mixing SimpleRegistryMixin from fabric-tag-api-v1.mixins.json into net.minecraft.registry.SimpleRegistry
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Mixing SimpleRegistryAccessor from fabric-registry-sync-v0.mixins.json into net.minecraft.registry.SimpleRegistry
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Mixing SimpleRegistryMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.registry.SimpleRegistry
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$init$4([Lnet/fabricmc/fabric/api/event/registry/RegistryIdRemapCallback;)Lnet/fabricmc/fabric/api/event/registry/RegistryIdRemapCallback; to md3f1c3d$fabric-registry-sync-v0$lambda$init$4$0 in fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$init$3([Lnet/fabricmc/fabric/api/event/registry/RegistryIdRemapCallback;Lnet/fabricmc/fabric/api/event/registry/RegistryIdRemapCallback$RemapState;)V to md3f1c3d$fabric-registry-sync-v0$lambda$init$3$1 in fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$init$2(ILnet/minecraft/util/Identifier;Ljava/lang/Object;)V to md3f1c3d$fabric-registry-sync-v0$lambda$init$2$2 in fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$init$1([Lnet/fabricmc/fabric/api/event/registry/RegistryEntryAddedCallback;)Lnet/fabricmc/fabric/api/event/registry/RegistryEntryAddedCallback; to md3f1c3d$fabric-registry-sync-v0$lambda$init$1$3 in fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0
[20:03:44] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$init$0([Lnet/fabricmc/fabric/api/event/registry/RegistryEntryAddedCallback;ILnet/minecraft/util/Identifier;Ljava/lang/Object;)V to md3f1c3d$fabric-registry-sync-v0$lambda$init$0$4 in fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::init(Lnet/minecraft/registry/RegistryKey;Lcom/mojang/serialization/Lifecycle;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::init(Lnet/minecraft/registry/RegistryKey;Lcom/mojang/serialization/Lifecycle;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::init(Lnet/minecraft/registry/RegistryKey;Lcom/mojang/serialization/Lifecycle;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::set(Lnet/minecraft/registry/RegistryKey;Ljava/lang/Object;Lnet/minecraft/registry/entry/RegistryEntryInfo;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing RegistryKeysMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.registry.RegistryKeys
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing ArgumentTypesAccessor from fabric-command-api-v2.mixins.json into net.minecraft.command.argument.ArgumentTypes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method fabric_getClassMap()Ljava/util/Map; to fabric_getClassMap$fabric-command-api-v2_$md$3f1c3d$0 in fabric-command-api-v2.mixins.json:ArgumentTypesAccessor from mod fabric-command-api-v2
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemGroupsMixin from fabric-item-group-api-v1.mixins.json into net.minecraft.item.ItemGroups
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$paginateGroups$0(Lnet/minecraft/registry/entry/RegistryEntry$Reference;Lnet/minecraft/registry/entry/RegistryEntry$Reference;)I to md3f1c3d$fabric-item-group-api-v1$lambda$paginateGroups$0$0 in fabric-item-group-api-v1.mixins.json:ItemGroupsMixin from mod fabric-item-group-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-item-group-api-v1.mixins.json:ItemGroupsMixin from mod fabric-item-group-api-v1->@Inject::deferDuplicateCheck(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-item-group-api-v1.mixins.json:ItemGroupsMixin from mod fabric-item-group-api-v1->@Inject::paginateGroups(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemGroupAccessor from fabric-item-group-api-v1.mixins.json into net.minecraft.item.ItemGroup
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemGroupMixin from fabric-item-group-api-v1.mixins.json into net.minecraft.item.ItemGroup
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$getStacks$0(Lnet/minecraft/item/ItemGroup;)Ljava/lang/IllegalStateException; to md3f1c3d$fabric-item-group-api-v1$lambda$getStacks$0$0 in fabric-item-group-api-v1.mixins.json:ItemGroupMixin from mod fabric-item-group-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-item-group-api-v1.mixins.json:ItemGroupMixin from mod fabric-item-group-api-v1->@Inject::getStacks(Lnet/minecraft/item/ItemGroup$DisplayContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-item-group-api-v1.mixins.json:ItemGroupMixin from mod fabric-item-group-api-v1->@Inject::getStacks(Lnet/minecraft/item/ItemGroup$DisplayContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-item-group-api-v1.mixins.json:ItemGroupMixin from mod fabric-item-group-api-v1->@Inject::getStacks(Lnet/minecraft/item/ItemGroup$DisplayContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing FireBlockMixin from fabric-content-registries-v0.mixins.json into net.minecraft.block.FireBlock
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:FireBlockMixin from mod fabric-content-registries-v0->@Inject::afterConstruct(Lnet/minecraft/block/AbstractBlock$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:FireBlockMixin from mod fabric-content-registries-v0->@Inject::afterConstruct(Lnet/minecraft/block/AbstractBlock$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:FireBlockMixin from mod fabric-content-registries-v0->@Inject::afterConstruct(Lnet/minecraft/block/AbstractBlock$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:FireBlockMixin from mod fabric-content-registries-v0->@Inject::getFabricBurnChance(Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:FireBlockMixin from mod fabric-content-registries-v0->@Inject::getFabricSpreadChance(Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockMixin from fabric-block-api-v1.mixins.json into net.minecraft.block.Block
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing AbstractBlockAccessor from fabric-content-registries-v0.mixins.json into net.minecraft.block.AbstractBlock
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockViewMixin from fabric-block-view-api-v2.mixins.json into net.minecraft.world.BlockView
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing EntityMixin from fabric-particles-v1.mixins.json into net.minecraft.entity.Entity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing EntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.Entity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing AttachmentTargetsMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.entity.Entity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_readAttachmentsFromNbt$3(Lnet/minecraft/storage/ReadView;Lnet/fabricmc/fabric/api/attachment/v1/AttachmentType;Ljava/lang/Object;)V to md3f1c3d$fabric-data-attachment-api-v1$lambda$fabric_readAttachmentsFromNbt$3$0 in fabric-data-attachment-api-v1.mixins.json:AttachmentTargetsMixin from mod fabric-data-attachment-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onAttachedSet$2(Lnet/fabricmc/fabric/api/attachment/v1/AttachmentType;)Lnet/fabricmc/fabric/api/event/Event; to md3f1c3d$fabric-data-attachment-api-v1$lambda$onAttachedSet$2$1 in fabric-data-attachment-api-v1.mixins.json:AttachmentTargetsMixin from mod fabric-data-attachment-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onAttachedSet$1([Lnet/fabricmc/fabric/api/attachment/v1/AttachmentTarget$OnAttachedSet;)Lnet/fabricmc/fabric/api/attachment/v1/AttachmentTarget$OnAttachedSet; to md3f1c3d$fabric-data-attachment-api-v1$lambda$onAttachedSet$1$2 in fabric-data-attachment-api-v1.mixins.json:AttachmentTargetsMixin from mod fabric-data-attachment-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onAttachedSet$0([Lnet/fabricmc/fabric/api/attachment/v1/AttachmentTarget$OnAttachedSet;Ljava/lang/Object;Ljava/lang/Object;)V to md3f1c3d$fabric-data-attachment-api-v1$lambda$onAttachedSet$0$3 in fabric-data-attachment-api-v1.mixins.json:AttachmentTargetsMixin from mod fabric-data-attachment-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing EntityMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.entity.Entity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_syncChange$0(Lnet/fabricmc/fabric/api/attachment/v1/AttachmentSyncPredicate;Lnet/fabricmc/fabric/impl/attachment/sync/s2c/AttachmentSyncPayloadS2C;Lnet/minecraft/server/network/ServerPlayerEntity;)V to md3f1c3d$fabric-data-attachment-api-v1$lambda$fabric_syncChange$0$4 in fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::readEntityAttachments(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::readEntityAttachments(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::readEntityAttachments(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::writeEntityAttachments(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::writeEntityAttachments(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::writeEntityAttachments(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing PlayerEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.player.PlayerEntity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:PlayerEntityMixin from mod fabric-entity-events-v1->@Inject::onTrySleep(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:PlayerEntityMixin from mod fabric-entity-events-v1->@Inject::onIsSleepingLongEnough(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing LivingEntityMixin from fabric-item-api-v1.mixins.json into net.minecraft.entity.LivingEntity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing LivingEntityMixin from fabric-lifecycle-events-v1.mixins.json into net.minecraft.entity.LivingEntity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing LivingEntityMixin from fabric-particles-v1.mixins.json into net.minecraft.entity.LivingEntity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing LivingEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.LivingEntity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing elytra.LivingEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.LivingEntity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing LivingEntityMixin from fabric-block-api-v1.mixins.json into net.minecraft.entity.LivingEntity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing health.VillagerDamageTaskInterruptMixin from ai_villagers.mixins.json into net.minecraft.entity.LivingEntity
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:health.VillagerDamageTaskInterruptMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:LivingEntityMixin from mod fabric-item-api-v1->@Inject::onGetPreferredEquipmentSlot(Lnet/minecraft/item/ItemStack;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:LivingEntityMixin from mod fabric-lifecycle-events-v1->@Inject::getEquipmentChanges(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:LivingEntityMixin from mod fabric-lifecycle-events-v1->@Inject::getEquipmentChanges(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:LivingEntityMixin from mod fabric-lifecycle-events-v1->@Inject::getEquipmentChanges(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::beforeDamage(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/damage/DamageSource;FLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::afterDamage(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/damage/DamageSource;FLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::afterDamage(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/damage/DamageSource;FLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::afterDamage(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/damage/DamageSource;FLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onSleep(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onSleep(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onSleep(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onWakeUp(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onWakeUp(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onWakeUp(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onIsSleepingInBed(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:elytra.LivingEntityMixin from mod fabric-entity-events-v1->@Inject::injectElytraTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:elytra.LivingEntityMixin from mod fabric-entity-events-v1->@Inject::injectElytraCheck(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) fabric-block-api-v1.mixins.json:LivingEntityMixin from mod fabric-block-api-v1->@Inject::allowTaggedBlocksForTrapdoorClimbing(Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:health.VillagerDamageTaskInterruptMixin from mod ai_villagers->@Inject::onDamage(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/damage/DamageSource;FLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:health.VillagerDamageTaskInterruptMixin from mod ai_villagers->@Inject::onDamage(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/damage/DamageSource;FLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:health.VillagerDamageTaskInterruptMixin from mod ai_villagers->@Inject::onDamage(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/damage/DamageSource;FLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing WorldMixin from fabric-lifecycle-events-v1.mixins.json into net.minecraft.world.World
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing AttachmentTargetsMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.world.World
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing WorldMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.world.World
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing WorldViewMixin from fabric-block-view-api-v2.mixins.json into net.minecraft.world.WorldView
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerWorldMixin from fabric-lifecycle-events-v1.mixins.json into net.minecraft.server.world.ServerWorld
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerWorldMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.server.world.ServerWorld
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_syncChange$1(Lnet/fabricmc/fabric/api/attachment/v1/AttachmentType;Lnet/fabricmc/fabric/impl/attachment/sync/s2c/AttachmentSyncPayloadS2C;Lnet/minecraft/server/network/ServerPlayerEntity;)V to md3f1c3d$fabric-data-attachment-api-v1$lambda$fabric_syncChange$1$0 in fabric-data-attachment-api-v1.mixins.json:ServerWorldMixin from mod fabric-data-attachment-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$createAttachmentsPersistentState$0(Lnet/minecraft/server/world/ServerWorld;)Lnet/fabricmc/fabric/impl/attachment/AttachmentPersistentState; to md3f1c3d$fabric-data-attachment-api-v1$lambda$createAttachmentsPersistentState$0$1 in fabric-data-attachment-api-v1.mixins.json:ServerWorldMixin from mod fabric-data-attachment-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerWorldMixin from fabric-api-lookup-api-v1.mixins.json into net.minecraft.server.world.ServerWorld
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_invalidateCache$5(Ljava/util/Map$Entry;)Z to md3f1c3d$fabric-api-lookup-api-v1$lambda$fabric_invalidateCache$5$2 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_invalidateCache$4(Ljava/lang/ref/WeakReference;)Z to md3f1c3d$fabric-api-lookup-api-v1$lambda$fabric_invalidateCache$4$3 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_invalidateCache$3(Ljava/lang/ref/WeakReference;)V to md3f1c3d$fabric-api-lookup-api-v1$lambda$fabric_invalidateCache$3$4 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_invalidateCache$2(Ljava/lang/ref/WeakReference;)Z to md3f1c3d$fabric-api-lookup-api-v1$lambda$fabric_invalidateCache$2$5 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_registerCache$1(Ljava/lang/ref/WeakReference;)Z to md3f1c3d$fabric-api-lookup-api-v1$lambda$fabric_registerCache$1$6 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[20:03:45] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_registerCache$0(Lnet/minecraft/util/math/BlockPos;)Ljava/util/List; to md3f1c3d$fabric-api-lookup-api-v1$lambda$fabric_registerCache$0$7 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::endWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::endWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::endWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:ServerWorldMixin from mod fabric-data-attachment-api-v1->@Inject::createAttachmentsPersistentState(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:ServerWorldMixin from mod fabric-data-attachment-api-v1->@Inject::createAttachmentsPersistentState(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:ServerWorldMixin from mod fabric-data-attachment-api-v1->@Inject::createAttachmentsPersistentState(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockStateMixin from fabric-block-api-v1.mixins.json into net.minecraft.block.BlockState
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) Mixing AbstractBlockAbstractBlockStateMixin from fabric-content-registries-v0.mixins.json into net.minecraft.block.AbstractBlock$AbstractBlockState
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) Mixing IdListMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.util.collection.IdList
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_remapIds$0(Lit/unimi/dsi/fastutil/ints/Int2IntMap;Ljava/lang/Object;Ljava/lang/Integer;)Ljava/lang/Integer; to md3f1c3d$fabric-registry-sync-v0$lambda$fabric_remapIds$0$0 in fabric-registry-sync-v0.mixins.json:IdListMixin from mod fabric-registry-sync-v0
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) Mixing RegistryByteBufMixin from fabric-networking-api-v1.mixins.json into net.minecraft.network.RegistryByteBuf
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) Mixing BlocksMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.block.Blocks
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$initShapeCache$0(ILnet/minecraft/util/Identifier;Lnet/minecraft/block/Block;)V to md3f1c3d$fabric-registry-sync-v0$lambda$initShapeCache$0$0 in fabric-registry-sync-v0.mixins.json:BlocksMixin from mod fabric-registry-sync-v0
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:BlocksMixin from mod fabric-registry-sync-v0->@Inject::initShapeCache(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) Mixing OxidizableMixin from fabric-content-registries-v0.mixins.json into net.minecraft.block.Oxidizable
[20:03:46] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:OxidizableMixin from mod fabric-content-registries-v0->@Inject::createOxidationLevelIncreasesMap(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing EntityTypeMixin from fabric-object-builder-v1.mixins.json into net.minecraft.entity.EntityType
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing core.VillagerDimensionOptimizationMixin from ai_villagers.mixins.json into net.minecraft.entity.EntityType
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:core.VillagerDimensionOptimizationMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$getCachedOrComputeDimensions$0(Lnet/minecraft/entity/EntityDimensions;Lnet/minecraft/entity/EntityType;)Ljava/util/Optional; to md3f1c3d$ai_villagers$lambda$getCachedOrComputeDimensions$0$0 in ai_villagers.mixins.json:core.VillagerDimensionOptimizationMixin from mod ai_villagers
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:EntityTypeMixin from mod fabric-object-builder-api-v1->@Inject::onAlwaysUpdateVelocity(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:EntityTypeMixin from mod fabric-object-builder-api-v1->@Inject::onCanPotentiallyExecuteCommands(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:core.VillagerDimensionOptimizationMixin from mod ai_villagers->@Inject::optimizeVillagerDimensions(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing FluidMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.fluid.Fluid
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:FluidMixin from mod fabric-transfer-api-v1->@Inject::hookGetBucketFillSound(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing ComponentMapBuilderMixin from fabric-item-api-v1.mixins.json into net.minecraft.component.ComponentMap$Builder
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing AttachmentTargetsMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.block.entity.BlockEntity
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockEntityMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.block.entity.BlockEntity
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_syncChange$0(Lnet/fabricmc/fabric/api/attachment/v1/AttachmentType;Lnet/fabricmc/fabric/impl/attachment/sync/s2c/AttachmentSyncPayloadS2C;Lnet/minecraft/server/network/ServerPlayerEntity;)V to md3f1c3d$fabric-data-attachment-api-v1$lambda$fabric_syncChange$0$0 in fabric-data-attachment-api-v1.mixins.json:BlockEntityMixin from mod fabric-data-attachment-api-v1
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockEntityMixin from fabric-block-view-api-v2.mixins.json into net.minecraft.block.entity.BlockEntity
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:BlockEntityMixin from mod fabric-data-attachment-api-v1->@Inject::readBlockEntityAttachments(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:BlockEntityMixin from mod fabric-data-attachment-api-v1->@Inject::readBlockEntityAttachments(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:BlockEntityMixin from mod fabric-data-attachment-api-v1->@Inject::readBlockEntityAttachments(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:BlockEntityMixin from mod fabric-data-attachment-api-v1->@Inject::writeBlockEntityAttachments(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:BlockEntityMixin from mod fabric-data-attachment-api-v1->@Inject::writeBlockEntityAttachments(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:BlockEntityMixin from mod fabric-data-attachment-api-v1->@Inject::writeBlockEntityAttachments(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockStateParticleEffectMixin from fabric-particles-v1.mixins.json into net.minecraft.particle.BlockStateParticleEffect
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing LockableContainerBlockEntityMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.entity.LockableContainerBlockEntity
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing NamedScreenHandlerFactoryMixin from fabric-screen-handler-api-v1.mixins.json into net.minecraft.screen.NamedScreenHandlerFactory
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing DetectorRailBlockMixin from fabric-object-builder-v1.mixins.json into net.minecraft.block.DetectorRailBlock
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$getCustomComparatorOutput$0(Lnet/minecraft/entity/Entity;)Z to md3f1c3d$fabric-object-builder-api-v1$lambda$getCustomComparatorOutput$0$0 in fabric-object-builder-v1.mixins.json:DetectorRailBlockMixin from mod fabric-object-builder-api-v1
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:DetectorRailBlockMixin from mod fabric-object-builder-api-v1->@Inject::getCustomComparatorOutput(Lnet/minecraft/block/BlockState;Lnet/minecraft/world/World;Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing MobEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.mob.MobEntity
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing world.VillageAnimalSpawnMarkingMixin from ai_villagers.mixins.json into net.minecraft.entity.mob.MobEntity
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSpawnMarkingMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSpawnMarkingMixin from mod ai_villagers->@Inject::onInitializeMarkVillageAnimal(Lnet/minecraft/world/ServerWorldAccess;Lnet/minecraft/world/LocalDifficulty;Lnet/minecraft/entity/SpawnReason;Lnet/minecraft/entity/EntityData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSpawnMarkingMixin from mod ai_villagers->@Inject::onInitializeMarkVillageAnimal(Lnet/minecraft/world/ServerWorldAccess;Lnet/minecraft/world/LocalDifficulty;Lnet/minecraft/entity/SpawnReason;Lnet/minecraft/entity/EntityData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSpawnMarkingMixin from mod ai_villagers->@Inject::onInitializeMarkVillageAnimal(Lnet/minecraft/world/ServerWorldAccess;Lnet/minecraft/world/LocalDifficulty;Lnet/minecraft/entity/SpawnReason;Lnet/minecraft/entity/EntityData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) Mixing ChiseledBookshelfBlockEntityMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.entity.ChiseledBookshelfBlockEntity
[20:03:47] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:ChiseledBookshelfBlockEntityMixin from mod fabric-transfer-api-v1->@Inject::setStackBypass(ILnet/minecraft/item/ItemStack;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockEntityTypeMixin from fabric-object-builder-v1.mixins.json into net.minecraft.block.entity.BlockEntityType
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockEntityTypeAccessor from fabric-api-lookup-api-v1.mixins.json into net.minecraft.block.entity.BlockEntityType
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:BlockEntityTypeMixin from mod fabric-object-builder-api-v1->@Inject::mutableBlocks(Lnet/minecraft/block/entity/BlockEntityType$BlockEntityFactory;Ljava/util/Set;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:BlockEntityTypeMixin from mod fabric-object-builder-api-v1->@Inject::mutableBlocks(Lnet/minecraft/block/entity/BlockEntityType$BlockEntityFactory;Ljava/util/Set;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:BlockEntityTypeMixin from mod fabric-object-builder-api-v1->@Inject::mutableBlocks(Lnet/minecraft/block/entity/BlockEntityType$BlockEntityFactory;Ljava/util/Set;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing protection.FarmlandVillagerProtectionMixin from ai_villagers.mixins.json into net.minecraft.block.FarmlandBlock
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:protection.FarmlandVillagerProtectionMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:protection.FarmlandVillagerProtectionMixin from mod ai_villagers->@Inject::protectFarmlandFromVillagers(Lnet/minecraft/world/World;Lnet/minecraft/block/BlockState;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/entity/Entity;DLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing AbstractFurnaceBlockEntityMixin from fabric-item-api-v1.mixins.json into net.minecraft.block.entity.AbstractFurnaceBlockEntity
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing AbstractFurnaceBlockEntityMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.entity.AbstractFurnaceBlockEntity
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:AbstractFurnaceBlockEntityMixin from mod fabric-item-api-v1->@Inject::getStackRemainder(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/BlockState;Lnet/minecraft/block/entity/AbstractFurnaceBlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:AbstractFurnaceBlockEntityMixin from mod fabric-transfer-api-v1->@Inject::setStackSuppressUpdate(ILnet/minecraft/item/ItemStack;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing WoodTypeMixin from fabric-object-builder-v1.client.mixins.json into net.minecraft.block.WoodType
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.client.mixins.json:WoodTypeMixin from mod fabric-object-builder-api-v1->@Inject::onReturnRegister(Lnet/minecraft/block/WoodType;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing JukeboxBlockEntityMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.entity.JukeboxBlockEntity
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:JukeboxBlockEntityMixin from mod fabric-transfer-api-v1->@Inject::setStackBypass(Lnet/minecraft/item/ItemStack;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing BrewingStandBlockEntityMixin from fabric-item-api-v1.mixins.json into net.minecraft.block.entity.BrewingStandBlockEntity
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:BrewingStandBlockEntityMixin from mod fabric-item-api-v1->@Inject::captureItemStack(Lnet/minecraft/world/World;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/util/collection/DefaultedList;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemStackMixin from fabric-item-api-v1.mixins.json into net.minecraft.item.ItemStack
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$hookDamage$0(Lorg/apache/commons/lang3/mutable/MutableBoolean;Ljava/util/function/Consumer;)V to md3f1c3d$fabric-item-api-v1$lambda$hookDamage$0$0 in fabric-item-api-v1.mixins.json:ItemStackMixin from mod fabric-item-api-v1
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemStackMixin from fabric-item-api-v1.client.mixins.json into net.minecraft.item.ItemStack
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemStackMixin from mod fabric-item-api-v1->@Inject::preAttributeModifiers(Lnet/minecraft/item/Item$TooltipContext;Lnet/minecraft/component/type/TooltipDisplayComponent;Lnet/minecraft/entity/player/PlayerEntity;Lnet/minecraft/item/tooltip/TooltipType;Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemStackMixin from mod fabric-item-api-v1->@Inject::postTooltipsAdvanced(Lnet/minecraft/item/Item$TooltipContext;Lnet/minecraft/component/type/TooltipDisplayComponent;Lnet/minecraft/entity/player/PlayerEntity;Lnet/minecraft/item/tooltip/TooltipType;Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.client.mixins.json:ItemStackMixin from mod fabric-item-api-v1->@Inject::getTooltip(Lnet/minecraft/item/Item$TooltipContext;Lnet/minecraft/entity/player/PlayerEntity;Lnet/minecraft/item/tooltip/TooltipType;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing AnvilScreenHandlerMixin from fabric-item-api-v1.mixins.json into net.minecraft.screen.AnvilScreenHandler
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing HopperBlockEntityMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.entity.HopperBlockEntity
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$hookExtract$1(Lnet/fabricmc/fabric/api/transfer/v1/item/ItemVariant;)Z to md3f1c3d$fabric-transfer-api-v1$lambda$hookExtract$1$0 in fabric-transfer-api-v1.mixins.json:HopperBlockEntityMixin from mod fabric-transfer-api-v1
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$hookInsert$0(Lnet/fabricmc/fabric/api/transfer/v1/item/ItemVariant;)Z to md3f1c3d$fabric-transfer-api-v1$lambda$hookInsert$0$1 in fabric-transfer-api-v1.mixins.json:HopperBlockEntityMixin from mod fabric-transfer-api-v1
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:HopperBlockEntityMixin from mod fabric-transfer-api-v1->@Inject::hookInsert(Lnet/minecraft/world/World;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/HopperBlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:HopperBlockEntityMixin from mod fabric-transfer-api-v1->@Inject::hookExtract(Lnet/minecraft/world/World;Lnet/minecraft/block/entity/Hopper;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing DropperBlockMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.DropperBlock
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$hookDispense$0(Lnet/fabricmc/fabric/api/transfer/v1/item/ItemVariant;)Z to md3f1c3d$fabric-transfer-api-v1$lambda$hookDispense$0$0 in fabric-transfer-api-v1.mixins.json:DropperBlockMixin from mod fabric-transfer-api-v1
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:DropperBlockMixin from mod fabric-transfer-api-v1->@Inject::hookDispense(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/block/BlockState;Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:48] [main/DEBUG] (FabricLoader/Mixin) Mixing BannerBlockEntityMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.block.entity.BannerBlockEntity
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) Mixing world.VillageAnimalSystemMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.AnimalEntity
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onInteractMob$0(Lnet/minecraft/entity/passive/VillagerEntity;)Z to md3f1c3d$ai_villagers$lambda$onInteractMob$0$0 in ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::onWriteCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::onWriteCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::onWriteCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::onReadCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::onReadCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::onReadCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::aiVillagers_onTickMovement(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::aiVillagers_onTickMovement(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::aiVillagers_onTickMovement(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::onInteractMob(Lnet/minecraft/entity/player/PlayerEntity;Lnet/minecraft/util/Hand;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::onInteractMob(Lnet/minecraft/entity/player/PlayerEntity;Lnet/minecraft/util/Hand;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillageAnimalSystemMixin from mod ai_villagers->@Inject::onInteractMob(Lnet/minecraft/entity/player/PlayerEntity;Lnet/minecraft/util/Hand;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) Mixing CrafterBlockMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.CrafterBlock
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:CrafterBlockMixin from mod fabric-transfer-api-v1->@Inject::transferOrSpawnStack(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/CrafterBlockEntity;Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lnet/minecraft/recipe/RecipeEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemAccessor from fabric-item-api-v1.mixins.json into net.minecraft.item.Item
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemMixin from fabric-item-api-v1.mixins.json into net.minecraft.item.Item
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.item.Item
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemMixin from mod fabric-item-api-v1->@Inject::onConstruct(Lnet/minecraft/item/Item$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemMixin from mod fabric-item-api-v1->@Inject::onConstruct(Lnet/minecraft/item/Item$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:49] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemMixin from mod fabric-item-api-v1->@Inject::onConstruct(Lnet/minecraft/item/Item$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:50] [main/DEBUG] (FabricLoader/Mixin) Mixing BucketItemAccessor from fabric-transfer-api-v1.mixins.json into net.minecraft.item.BucketItem
[20:03:50] [main/DEBUG] (FabricLoader/Mixin) Mixing BucketItemMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.item.BucketItem
[20:03:50] [main/DEBUG] (FabricLoader/Mixin) Mixing AxeItemAccessor from fabric-content-registries-v0.mixins.json into net.minecraft.item.AxeItem
[20:03:50] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getStrippedBlocks()Ljava/util/Map; to getStrippedBlocks$fabric-content-registries-v0_$md$3f1c3d$0 in fabric-content-registries-v0.mixins.json:AxeItemAccessor from mod fabric-content-registries-v0
[20:03:50] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method setStrippedBlocks(Ljava/util/Map;)V to setStrippedBlocks$fabric-content-registries-v0_$md$3f1c3d$1 in fabric-content-registries-v0.mixins.json:AxeItemAccessor from mod fabric-content-registries-v0
[20:03:50] [main/DEBUG] (FabricLoader/Mixin) Mixing ShovelItemAccessor from fabric-content-registries-v0.mixins.json into net.minecraft.item.ShovelItem
[20:03:50] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getPathStates()Ljava/util/Map; to getPathStates$fabric-content-registries-v0_$md$3f1c3d$0 in fabric-content-registries-v0.mixins.json:ShovelItemAccessor from mod fabric-content-registries-v0
[20:03:50] [main/DEBUG] (FabricLoader/Mixin) Mixing HoeItemAccessor from fabric-content-registries-v0.mixins.json into net.minecraft.item.HoeItem
[20:03:50] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getTillingActions()Ljava/util/Map; to getTillingActions$fabric-content-registries-v0_$md$3f1c3d$0 in fabric-content-registries-v0.mixins.json:HoeItemAccessor from mod fabric-content-registries-v0
[20:03:50] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemSettingsMixin from fabric-item-api-v1.mixins.json into net.minecraft.item.Item$Settings
[20:03:51] [main/DEBUG] (FabricLoader/Mixin) Mixing TagKeyMixin from fabric-convention-tags-api-v2.mixins.json into net.minecraft.registry.tag.TagKey
[20:03:51] [main/DEBUG] (FabricLoader/Mixin) Mixing BundleContentsComponentAccessor from fabric-transfer-api-v1.mixins.json into net.minecraft.component.type.BundleContentsComponent
[20:03:51] [main/DEBUG] (FabricLoader/Mixin) Renaming @Invoker method getOccupancy(Lnet/minecraft/item/ItemStack;)Lorg/apache/commons/lang3/math/Fraction; to getOccupancy$fabric-transfer-api-v1_$md$3f1c3d$0 in fabric-transfer-api-v1.mixins.json:BundleContentsComponentAccessor from mod fabric-transfer-api-v1
[20:03:51] [main/DEBUG] (FabricLoader/Mixin) Mixing AttachmentTargetsMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.world.chunk.Chunk
[20:03:51] [main/DEBUG] (FabricLoader/Mixin) Mixing ChunkMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.world.chunk.Chunk
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) Mixing WorldChunkMixin from fabric-lifecycle-events-v1.client.mixins.json into net.minecraft.world.chunk.WorldChunk
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) Mixing WorldChunkMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.world.chunk.WorldChunk
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_syncChange$0(Lnet/fabricmc/fabric/api/attachment/v1/AttachmentType;Lnet/fabricmc/fabric/impl/attachment/sync/s2c/AttachmentSyncPayloadS2C;Lnet/minecraft/server/network/ServerPlayerEntity;)V to md3f1c3d$fabric-data-attachment-api-v1$lambda$fabric_syncChange$0$0 in fabric-data-attachment-api-v1.mixins.json:WorldChunkMixin from mod fabric-data-attachment-api-v1
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/block/entity/BlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/block/entity/BlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/block/entity/BlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:WorldChunkMixin from mod fabric-data-attachment-api-v1->@Inject::transferProtoChunkAttachment(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/world/chunk/ProtoChunk;Lnet/minecraft/world/chunk/WorldChunk$EntityLoader;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:WorldChunkMixin from mod fabric-data-attachment-api-v1->@Inject::transferProtoChunkAttachment(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/world/chunk/ProtoChunk;Lnet/minecraft/world/chunk/WorldChunk$EntityLoader;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:WorldChunkMixin from mod fabric-data-attachment-api-v1->@Inject::transferProtoChunkAttachment(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/world/chunk/ProtoChunk;Lnet/minecraft/world/chunk/WorldChunk$EntityLoader;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) Mixing ContainerComponentAccessor from fabric-transfer-api-v1.mixins.json into net.minecraft.component.type.ContainerComponent
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) Mixing LootTableAccessor from fabric-loot-api-v3.mixins.json into net.minecraft.loot.LootTable
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) Mixing LootTableMixin from fabric-loot-api-v3.mixins.json into net.minecraft.loot.LootTable
[20:03:52] [main/DEBUG] (FabricLoader/Mixin) Mixing EnchantRandomlyLootFunctionMixin from fabric-item-api-v1.mixins.json into net.minecraft.loot.function.EnchantRandomlyLootFunction
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing EntityTypeBuilderMixin from fabric-object-builder-v1.mixins.json into net.minecraft.entity.EntityType$Builder
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:EntityTypeBuilderMixin from mod fabric-object-builder-api-v1->@Inject::applyChildBuilders(Lnet/minecraft/registry/RegistryKey;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayerEntityMixin from fabric-screen-handler-api-v1.mixins.json into net.minecraft.server.network.ServerPlayerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayerEntityMixin from fabric-particles-v1.mixins.json into net.minecraft.server.network.ServerPlayerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayerEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.server.network.ServerPlayerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayerEntityMixin from fabric-events-interaction-v0.mixins.json into net.minecraft.server.network.ServerPlayerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-handler-api-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-screen-handler-api-v1->@Inject::fabric_storeOpenedScreenHandler(Lnet/minecraft/screen/NamedScreenHandlerFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-handler-api-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-screen-handler-api-v1->@Inject::fabric_storeOpenedScreenHandler(Lnet/minecraft/screen/NamedScreenHandlerFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-handler-api-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-screen-handler-api-v1->@Inject::fabric_storeOpenedScreenHandler(Lnet/minecraft/screen/NamedScreenHandlerFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::callOnKillForPlayer(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::callOnKillForPlayer(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::callOnKillForPlayer(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::afterWorldChanged(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::afterWorldChanged(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::afterWorldChanged(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::onCopyFrom(Lnet/minecraft/server/network/ServerPlayerEntity;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::onCopyFrom(Lnet/minecraft/server/network/ServerPlayerEntity;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::onCopyFrom(Lnet/minecraft/server/network/ServerPlayerEntity;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.mixins.json:ServerPlayerEntityMixin from mod fabric-events-interaction-v0->@Inject::onPlayerInteractEntity(Lnet/minecraft/entity/Entity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing world.VillagerExperienceAttractionMixin from ai_villagers.mixins.json into net.minecraft.entity.ExperienceOrbEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillagerExperienceAttractionMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onTickEnd$3(Lnet/minecraft/entity/passive/VillagerEntity;)Z to md3f1c3d$ai_villagers$lambda$onTickEnd$3$0 in ai_villagers.mixins.json:world.VillagerExperienceAttractionMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onTickEnd$2(Lnet/minecraft/entity/passive/VillagerEntity;)V to md3f1c3d$ai_villagers$lambda$onTickEnd$2$1 in ai_villagers.mixins.json:world.VillagerExperienceAttractionMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onTickEnd$1(Lnet/minecraft/entity/ExperienceOrbEntity;Lnet/minecraft/entity/passive/VillagerEntity;)D to md3f1c3d$ai_villagers$lambda$onTickEnd$1$2 in ai_villagers.mixins.json:world.VillagerExperienceAttractionMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onTickEnd$0(Lnet/minecraft/entity/passive/VillagerEntity;)Z to md3f1c3d$ai_villagers$lambda$onTickEnd$0$3 in ai_villagers.mixins.json:world.VillagerExperienceAttractionMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillagerExperienceAttractionMixin from mod ai_villagers->@Inject::onTickEnd(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillagerExperienceAttractionMixin from mod ai_villagers->@Inject::onTickEnd(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.VillagerExperienceAttractionMixin from mod ai_villagers->@Inject::onTickEnd(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing BreezeEntityMixin from fabric-particles-v1.mixins.json into net.minecraft.entity.mob.BreezeEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing world.animals.CowVillageStatusInheritanceMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.CowEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.animals.CowVillageStatusInheritanceMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.animals.CowVillageStatusInheritanceMixin from mod ai_villagers->@Inject::aiVillagers$inheritVillageStatus(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/passive/PassiveEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing VillagerEntityAccessor from fabric-content-registries-v0.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method fabric_setItemFoodValues(Ljava/util/Map;)V to fabric_setItemFoodValues$fabric-content-registries-v0_$md$3f1c3d$0 in fabric-content-registries-v0.mixins.json:VillagerEntityAccessor from mod fabric-content-registries-v0
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing VillagerEntityMixin from fabric-content-registries-v0.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing accessors.GlobalAccessor from ai_villagers.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:accessors.GlobalAccessor from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing behavior.VillagerDoorInteractionMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerDoorInteractionMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$checkNearbyDoors$3(LAiVillagers/interfaces/BlockType;)LAiVillagers/interfaces/BlockRecord; to md3f1c3d$ai_villagers$lambda$checkNearbyDoors$3$1 in ai_villagers.mixins.json:behavior.VillagerDoorInteractionMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$checkNearbyGates$2(LAiVillagers/interfaces/BlockType;)LAiVillagers/interfaces/BlockRecord; to md3f1c3d$ai_villagers$lambda$checkNearbyGates$2$2 in ai_villagers.mixins.json:behavior.VillagerDoorInteractionMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$processBlockAt$1(LAiVillagers/interfaces/BlockType;)LAiVillagers/interfaces/BlockRecord; to md3f1c3d$ai_villagers$lambda$processBlockAt$1$3 in ai_villagers.mixins.json:behavior.VillagerDoorInteractionMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$processBlockAt$0(LAiVillagers/interfaces/BlockType;)LAiVillagers/interfaces/BlockRecord; to md3f1c3d$ai_villagers$lambda$processBlockAt$0$4 in ai_villagers.mixins.json:behavior.VillagerDoorInteractionMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing behavior.VillagerGenderSystemMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing behavior.VillagerItemCollectionMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerItemCollectionMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming @Unique field ITEM_FOOD_VALUESLjava/util/Map; to fd3f1c3d$ai_villagers$ITEM_FOOD_VALUES$0 in ai_villagers.mixins.json:behavior.VillagerItemCollectionMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing health.VillagerHealthRegenerationMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:health.VillagerHealthRegenerationMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing professions.butcher.ButcherBehaviorMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onReadCustomData$23(Lnet/minecraft/storage/ReadView$TypedListReadView;)V to md3f1c3d$ai_villagers$lambda$onReadCustomData$23$5 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onReadCustomData$22(Ljava/lang/String;)V to md3f1c3d$ai_villagers$lambda$onReadCustomData$22$6 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onReadCustomData$21(Lnet/minecraft/storage/ReadView$ListReadView;)V to md3f1c3d$ai_villagers$lambda$onReadCustomData$21$7 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onReadCustomData$20(Lnet/minecraft/storage/ReadView;Ljava/lang/String;)V to md3f1c3d$ai_villagers$lambda$onReadCustomData$20$8 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onReadCustomData$19(Lnet/minecraft/storage/ReadView$ListReadView;)V to md3f1c3d$ai_villagers$lambda$onReadCustomData$19$9 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onReadCustomData$18(Lnet/minecraft/storage/ReadView;Ljava/lang/String;)V to md3f1c3d$ai_villagers$lambda$onReadCustomData$18$a in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onReadCustomData$17(Ljava/lang/String;Ljava/lang/Long;)V to md3f1c3d$ai_villagers$lambda$onReadCustomData$17$b in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onReadCustomData$16(Ljava/lang/String;)V to md3f1c3d$ai_villagers$lambda$onReadCustomData$16$c in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onReadCustomData$15(Lnet/minecraft/storage/ReadView;Ljava/lang/Integer;)V to md3f1c3d$ai_villagers$lambda$onReadCustomData$15$d in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onReadCustomData$14(Lnet/minecraft/storage/ReadView;Ljava/lang/Integer;)V to md3f1c3d$ai_villagers$lambda$onReadCustomData$14$e in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onReadCustomData$13(Ljava/lang/String;)V to md3f1c3d$ai_villagers$lambda$onReadCustomData$13$f in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onWriteCustomData$12(Lnet/minecraft/storage/WriteView$ListAppender;Ljava/util/UUID;Ljava/lang/Long;)V to md3f1c3d$ai_villagers$lambda$onWriteCustomData$12$10 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$isChestClaimedByOtherVillager$11(Ljava/util/UUID;Lnet/minecraft/entity/passive/VillagerEntity;)Z to md3f1c3d$ai_villagers$lambda$isChestClaimedByOtherVillager$11$11 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$findNearestChest$10(Lnet/minecraft/world/World;Lnet/minecraft/util/math/BlockPos;)Z to md3f1c3d$ai_villagers$lambda$findNearestChest$10$12 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$processAnimalChasing$9(Lnet/minecraft/entity/passive/AnimalEntity;)Z to md3f1c3d$ai_villagers$lambda$processAnimalChasing$9$13 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$pruneDeadFedAnimals$8(JLjava/util/Map$Entry;)Z to md3f1c3d$ai_villagers$lambda$pruneDeadFedAnimals$8$14 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$pruneDeadFedAnimals$7(Lnet/minecraft/entity/passive/AnimalEntity;)Z to md3f1c3d$ai_villagers$lambda$pruneDeadFedAnimals$7$15 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$refillButcherTargets$6(Lnet/minecraft/entity/passive/AnimalEntity;LAiVillagers/interfaces/ButcherHelper;)Z to md3f1c3d$ai_villagers$lambda$refillButcherTargets$6$16 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$processFeedingInteraction$5(Ljava/lang/Class;)Ljava/util/List; to md3f1c3d$ai_villagers$lambda$processFeedingInteraction$5$17 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$processFeedingInteraction$4(JLnet/minecraft/entity/passive/AnimalEntity;)Z to md3f1c3d$ai_villagers$lambda$processFeedingInteraction$4$18 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$assignStructures$3(Lnet/minecraft/world/gen/structure/Structure;)Z to md3f1c3d$ai_villagers$lambda$assignStructures$3$19 in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onVillagerTick$2(Ljava/util/UUID;Ljava/util/Map$Entry;)Z to md3f1c3d$ai_villagers$lambda$onVillagerTick$2$1a in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onVillagerTick$1(Ljava/util/UUID;Ljava/util/Map$Entry;)Z to md3f1c3d$ai_villagers$lambda$onVillagerTick$1$1b in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onVillagerDeath$0(Ljava/util/UUID;Ljava/util/Map$Entry;)Z to md3f1c3d$ai_villagers$lambda$onVillagerDeath$0$1c in ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Mixing professions.farmer.FarmerBehaviorMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$readFarmerCustomData$9(Lnet/minecraft/storage/ReadView;Ljava/lang/Integer;)V to md3f1c3d$ai_villagers$lambda$readFarmerCustomData$9$1d in ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$readFarmerCustomData$8(Ljava/lang/String;)V to md3f1c3d$ai_villagers$lambda$readFarmerCustomData$8$1e in ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers
[20:03:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$stopAllAnimalAttraction$7(Lnet/minecraft/entity/passive/AnimalEntity;)Z to md3f1c3d$ai_villagers$lambda$stopAllAnimalAttraction$7$1f in ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$handleAnimalAttraction$6(Lnet/minecraft/entity/passive/ChickenEntity;)Z to md3f1c3d$ai_villagers$lambda$handleAnimalAttraction$6$20 in ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$handleAnimalAttraction$5(Lnet/minecraft/entity/passive/AnimalEntity;)Z to md3f1c3d$ai_villagers$lambda$handleAnimalAttraction$5$21 in ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$handleAnimalAttraction$4(Lnet/minecraft/entity/passive/PigEntity;)Z to md3f1c3d$ai_villagers$lambda$handleAnimalAttraction$4$22 in ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$isChestReserved$3(Lnet/minecraft/entity/passive/VillagerEntity;Lnet/minecraft/entity/passive/VillagerEntity;)Z to md3f1c3d$ai_villagers$lambda$isChestReserved$3$23 in ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$findNearestChest$2(Lnet/minecraft/entity/passive/VillagerEntity;Lnet/minecraft/util/math/BlockPos;)Z to md3f1c3d$ai_villagers$lambda$findNearestChest$2$24 in ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$areAnimalsValidForBreeding$1(Lnet/minecraft/entity/passive/AnimalEntity;)Z to md3f1c3d$ai_villagers$lambda$areAnimalsValidForBreeding$1$25 in ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$findBreedingPair$0(Lnet/minecraft/entity/passive/AnimalEntity;)Z to md3f1c3d$ai_villagers$lambda$findBreedingPair$0$26 in ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Mixing professions.fisher.FisherBehaviorMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$cleanupFailedSpotsWithoutWater$2(Lnet/minecraft/world/World;Ljava/util/Map$Entry;)Z to md3f1c3d$ai_villagers$lambda$cleanupFailedSpotsWithoutWater$2$27 in ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$executePerformanceOptimizedCacheCleanup$1(Lnet/minecraft/util/math/BlockPos;)Z to md3f1c3d$ai_villagers$lambda$executePerformanceOptimizedCacheCleanup$1$28 in ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$executePerformanceOptimizedCacheCleanup$0(JLjava/util/Map$Entry;)Z to md3f1c3d$ai_villagers$lambda$executePerformanceOptimizedCacheCleanup$0$29 in ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Mixing professions.fisher.VillagerFishingBobberTrackingMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.VillagerFishingBobberTrackingMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Mixing professions.nitwit.NitwitBehaviorMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.nitwit.NitwitBehaviorMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Mixing protection.VillagerBedProtectionMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.VillagerEntity
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:protection.VillagerBedProtectionMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$isPlayerOccupyingBed$2(Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/util/math/BlockPos;)Ljava/lang/Boolean; to md3f1c3d$ai_villagers$lambda$isPlayerOccupyingBed$2$2a in ai_villagers.mixins.json:protection.VillagerBedProtectionMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$processProtectionContext$1(Lnet/minecraft/entity/passive/VillagerEntity;Lnet/minecraft/entity/player/PlayerEntity;)V to md3f1c3d$ai_villagers$lambda$processProtectionContext$1$2b in ai_villagers.mixins.json:protection.VillagerBedProtectionMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$processProtectionContext$0(Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/entity/player/PlayerEntity;)Z to md3f1c3d$ai_villagers$lambda$processProtectionContext$0$2c in ai_villagers.mixins.json:protection.VillagerBedProtectionMixin from mod ai_villagers
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerDoorInteractionMixin from mod ai_villagers->@Inject::onMobTick(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerDoorInteractionMixin from mod ai_villagers->@Inject::onMobTick(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerDoorInteractionMixin from mod ai_villagers->@Inject::onMobTick(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::initDataTracker(Lnet/minecraft/entity/data/DataTracker$Builder;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::initDataTracker(Lnet/minecraft/entity/data/DataTracker$Builder;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::initDataTracker(Lnet/minecraft/entity/data/DataTracker$Builder;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::initialize(Lnet/minecraft/world/ServerWorldAccess;Lnet/minecraft/world/LocalDifficulty;Lnet/minecraft/entity/SpawnReason;Lnet/minecraft/entity/EntityData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::initialize(Lnet/minecraft/world/ServerWorldAccess;Lnet/minecraft/world/LocalDifficulty;Lnet/minecraft/entity/SpawnReason;Lnet/minecraft/entity/EntityData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::initialize(Lnet/minecraft/world/ServerWorldAccess;Lnet/minecraft/world/LocalDifficulty;Lnet/minecraft/entity/SpawnReason;Lnet/minecraft/entity/EntityData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::writeCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::writeCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::writeCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::readCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::readCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerGenderSystemMixin from mod ai_villagers->@Inject::readCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerItemCollectionMixin from mod ai_villagers->@Inject::onCanGather(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/item/ItemStack;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:behavior.VillagerItemCollectionMixin from mod ai_villagers->@Inject::addAppleFoodValue(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:health.VillagerHealthRegenerationMixin from mod ai_villagers->@Inject::onTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:health.VillagerHealthRegenerationMixin from mod ai_villagers->@Inject::onTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:health.VillagerHealthRegenerationMixin from mod ai_villagers->@Inject::onTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::cachePreviousData(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::cachePreviousData(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::cachePreviousData(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onProfessionChanged(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onProfessionChanged(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onProfessionChanged(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onVillagerDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onVillagerDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onVillagerDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onVillagerTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onVillagerTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onVillagerTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onWriteCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onWriteCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onWriteCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onReadCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onReadCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.butcher.ButcherBehaviorMixin from mod ai_villagers->@Inject::onReadCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onFarmerTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onFarmerTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onFarmerTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onSetVillagerData(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onSetVillagerData(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onSetVillagerData(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onVillagerDeath(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onVillagerDeath(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onVillagerDeath(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onVillagerDataChange(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onVillagerDataChange(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::onVillagerDataChange(Lnet/minecraft/village/VillagerData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::writeFarmerCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::writeFarmerCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::writeFarmerCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::readFarmerCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::readFarmerCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.farmer.FarmerBehaviorMixin from mod ai_villagers->@Inject::readFarmerCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers->@Inject::onPlayerInteractionAttempt(Lnet/minecraft/entity/player/PlayerEntity;Lnet/minecraft/util/Hand;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers->@Inject::processFisherVillagerBehavior(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers->@Inject::processFisherVillagerBehavior(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers->@Inject::processFisherVillagerBehavior(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers->@Inject::onWriteCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers->@Inject::onWriteCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers->@Inject::onWriteCustomData(Lnet/minecraft/storage/WriteView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers->@Inject::onReadCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers->@Inject::onReadCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.fisher.FisherBehaviorMixin from mod ai_villagers->@Inject::onReadCustomData(Lnet/minecraft/storage/ReadView;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.nitwit.NitwitBehaviorMixin from mod ai_villagers->@Inject::tick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.nitwit.NitwitBehaviorMixin from mod ai_villagers->@Inject::tick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:professions.nitwit.NitwitBehaviorMixin from mod ai_villagers->@Inject::tick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:protection.VillagerBedProtectionMixin from mod ai_villagers->@Inject::onTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:protection.VillagerBedProtectionMixin from mod ai_villagers->@Inject::onTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:54] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:protection.VillagerBedProtectionMixin from mod ai_villagers->@Inject::onTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) Mixing world.animals.PigVillageStatusInheritanceMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.PigEntity
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.animals.PigVillageStatusInheritanceMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.animals.PigVillageStatusInheritanceMixin from mod ai_villagers->@Inject::onCreateChild(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/passive/PassiveEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) Mixing world.animals.SheepVillageStatusInheritanceMixin from ai_villagers.mixins.json into net.minecraft.entity.passive.SheepEntity
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.animals.SheepVillageStatusInheritanceMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:world.animals.SheepVillageStatusInheritanceMixin from mod ai_villagers->@Inject::onCreateChild(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/passive/PassiveEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) Mixing WardenEntityMixin from fabric-particles-v1.mixins.json into net.minecraft.entity.mob.WardenEntity
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) Mixing TrackedDataHandlerRegistryAccessor from fabric-object-builder-v1.mixins.json into net.minecraft.entity.data.TrackedDataHandlerRegistry
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method fabric_getDataHandlers()Lnet/minecraft/util/collection/Int2ObjectBiMap; to fabric_getDataHandlers$fabric-object-builder-api-v1_$md$3f1c3d$0 in fabric-object-builder-v1.mixins.json:TrackedDataHandlerRegistryAccessor from mod fabric-object-builder-api-v1
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) Mixing TrackedDataHandlerRegistryMixin from fabric-object-builder-v1.mixins.json into net.minecraft.entity.data.TrackedDataHandlerRegistry
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:TrackedDataHandlerRegistryMixin from mod fabric-object-builder-api-v1->@Inject::storeVanillaHandlers(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:TrackedDataHandlerRegistryMixin from mod fabric-object-builder-api-v1->@Inject::onHeadRegister(Lnet/minecraft/entity/data/TrackedDataHandler;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:55] [main/DEBUG] (net.fabricmc.fabric.impl.object.builder.FabricTrackedDataRegistryImpl) Stored 35 vanilla handlers
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) Mixing HoneycombItemMixin from fabric-content-registries-v0.mixins.json into net.minecraft.item.HoneycombItem
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:HoneycombItemMixin from mod fabric-content-registries-v0->@Inject::createUnwaxedToWaxedMap(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:55] [main/DEBUG] (FabricLoader/Mixin) Mixing BrushItemMixin from fabric-particles-v1.mixins.json into net.minecraft.item.BrushItem
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) Mixing EntitySelectorOptionsAccessor from fabric-command-api-v2.mixins.json into net.minecraft.command.EntitySelectorOptions
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) Renaming @Invoker method callPutOption(Ljava/lang/String;Lnet/minecraft/command/EntitySelectorOptions$SelectorHandler;Ljava/util/function/Predicate;Lnet/minecraft/text/Text;)V to callPutOption$fabric-command-api-v2_$md$3f1c3d$0 in fabric-command-api-v2.mixins.json:EntitySelectorOptionsAccessor from mod fabric-command-api-v2
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) Mixing EntitySelectorReaderMixin from fabric-command-api-v2.mixins.json into net.minecraft.command.EntitySelectorReader
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) Generating mapped inner class net/minecraft/block/entity/ChiseledBookshelfBlockEntity$Anonymous$d2bde9883bdf43c585c5075440734812 (originally net/fabricmc/fabric/mixin/transfer/ChiseledBookshelfBlockEntityMixin$1)
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) Mixing SimpleInventoryMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.inventory.SimpleInventory
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) Mixing CraftingRecipeMixin from fabric-item-api-v1.mixins.json into net.minecraft.recipe.CraftingRecipe
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) Mixing ingredient.IngredientMixin from fabric-recipe-api-v1.mixins.json into net.minecraft.recipe.Ingredient
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$injectCodec$2(Lnet/minecraft/recipe/Ingredient;)Lcom/mojang/datafixers/util/Either; to md3f1c3d$fabric-recipe-api-v1$lambda$injectCodec$2$0 in fabric-recipe-api-v1.mixins.json:ingredient.IngredientMixin from mod fabric-recipe-api-v1
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$injectCodec$1(Lcom/mojang/datafixers/util/Either;)Lnet/minecraft/recipe/Ingredient; to md3f1c3d$fabric-recipe-api-v1$lambda$injectCodec$1$1 in fabric-recipe-api-v1.mixins.json:ingredient.IngredientMixin from mod fabric-recipe-api-v1
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$injectCodec$0(Lnet/minecraft/recipe/Ingredient;)Lnet/minecraft/recipe/Ingredient; to md3f1c3d$fabric-recipe-api-v1$lambda$injectCodec$0$2 in fabric-recipe-api-v1.mixins.json:ingredient.IngredientMixin from mod fabric-recipe-api-v1
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.IngredientMixin from mod fabric-recipe-api-v1->@Inject::injectCodec(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.IngredientMixin from mod fabric-recipe-api-v1->@Inject::onGetEntries(Lnet/minecraft/recipe/Ingredient;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.IngredientMixin from mod fabric-recipe-api-v1->@Inject::onGetEntries(Lnet/minecraft/recipe/Ingredient;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.IngredientMixin from mod fabric-recipe-api-v1->@Inject::onGetEntries(Lnet/minecraft/recipe/Ingredient;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.IngredientMixin from mod fabric-recipe-api-v1->@Inject::onHeadEquals(Ljava/lang/Object;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) Mixing ingredient.ShapelessRecipeMixin from fabric-recipe-api-v1.mixins.json into net.minecraft.recipe.ShapelessRecipe
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.ShapelessRecipeMixin from mod fabric-recipe-api-v1->@Inject::cacheRequiresTesting(Ljava/lang/String;Lnet/minecraft/recipe/book/CraftingRecipeCategory;Lnet/minecraft/item/ItemStack;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.ShapelessRecipeMixin from mod fabric-recipe-api-v1->@Inject::cacheRequiresTesting(Ljava/lang/String;Lnet/minecraft/recipe/book/CraftingRecipeCategory;Lnet/minecraft/item/ItemStack;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.ShapelessRecipeMixin from mod fabric-recipe-api-v1->@Inject::cacheRequiresTesting(Ljava/lang/String;Lnet/minecraft/recipe/book/CraftingRecipeCategory;Lnet/minecraft/item/ItemStack;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:56] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.ShapelessRecipeMixin from mod fabric-recipe-api-v1->@Inject::customIngredientMatch(Lnet/minecraft/recipe/input/CraftingRecipeInput;Lnet/minecraft/world/World;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Mixing ChunkGeneratingMixin from fabric-lifecycle-events-v1.mixins.json into net.minecraft.world.chunk.ChunkGenerating
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ChunkGeneratingMixin from mod fabric-lifecycle-events-v1->@Inject::onChunkLoad(Lnet/minecraft/world/chunk/Chunk;Lnet/minecraft/world/chunk/ChunkGenerationContext;Lnet/minecraft/world/chunk/AbstractChunkHolder;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Mixing StructureTemplateManagerMixin from fabric-gametest-api-v1.mixins.json into net.minecraft.structure.StructureTemplateManager
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Renaming @Unique method streamTemplatesFromResource()Ljava/util/stream/Stream; to md3f1c3d$fabric-gametest-api-v1$streamTemplatesFromResource$0 in fabric-gametest-api-v1.mixins.json:StructureTemplateManagerMixin from mod fabric-gametest-api-v1
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:StructureTemplateManagerMixin from mod fabric-gametest-api-v1->@Inject::addFabricTemplateProvider(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/world/level/storage/LevelStorage$Session;Lcom/mojang/datafixers/DataFixer;Lnet/minecraft/registry/RegistryEntryLookup;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:StructureTemplateManagerMixin from mod fabric-gametest-api-v1->@Inject::addFabricTemplateProvider(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/world/level/storage/LevelStorage$Session;Lcom/mojang/datafixers/DataFixer;Lnet/minecraft/registry/RegistryEntryLookup;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:StructureTemplateManagerMixin from mod fabric-gametest-api-v1->@Inject::addFabricTemplateProvider(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/world/level/storage/LevelStorage$Session;Lcom/mojang/datafixers/DataFixer;Lnet/minecraft/registry/RegistryEntryLookup;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Mixing accessors.ButcherAccessor from ai_villagers.mixins.json into net.minecraft.structure.PoolStructurePiece
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) ai_villagers.mixins.json:accessors.ButcherAccessor from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Mixing BiomeSourceMixin from fabric-biome-api-v1.mixins.json into net.minecraft.world.biome.source.BiomeSource
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Mixing MultiNoiseBiomeSourceMixin from fabric-biome-api-v1.mixins.json into net.minecraft.world.biome.source.MultiNoiseBiomeSource
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Mixing NetherBiomePresetMixin from fabric-biome-api-v1.mixins.json into net.minecraft.world.biome.source.MultiNoiseBiomeSourceParameterList$Preset$1
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:NetherBiomePresetMixin from mod fabric-biome-api-v1->@Inject::apply(Ljava/util/function/Function;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Mixing RegistryOpsAccessor from fabric-resource-conditions-api-v1.mixins.json into net.minecraft.registry.RegistryOps
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Mixing TheEndBiomeSourceMixin from fabric-biome-api-v1.mixins.json into net.minecraft.world.biome.source.TheEndBiomeSource
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$init$1(Lnet/minecraft/registry/RegistryEntryLookup;)Lnet/fabricmc/fabric/impl/biome/TheEndBiomeData$Overrides; to md3f1c3d$fabric-biome-api-v1$lambda$init$1$0 in fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$modifyCodec$0(Lcom/mojang/serialization/codecs/RecordCodecBuilder$Instance;)Lcom/mojang/datafixers/kinds/App; to md3f1c3d$fabric-biome-api-v1$lambda$modifyCodec$0$1 in fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::modifyCodec(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::rememberLookup(Lnet/minecraft/registry/RegistryEntryLookup;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::clearLookup(Lnet/minecraft/registry/RegistryEntryLookup;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::init(Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::init(Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::init(Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::getWeightedEndBiome(IIILnet/minecraft/world/biome/source/util/MultiNoiseUtil$MultiNoiseSampler;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::getWeightedEndBiome(IIILnet/minecraft/world/biome/source/util/MultiNoiseUtil$MultiNoiseSampler;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::getWeightedEndBiome(IIILnet/minecraft/world/biome/source/util/MultiNoiseUtil$MultiNoiseSampler;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::getWeightedEndBiome(IIILnet/minecraft/world/biome/source/util/MultiNoiseUtil$MultiNoiseSampler;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::getWeightedEndBiome(IIILnet/minecraft/world/biome/source/util/MultiNoiseUtil$MultiNoiseSampler;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Mixing DebugChunkGeneratorAccessor from fabric-registry-sync-v0.mixins.json into net.minecraft.world.gen.chunk.DebugChunkGenerator
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method setBLOCK_STATES(Ljava/util/List;)V to setBLOCK_STATES$fabric-registry-sync-v0_$md$3f1c3d$0 in fabric-registry-sync-v0.mixins.json:DebugChunkGeneratorAccessor from mod fabric-registry-sync-v0
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method setX_SIDE_LENGTH(I)V to setX_SIDE_LENGTH$fabric-registry-sync-v0_$md$3f1c3d$1 in fabric-registry-sync-v0.mixins.json:DebugChunkGeneratorAccessor from mod fabric-registry-sync-v0
[20:03:57] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method setZ_SIDE_LENGTH(I)V to setZ_SIDE_LENGTH$fabric-registry-sync-v0_$md$3f1c3d$2 in fabric-registry-sync-v0.mixins.json:DebugChunkGeneratorAccessor from mod fabric-registry-sync-v0
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing GameRulesBooleanRuleAccessor from fabric-game-rule-api-v1.mixins.json into net.minecraft.world.GameRules$BooleanRule
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming @Invoker method invokeCreate(ZLjava/util/function/BiConsumer;)Lnet/minecraft/world/GameRules$Type; to invokeCreate$fabric-game-rule-api-v1_$md$3f1c3d$0 in fabric-game-rule-api-v1.mixins.json:GameRulesBooleanRuleAccessor from mod fabric-game-rule-api-v1
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing GameRulesAccessor from fabric-game-rule-api-v1.mixins.json into net.minecraft.world.GameRules
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming @Invoker method callRegister(Ljava/lang/String;Lnet/minecraft/world/GameRules$Category;Lnet/minecraft/world/GameRules$Type;)Lnet/minecraft/world/GameRules$Key; to callRegister$fabric-game-rule-api-v1_$md$3f1c3d$0 in fabric-game-rule-api-v1.mixins.json:GameRulesAccessor from mod fabric-game-rule-api-v1
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getRuleTypes()Ljava/util/Map; to getRuleTypes$fabric-game-rule-api-v1_$md$3f1c3d$1 in fabric-game-rule-api-v1.mixins.json:GameRulesAccessor from mod fabric-game-rule-api-v1
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing GameRulesKeyMixin from fabric-game-rule-api-v1.mixins.json into net.minecraft.world.GameRules$Key
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing modification.MinecraftServerMixin from fabric-biome-api-v1.mixins.json into net.minecraft.server.MinecraftServer
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing MinecraftServerMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.server.MinecraftServer
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$init$0(Lnet/minecraft/resource/ResourcePack;)Ljava/util/stream/Stream; to md3f1c3d$fabric-resource-loader-v0$lambda$init$0$0 in fabric-resource-loader-v0.mixins.json:MinecraftServerMixin from mod fabric-resource-loader-v0
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing MinecraftServerMixin from fabric-message-api-v1.mixins.json into net.minecraft.server.MinecraftServer
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onGetChatDecorator$0(Lnet/minecraft/server/network/ServerPlayerEntity;Lnet/minecraft/text/Text;)Lnet/minecraft/text/Text; to md3f1c3d$fabric-message-api-v1$lambda$onGetChatDecorator$0$1 in fabric-message-api-v1.mixins.json:MinecraftServerMixin from mod fabric-message-api-v1
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing MinecraftServerMixin from fabric-lifecycle-events-v1.mixins.json into net.minecraft.server.MinecraftServer
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$endResourceReload$0(Ljava/lang/Void;Ljava/lang/Throwable;)Ljava/lang/Void; to md3f1c3d$fabric-lifecycle-events-v1$lambda$endResourceReload$0$2 in fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:modification.MinecraftServerMixin from mod fabric-biome-api-v1->@Inject::finalizeWorldGen(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:modification.MinecraftServerMixin from mod fabric-biome-api-v1->@Inject::finalizeWorldGen(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:modification.MinecraftServerMixin from mod fabric-biome-api-v1->@Inject::finalizeWorldGen(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:MinecraftServerMixin from mod fabric-resource-loader-v0->@Inject::init(Ljava/lang/Thread;Lnet/minecraft/world/level/storage/LevelStorage$Session;Lnet/minecraft/resource/ResourcePackManager;Lnet/minecraft/server/SaveLoader;Ljava/net/Proxy;Lcom/mojang/datafixers/DataFixer;Lnet/minecraft/util/ApiServices;Lnet/minecraft/server/WorldGenerationProgressListenerFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:MinecraftServerMixin from mod fabric-resource-loader-v0->@Inject::init(Ljava/lang/Thread;Lnet/minecraft/world/level/storage/LevelStorage$Session;Lnet/minecraft/resource/ResourcePackManager;Lnet/minecraft/server/SaveLoader;Ljava/net/Proxy;Lcom/mojang/datafixers/DataFixer;Lnet/minecraft/util/ApiServices;Lnet/minecraft/server/WorldGenerationProgressListenerFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:MinecraftServerMixin from mod fabric-resource-loader-v0->@Inject::init(Ljava/lang/Thread;Lnet/minecraft/world/level/storage/LevelStorage$Session;Lnet/minecraft/resource/ResourcePackManager;Lnet/minecraft/server/SaveLoader;Ljava/net/Proxy;Lcom/mojang/datafixers/DataFixer;Lnet/minecraft/util/ApiServices;Lnet/minecraft/server/WorldGenerationProgressListenerFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.mixins.json:MinecraftServerMixin from mod fabric-message-api-v1->@Inject::onGetChatDecorator(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onUnloadWorldAtShutdown(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onUnloadWorldAtShutdown(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onUnloadWorldAtShutdown(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::startResourceReload(Ljava/util/Collection;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::startResourceReload(Ljava/util/Collection;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::startResourceReload(Ljava/util/Collection;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::endResourceReload(Ljava/util/Collection;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::startSave(ZZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::startSave(ZZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::startSave(ZZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::endSave(ZZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::endSave(ZZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::endSave(ZZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing client.item.BasicItemModelUnbakedMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.item.model.BasicItemModel$Unbaked
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing client.item.BasicItemModelMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.item.model.BasicItemModel
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_setMesh$0(Lnet/fabricmc/fabric/api/renderer/v1/model/SpriteFinder;Lnet/fabricmc/fabric/api/renderer/v1/mesh/QuadView;)V to md3f1c3d$fabric-renderer-api-v1$lambda$fabric_setMesh$0$0 in fabric-renderer-api-v1.mixins.json:client.item.BasicItemModelMixin from mod fabric-renderer-api-v1
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.item.BasicItemModelMixin from mod fabric-renderer-api-v1->@Inject::onReturnUpdate(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.item.BasicItemModelMixin from mod fabric-renderer-api-v1->@Inject::onReturnUpdate(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.item.BasicItemModelMixin from mod fabric-renderer-api-v1->@Inject::onReturnUpdate(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing SpecialModelTypesMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.render.item.model.special.SpecialModelTypes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:SpecialModelTypesMixin from mod fabric-rendering-v1->@Inject::onReturnClinit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing TexturedRenderLayersMixin from fabric-object-builder-v1.client.mixins.json into net.minecraft.client.render.TexturedRenderLayers
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.client.mixins.json:TexturedRenderLayersMixin from mod fabric-object-builder-api-v1->@Inject::onReturnClinit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing client.sprite.SpriteAtlasTextureMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.texture.SpriteAtlasTexture
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.sprite.SpriteAtlasTextureMixin from mod fabric-renderer-api-v1->@Inject::uploadHook(Lnet/minecraft/client/texture/SpriteLoader$StitchResult;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.sprite.SpriteAtlasTextureMixin from mod fabric-renderer-api-v1->@Inject::uploadHook(Lnet/minecraft/client/texture/SpriteLoader$StitchResult;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.sprite.SpriteAtlasTextureMixin from mod fabric-renderer-api-v1->@Inject::uploadHook(Lnet/minecraft/client/texture/SpriteLoader$StitchResult;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.render.item.ItemRenderer
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemRendererAccessor from fabric-renderer-indigo.mixins.json into net.minecraft.client.render.item.ItemRenderer
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming @Invoker method fabric_getDynamicDisplayGlintConsumer(Lnet/minecraft/client/render/VertexConsumerProvider;Lnet/minecraft/client/render/RenderLayer;Lnet/minecraft/client/util/math/MatrixStack$Entry;)Lnet/minecraft/client/render/VertexConsumer; to fabric_getDynamicDisplayGlintConsumer$fabric-renderer-indigo_$md$3f1c3d$0 in fabric-renderer-indigo.mixins.json:ItemRendererAccessor from mod fabric-renderer-indigo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing KeyBindingAccessor from fabric-key-binding-api-v1.mixins.json into net.minecraft.client.option.KeyBinding
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method fabric_getCategoryMap()Ljava/util/Map; to fabric_getCategoryMap$fabric-key-binding-api-v1_$md$3f1c3d$0 in fabric-key-binding-api-v1.mixins.json:KeyBindingAccessor from mod fabric-key-binding-api-v1
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing KeyBindingAccessor from fabric-events-interaction-v0.client.mixins.json into net.minecraft.client.option.KeyBinding
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing AtlasSourceManagerAccessor from fabric-rendering-v1.mixins.json into net.minecraft.client.texture.atlas.AtlasSourceManager
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getAtlasSourceCodecs()Lnet/minecraft/util/dynamic/Codecs$IdMapper; to getAtlasSourceCodecs$fabric-rendering-v1_$md$3f1c3d$0 in fabric-rendering-v1.mixins.json:AtlasSourceManagerAccessor from mod fabric-rendering-v1
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing ScreenAccessor from fabric-screen-api-v1.mixins.json into net.minecraft.client.gui.screen.Screen
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing ScreenMixin from fabric-screen-api-v1.mixins.json into net.minecraft.client.gui.screen.Screen
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing ClientWorldMixin from fabric-lifecycle-events-v1.client.mixins.json into net.minecraft.client.world.ClientWorld
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing ClientWorldMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.world.ClientWorld
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$new$1(Lnet/minecraft/world/biome/ColorResolver;)Lnet/minecraft/client/world/BiomeColorCache; to md3f1c3d$fabric-rendering-v1$lambda$new$1$0 in fabric-rendering-v1.mixins.json:ClientWorldMixin from mod fabric-rendering-v1
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$new$0(Lnet/minecraft/world/biome/ColorResolver;Lnet/minecraft/util/math/BlockPos;)I to md3f1c3d$fabric-rendering-v1$lambda$new$0$1 in fabric-rendering-v1.mixins.json:ClientWorldMixin from mod fabric-rendering-v1
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::tickWorldAfterBlockEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::tickWorldAfterBlockEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::tickWorldAfterBlockEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:ClientWorldMixin from mod fabric-rendering-v1->@Inject::onResetChunkColor(Lnet/minecraft/util/math/ChunkPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:ClientWorldMixin from mod fabric-rendering-v1->@Inject::onResetChunkColor(Lnet/minecraft/util/math/ChunkPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:ClientWorldMixin from mod fabric-rendering-v1->@Inject::onResetChunkColor(Lnet/minecraft/util/math/ChunkPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:ClientWorldMixin from mod fabric-rendering-v1->@Inject::onReloadColor(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:ClientWorldMixin from mod fabric-rendering-v1->@Inject::onReloadColor(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:ClientWorldMixin from mod fabric-rendering-v1->@Inject::onReloadColor(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing DefaultAttributeRegistryAccessor from fabric-object-builder-v1.mixins.json into net.minecraft.entity.attribute.DefaultAttributeRegistry
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getRegistry()Ljava/util/Map; to getRegistry$fabric-object-builder-api-v1_$md$3f1c3d$0 in fabric-object-builder-v1.mixins.json:DefaultAttributeRegistryAccessor from mod fabric-object-builder-api-v1
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) Mixing DefaultAttributeRegistryMixin from fabric-object-builder-v1.mixins.json into net.minecraft.entity.attribute.DefaultAttributeRegistry
[20:03:58] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:DefaultAttributeRegistryMixin from mod fabric-object-builder-api-v1->@Inject::injectAttributes(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftClientMixin from fabric-screen-api-v1.mixins.json into net.minecraft.client.MinecraftClient
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming @Unique field LOGGERLorg/slf4j/Logger; to fd3f1c3d$fabric-screen-api-v1$LOGGER$0 in fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftClientMixin from fabric-lifecycle-events-v1.client.mixins.json into net.minecraft.client.MinecraftClient
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftClientMixin from fabric-data-generation-api-v1.client.mixins.json into net.minecraft.client.MinecraftClient
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftClientMixin from fabric-registry-sync-v0.client.mixins.json into net.minecraft.client.MinecraftClient
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftClientMixin from fabric-events-interaction-v0.client.mixins.json into net.minecraft.client.MinecraftClient
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing accessor.MinecraftClientAccessor from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.MinecraftClient
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::checkThreadOnDev(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::checkThreadOnDev(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::checkThreadOnDev(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemove(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemove(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemove(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemoveBecauseStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemoveBecauseStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemoveBecauseStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::afterClientWorldChange(Lnet/minecraft/client/world/ClientWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::afterClientWorldChange(Lnet/minecraft/client/world/ClientWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::afterClientWorldChange(Lnet/minecraft/client/world/ClientWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-data-generation-api-v1.client.mixins.json:MinecraftClientMixin from mod fabric-data-generation-api-v1->@Inject::main(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-data-generation-api-v1.client.mixins.json:MinecraftClientMixin from mod fabric-data-generation-api-v1->@Inject::main(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-data-generation-api-v1.client.mixins.json:MinecraftClientMixin from mod fabric-data-generation-api-v1->@Inject::main(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::disconnectAfter(Lnet/minecraft/client/gui/screen/Screen;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::disconnectAfter(Lnet/minecraft/client/gui/screen/Screen;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::disconnectAfter(Lnet/minecraft/client/gui/screen/Screen;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::afterModInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::afterModInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::afterModInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectUseEntityCallback(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectHandleInputEventsForPreAttackCallback(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectHandleInputEventsForPreAttackCallback(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectHandleInputEventsForPreAttackCallback(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectDoAttackForCancelling(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectHandleBlockBreakingForCancelling(ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing HandledScreenMixin from fabric-screen-api-v1.mixins.json into net.minecraft.client.gui.screen.ingame.HandledScreen
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:HandledScreenMixin from mod fabric-screen-api-v1->@Inject::callSuperMouseReleased(DDILorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:HandledScreenMixin from mod fabric-screen-api-v1->@Inject::callSuperMouseReleased(DDIDDLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing VanillaResourcePackProviderMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.resource.VanillaResourcePackProvider
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:VanillaResourcePackProviderMixin from mod fabric-resource-loader-v0->@Inject::addBuiltinResourcePacks(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:VanillaResourcePackProviderMixin from mod fabric-resource-loader-v0->@Inject::addBuiltinResourcePacks(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:VanillaResourcePackProviderMixin from mod fabric-resource-loader-v0->@Inject::addBuiltinResourcePacks(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ResourcePackManagerMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.ResourcePackManager
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$handleAutoDisable$0(Ljava/util/Set;Lnet/minecraft/resource/ResourcePackProfile;)Z to md3f1c3d$fabric-resource-loader-v0$lambda$handleAutoDisable$0$0 in fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::construct([Lnet/minecraft/resource/ResourcePackProvider;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::construct([Lnet/minecraft/resource/ResourcePackProvider;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::construct([Lnet/minecraft/resource/ResourcePackProvider;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::handleAutoEnableDisable(Ljava/util/Collection;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::handleAutoEnableDisable(Ljava/util/Collection;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::handleAutoEnableDisable(Ljava/util/Collection;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::handleAutoEnable(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::handleAutoEnable(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::handleAutoEnable(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::handleAutoDisable(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::handleAutoDisable(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::handleAutoDisable(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:03:59] [Render thread/INFO] (com.mojang.authlib.yggdrasil.YggdrasilAuthenticationService) Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[20:03:59] [Yggdrasil Key Fetcher/DEBUG] (com.mojang.authlib.minecraft.client.MinecraftClient) Connecting to https://api.minecraftservices.com/publickeys
[20:03:59] [Download-1/DEBUG] (com.mojang.authlib.minecraft.client.MinecraftClient) Connecting to https://sessionserver.mojang.com/session/minecraft/profile/3fff7d8624d633b18ce19423a13c7c89?unsigned=false
[20:03:59] [Render thread/INFO] (net.minecraft.client.MinecraftClient) Setting user: Player517
[20:03:59] [Render thread/DEBUG] (FabricLoader/Entrypoint) Iterating over entrypoint 'main'
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ServerConfigurationNetworkHandlerMixin from fabric-networking-api-v1.mixins.json into net.minecraft.server.network.ServerConfigurationNetworkHandler
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$bindChannelInfo$0(Lnet/minecraft/network/RegistryByteBuf;)Lnet/minecraft/network/RegistryByteBuf; to md3f1c3d$fabric-networking-api-v1$lambda$bindChannelInfo$0$0 in fabric-networking-api-v1.mixins.json:ServerConfigurationNetworkHandlerMixin from mod fabric-networking-api-v1
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ServerConfigurationNetworkHandlerMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.server.network.ServerConfigurationNetworkHandler
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerConfigurationNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerConfigurationNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerConfigurationNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerConfigurationNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::onClientReady(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ServerCommonNetworkHandlerMixin from fabric-networking-api-v1.mixins.json into net.minecraft.server.network.ServerCommonNetworkHandler
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing accessor.ServerCommonNetworkHandlerAccessor from fabric-networking-api-v1.mixins.json into net.minecraft.server.network.ServerCommonNetworkHandler
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerCommonNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleCustomPayloadReceivedAsync(Lnet/minecraft/network/packet/c2s/common/CustomPayloadC2SPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerCommonNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::onPlayPong(Lnet/minecraft/network/packet/c2s/common/CommonPongC2SPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerCommonNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::onPlayPong(Lnet/minecraft/network/packet/c2s/common/CommonPongC2SPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerCommonNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::onPlayPong(Lnet/minecraft/network/packet/c2s/common/CommonPongC2SPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ResourceFinderAccessor from fabric-loot-api-v3.mixins.json into net.minecraft.resource.ResourceFinder
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ResourceFinderAccessor from fabric-resource-conditions-api-v1.mixins.json into net.minecraft.resource.ResourceFinder
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Unexpected: Registered method getDirectoryName()Ljava/lang/String; in net.minecraft.resource.ResourceFinder was not merged
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing LootTableBuilderMixin from fabric-loot-api-v3.mixins.json into net.minecraft.loot.LootTable$Builder
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing LootTableBuilderMixin from fabric-loot-api-v2.mixins.json into net.minecraft.loot.LootTable$Builder
[20:03:59] [Render thread/INFO] (ai_villagers) Inicializando ai_villagers!
[20:03:59] [Render thread/INFO] (ai_villagers) Items personalizados de AiVillagers registrados
[20:03:59] [Render thread/INFO] (ai_villagers) Entidades personalizadas de AiVillagers registradas
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing CustomPayloadC2SPacketMixin from fabric-networking-api-v1.mixins.json into net.minecraft.network.packet.c2s.common.CustomPayloadC2SPacket
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapCodec$0(Lnet/minecraft/network/PacketByteBuf;Lnet/minecraft/util/Identifier;)Lnet/minecraft/network/packet/CustomPayload$Type; to md3f1c3d$fabric-networking-api-v1$lambda$wrapCodec$0$0 in fabric-networking-api-v1.mixins.json:CustomPayloadC2SPacketMixin from mod fabric-networking-api-v1
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing CustomPayloadC2SPacketAccessor from fabric-data-attachment-api-v1.mixins.json into net.minecraft.network.packet.c2s.common.CustomPayloadC2SPacket
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing CustomPayloadPacketCodecMixin from fabric-networking-api-v1.mixins.json into net.minecraft.network.packet.CustomPayload$1
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayNetworkHandlerMixin from fabric-networking-api-v1.mixins.json into net.minecraft.server.network.ServerPlayNetworkHandler
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayNetworkHandlerMixin from fabric-events-interaction-v0.mixins.json into net.minecraft.server.network.ServerPlayNetworkHandler
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:03:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleCustomPayloadReceivedAsync(Lnet/minecraft/network/packet/c2s/common/CustomPayloadC2SPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:03:59] [Render thread/DEBUG] (FabricLoader/Entrypoint) Iterating over entrypoint 'client'
[20:04:00] [Render thread/INFO] (net.fabricmc.fabric.impl.client.indigo.Indigo) [Indigo] Registering Indigo renderer!
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockStateModelUnbakedMixin from fabric-model-loading-api-v1.mixins.json into net.minecraft.client.render.model.BlockStateModel$Unbaked
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.block.model.BlockStateModelMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.model.BlockStateModel
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.block.model.BlockModelPartMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.model.BlockModelPart
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.block.model.SimpleBlockStateModelMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.model.SimpleBlockStateModel
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientConfigurationNetworkHandlerMixin from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.network.ClientConfigurationNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientConfigurationNetworkHandlerMixin from fabric-lifecycle-events-v1.client.mixins.json into net.minecraft.client.network.ClientConfigurationNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing accessor.ClientConfigurationNetworkHandlerAccessor from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.network.ClientConfigurationNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientConfigurationNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientConfigurationNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientConfigurationNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientConfigurationNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleComplete(Lnet/minecraft/network/packet/s2c/config/ReadyS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientConfigurationNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleComplete(Lnet/minecraft/network/packet/s2c/config/ReadyS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientConfigurationNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleComplete(Lnet/minecraft/network/packet/s2c/config/ReadyS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientConfigurationNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::invokeTagsLoaded(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing accessor.ClientCommonNetworkHandlerAccessor from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.network.ClientCommonNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientCommonNetworkHandlerMixin from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.network.ClientCommonNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientCommonNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::onCustomPayload(Lnet/minecraft/network/packet/s2c/common/CustomPayloadS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientPlayNetworkHandlerMixin from fabric-message-api-v1.client.mixins.json into net.minecraft.client.network.ClientPlayNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientPlayNetworkHandlerMixin from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.network.ClientPlayNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientPlayNetworkHandlerMixin from fabric-lifecycle-events-v1.client.mixins.json into net.minecraft.client.network.ClientPlayNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientPlayNetworkHandlerMixin from fabric-command-api-v2.client.mixins.json into net.minecraft.client.network.ClientPlayNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientPlayNetworkHandlerMixin from fabric-data-attachment-api-v1.client.mixins.json into net.minecraft.client.network.ClientPlayNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_allowSendChatMessage(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_allowSendCommandMessage(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleServerPlayReady(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleServerPlayReady(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleServerPlayReady(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onPlayerRespawn(Lnet/minecraft/network/packet/s2c/play/PlayerRespawnS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onPlayerRespawn(Lnet/minecraft/network/packet/s2c/play/PlayerRespawnS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onPlayerRespawn(Lnet/minecraft/network/packet/s2c/play/PlayerRespawnS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onClearWorld(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onClearWorld(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onClearWorld(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::invokeTagsLoaded(Lnet/minecraft/network/packet/s2c/common/SynchronizeTagsS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::invokeTagsLoaded(Lnet/minecraft/network/packet/s2c/common/SynchronizeTagsS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::invokeTagsLoaded(Lnet/minecraft/network/packet/s2c/common/SynchronizeTagsS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onOnCommandTree(Lnet/minecraft/network/packet/s2c/play/CommandTreeS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onOnCommandTree(Lnet/minecraft/network/packet/s2c/play/CommandTreeS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onOnCommandTree(Lnet/minecraft/network/packet/s2c/play/CommandTreeS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onSendCommand(Ljava/lang/String;Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onSendCommand(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing accessor.ClientLoginNetworkHandlerAccessor from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.network.ClientLoginNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientLoginNetworkHandlerMixin from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.network.ClientLoginNetworkHandler
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleQueryRequest(Lnet/minecraft/network/packet/s2c/login/LoginQueryRequestS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ingredient.ClientConnectionMixin from fabric-recipe-api-v1.mixins.json into net.minecraft.network.ClientConnection
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientConnectionMixin from fabric-networking-api-v1.mixins.json into net.minecraft.network.ClientConnection
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_getPendingChannelsNames$0(Lnet/minecraft/network/NetworkPhase;)Ljava/util/Collection; to md3f1c3d$fabric-networking-api-v1$lambda$fabric_getPendingChannelsNames$0$0 in fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientConnectionMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.network.ClientConnection
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::initAddedFields(Lnet/minecraft/network/NetworkSide;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::initAddedFields(Lnet/minecraft/network/NetworkSide;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::initAddedFields(Lnet/minecraft/network/NetworkSide;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lio/netty/channel/ChannelFutureListener;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lio/netty/channel/ChannelFutureListener;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lio/netty/channel/ChannelFutureListener;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lio/netty/channel/ChannelFutureListener;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lio/netty/channel/ChannelFutureListener;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lio/netty/channel/ChannelFutureListener;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::unwatchAddon(Lnet/minecraft/network/state/NetworkState;Lnet/minecraft/network/listener/PacketListener;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::unwatchAddon(Lnet/minecraft/network/state/NetworkState;Lnet/minecraft/network/listener/PacketListener;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::unwatchAddon(Lnet/minecraft/network/state/NetworkState;Lnet/minecraft/network/listener/PacketListener;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::disconnectAddon(Lio/netty/channel/ChannelHandlerContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::disconnectAddon(Lio/netty/channel/ChannelHandlerContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::disconnectAddon(Lio/netty/channel/ChannelHandlerContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::disconnectAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::disconnectAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::disconnectAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/INFO] (ai_villagers) Inicializando cliente de AiVillagers
[20:04:00] [Render thread/INFO] (ai_villagers) Cliente de AiVillagers inicializado correctamente
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing SimpleRegistryTagLookup2Accessor from fabric-tag-api-v1.mixins.json into net.minecraft.registry.SimpleRegistry$TagLookup$2
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing GameOptionsMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.option.GameOptions
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing GameOptionsMixin from fabric-key-binding-api-v1.mixins.json into net.minecraft.client.option.GameOptions
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-key-binding-api-v1.mixins.json:GameOptionsMixin from mod fabric-key-binding-api-v1->@Inject::loadHook(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-key-binding-api-v1.mixins.json:GameOptionsMixin from mod fabric-key-binding-api-v1->@Inject::loadHook(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-key-binding-api-v1.mixins.json:GameOptionsMixin from mod fabric-key-binding-api-v1->@Inject::loadHook(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing SoundInstanceMixin from fabric-sound-api-v1.mixins.json into net.minecraft.client.sound.SoundInstance
[20:04:00] [Download-1/DEBUG] (com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService) Couldn't fetch profile properties for 3fff7d86-24d6-33b1-8ce1-9423a13c7c89 as the profile does not exist
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ResourcePackProfileMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.ResourcePackProfile
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$static$0(Ljava/util/Set;)Z to md3f1c3d$fabric-resource-loader-v0$lambda$static$0$0 in fabric-resource-loader-v0.mixins.json:ResourcePackProfileMixin from mod fabric-resource-loader-v0
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ResourcePackProfileMixin from fabric-resource-conditions-api-v1.mixins.json into net.minecraft.resource.ResourcePackProfile
[20:04:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackProfileMixin from mod fabric-resource-loader-v0->@Inject::onCreateResourcePack(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:01] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing GameOptionsWriteVisitorMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.option.GameOptions$3
[20:04:01] [Render thread/INFO] (net.minecraft.client.MinecraftClient) Backend library: LWJGL version 3.3.3-snapshot
[20:04:01] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MouseMixin from fabric-screen-api-v1.mixins.json into net.minecraft.client.Mouse
[20:04:01] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyboardMixin from fabric-screen-api-v1.mixins.json into net.minecraft.client.Keyboard
[20:04:01] [Render thread/INFO] (net.minecraft.client.MinecraftClient) Using optional rendering extensions: GL_ARB_buffer_storage, GL_KHR_debug, GL_ARB_vertex_attrib_binding, GL_ARB_direct_state_access
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing LifecycledResourceManagerImplMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.LifecycledResourceManagerImpl
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:LifecycledResourceManagerImplMixin from mod fabric-resource-loader-v0->@Inject::init(Lnet/minecraft/resource/ResourceType;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:LifecycledResourceManagerImplMixin from mod fabric-resource-loader-v0->@Inject::init(Lnet/minecraft/resource/ResourceType;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:LifecycledResourceManagerImplMixin from mod fabric-resource-loader-v0->@Inject::init(Lnet/minecraft/resource/ResourceType;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] Starting internal pack sorting with: [vanilla, ai_villagers, fabric, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-item-group-api-v1_programmer_art, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] Removed all internal packs, result: [vanilla, fabric]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled ai_villagers, currently enabled: [vanilla, fabric, ai_villagers]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-api, currently enabled: [vanilla, fabric, ai_villagers, fabric-api]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-api-base, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-api-lookup-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-biome-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-block-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-block-view-api-v2, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-client-gametest-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-command-api-v2, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-content-registries-v0, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-convention-tags-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-convention-tags-v2, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-crash-report-info-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-data-attachment-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-data-generation-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-dimensions-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-entity-events-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-events-interaction-v0, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-game-rule-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-gametest-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-item-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-item-group-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-key-binding-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-lifecycle-events-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-loot-api-v2, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-loot-api-v3, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-message-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-model-loading-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-networking-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-object-builder-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-particles-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-recipe-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-registry-sync-v0, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-renderer-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-renderer-indigo, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-rendering-fluids-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-rendering-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-resource-conditions-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-resource-loader-v0, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-screen-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-screen-handler-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-sound-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-tag-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-transfer-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-transitive-access-wideners-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabricloader, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] Final sorting result: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] Starting internal pack sorting with: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-item-group-api-v1_programmer_art, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] Removed all internal packs, result: [vanilla, fabric]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled ai_villagers, currently enabled: [vanilla, fabric, ai_villagers]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-api, currently enabled: [vanilla, fabric, ai_villagers, fabric-api]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-api-base, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-api-lookup-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-biome-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-block-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-block-view-api-v2, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-client-gametest-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-command-api-v2, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-content-registries-v0, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-convention-tags-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-convention-tags-v2, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-crash-report-info-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-data-attachment-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-data-generation-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-dimensions-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-entity-events-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-events-interaction-v0, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-game-rule-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-gametest-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-item-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-item-group-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-key-binding-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-lifecycle-events-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-loot-api-v2, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-loot-api-v3, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-message-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-model-loading-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-networking-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-object-builder-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-particles-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-recipe-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-registry-sync-v0, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-renderer-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-renderer-indigo, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-rendering-fluids-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-rendering-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-resource-conditions-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-resource-loader-v0, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-screen-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-screen-handler-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-sound-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-tag-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-transfer-api-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabric-transitive-access-wideners-v1, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] cur @ fabric, auto-enabled fabricloader, currently enabled: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader]
[20:04:02] [Render thread/DEBUG] (net.fabricmc.fabric.impl.resource.loader.ModResourcePackUtil) [Fabric] Final sorting result: [vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader]
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.resource.language.LanguageManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.texture.TextureManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.sound.SoundManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing SoundSystemMixin from fabric-sound-api-v1.mixins.json into net.minecraft.client.sound.SoundSystem
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing FontManagerMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.font.FontManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockColorsMixin from fabric-registry-sync-v0.client.mixins.json into net.minecraft.client.color.block.BlockColors
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockColorsMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.color.block.BlockColors
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:BlockColorsMixin from mod fabric-registry-sync-v0->@Inject::create(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:BlockColorsMixin from mod fabric-registry-sync-v0->@Inject::create(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:BlockColorsMixin from mod fabric-registry-sync-v0->@Inject::create(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:BlockColorsMixin from mod fabric-rendering-v1->@Inject::create(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.render.model.BakedModelManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BakedModelManagerMixin from fabric-model-loading-api-v1.mixins.json into net.minecraft.client.render.model.BakedModelManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$withModelDispatcher$3(Ljava/util/concurrent/CompletableFuture;Ljava/util/function/Function;Ljava/lang/Object;)Ljava/lang/Object; to md3f1c3d$fabric-model-loading-api-v1$lambda$withModelDispatcher$3$0 in fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$hookBlockStateModels$2(Lnet/minecraft/client/render/model/BlockStatesLoader$LoadedModels;Lnet/fabricmc/fabric/impl/client/model/loading/ModelLoadingEventDispatcher;)Lnet/minecraft/client/render/model/BlockStatesLoader$LoadedModels; to md3f1c3d$fabric-model-loading-api-v1$lambda$hookBlockStateModels$2$1 in fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$hookModels$1(Ljava/util/Map;Lnet/fabricmc/fabric/impl/client/model/loading/ModelLoadingEventDispatcher;)Ljava/util/Map; to md3f1c3d$fabric-model-loading-api-v1$lambda$hookModels$1$2 in fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$resetEventDispatcherFuture$0(Ljava/lang/Void;)Ljava/lang/Void; to md3f1c3d$fabric-model-loading-api-v1$lambda$resetEventDispatcherFuture$0$3 in fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1->@Inject::onHeadReload(Lnet/minecraft/resource/ResourceReloader$Synchronizer;Lnet/minecraft/resource/ResourceManager;Ljava/util/concurrent/Executor;Ljava/util/concurrent/Executor;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1->@Inject::onHeadReload(Lnet/minecraft/resource/ResourceReloader$Synchronizer;Lnet/minecraft/resource/ResourceManager;Ljava/util/concurrent/Executor;Ljava/util/concurrent/Executor;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1->@Inject::onHeadReload(Lnet/minecraft/resource/ResourceReloader$Synchronizer;Lnet/minecraft/resource/ResourceManager;Ljava/util/concurrent/Executor;Ljava/util/concurrent/Executor;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1->@Inject::resolveExtraModels(Ljava/util/Map;Lnet/minecraft/client/render/model/BlockStatesLoader$LoadedModels;Lnet/minecraft/client/item/ItemAssetsLoader$Result;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1->@Inject::onUpload(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1->@Inject::onUpload(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1->@Inject::onUpload(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.sprite.ErrorCollectingSpriteGetterMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.model.ErrorCollectingSpriteGetter
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.block.particle.BlockModelsMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.block.BlockModels
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing JsonDataLoaderMixin from fabric-loot-api-v3.mixins.json into net.minecraft.resource.JsonDataLoader
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing JsonDataLoaderMixin from fabric-resource-conditions-api-v1.mixins.json into net.minecraft.resource.JsonDataLoader
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-loot-api-v3.mixins.json:JsonDataLoaderMixin from mod fabric-loot-api-v3->@Inject::fillSourceMap(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/resource/ResourceFinder;Lcom/mojang/serialization/DynamicOps;Lcom/mojang/serialization/Codec;Ljava/util/Map;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-conditions-api-v1.mixins.json:JsonDataLoaderMixin from mod fabric-resource-conditions-api-v1->@Inject::skipData(Ljava/util/Map;Lnet/minecraft/util/Identifier;Ljava/lang/Object;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ItemRenderStateMixin from fabric-renderer-indigo.mixins.json into net.minecraft.client.render.item.ItemRenderState
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:ItemRenderStateMixin from mod fabric-renderer-indigo->@Inject::afterInitVecLoad(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:ItemRenderStateMixin from mod fabric-renderer-indigo->@Inject::afterInitVecLoad(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:ItemRenderStateMixin from mod fabric-renderer-indigo->@Inject::afterInitVecLoad(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:ItemRenderStateMixin from mod fabric-renderer-indigo->@Inject::afterLayerLoad(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:ItemRenderStateMixin from mod fabric-renderer-indigo->@Inject::afterLayerLoad(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:ItemRenderStateMixin from mod fabric-renderer-indigo->@Inject::afterLayerLoad(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.item.ItemRenderStateLayerRenderStateMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.item.ItemRenderState$LayerRenderState
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ItemRenderStateLayerRenderStateMixin from fabric-renderer-indigo.mixins.json into net.minecraft.client.render.item.ItemRenderState$LayerRenderState
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:ItemRenderStateLayerRenderStateMixin from mod fabric-renderer-indigo->@Inject::onReturnClear(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:ItemRenderStateLayerRenderStateMixin from mod fabric-renderer-indigo->@Inject::onReturnClear(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:ItemRenderStateLayerRenderStateMixin from mod fabric-renderer-indigo->@Inject::onReturnClear(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ModelBakerMixin from fabric-model-loading-api-v1.mixins.json into net.minecraft.client.render.model.ModelBaker
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$withExtraModels$2(Lnet/minecraft/client/render/model/ModelBaker$BakedModels;Ljava/util/Map;)Lnet/minecraft/client/render/model/ModelBaker$BakedModels; to md3f1c3d$fabric-model-loading-api-v1$lambda$withExtraModels$2$0 in fabric-model-loading-api-v1.mixins.json:ModelBakerMixin from mod fabric-model-loading-api-v1
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$withExtraModels$1(Lnet/minecraft/client/render/model/ModelBaker$BakerImpl;Lnet/fabricmc/fabric/api/client/model/loading/v1/ExtraModelKey;Lnet/fabricmc/fabric/api/client/model/loading/v1/UnbakedExtraModel;)Ljava/lang/Object; to md3f1c3d$fabric-model-loading-api-v1$lambda$withExtraModels$1$1 in fabric-model-loading-api-v1.mixins.json:ModelBakerMixin from mod fabric-model-loading-api-v1
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$hookBlockModelBake$0(Ljava/util/function/BiFunction;Lnet/minecraft/block/BlockState;Lnet/minecraft/client/render/model/BlockStateModel$UnbakedGrouped;)Lnet/minecraft/client/render/model/BlockStateModel; to md3f1c3d$fabric-model-loading-api-v1$lambda$hookBlockModelBake$0$2 in fabric-model-loading-api-v1.mixins.json:ModelBakerMixin from mod fabric-model-loading-api-v1
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:ModelBakerMixin from mod fabric-model-loading-api-v1->@Inject::onReturnInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:ModelBakerMixin from mod fabric-model-loading-api-v1->@Inject::onReturnInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:ModelBakerMixin from mod fabric-model-loading-api-v1->@Inject::onReturnInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.block.render.BlockRenderManagerMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.block.BlockRenderManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.render.block.BlockRenderManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockRenderManagerAccessor from fabric-renderer-indigo.mixins.json into net.minecraft.client.render.block.BlockRenderManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockRenderManagerMixin from fabric-renderer-indigo.mixins.json into net.minecraft.client.render.block.BlockRenderManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$afterGetModel$0(Lnet/minecraft/client/render/VertexConsumer;Lnet/minecraft/client/render/BlockRenderLayer;)Lnet/minecraft/client/render/VertexConsumer; to md3f1c3d$fabric-renderer-indigo$lambda$afterGetModel$0$0 in fabric-renderer-indigo.mixins.json:BlockRenderManagerMixin from mod fabric-renderer-indigo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:BlockRenderManagerMixin from mod fabric-renderer-indigo->@Inject::afterGetModel(Lnet/minecraft/block/BlockState;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/world/BlockRenderView;Lnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/render/VertexConsumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.block.render.BlockModelRendererMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.block.BlockModelRenderer
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockModelRendererMixin from fabric-renderer-indigo.mixins.json into net.minecraft.client.render.block.BlockModelRenderer
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$render$0(Lnet/minecraft/client/render/VertexConsumer;Lnet/minecraft/client/render/BlockRenderLayer;)Lnet/minecraft/client/render/VertexConsumer; to md3f1c3d$fabric-renderer-indigo$lambda$render$0$0 in fabric-renderer-indigo.mixins.json:BlockModelRendererMixin from mod fabric-renderer-indigo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing FluidRendererMixin from fabric-rendering-fluids-v1.mixins.json into net.minecraft.client.render.block.FluidRenderer
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::onResourceReloadReturn(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::onResourceReloadReturn(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::onResourceReloadReturn(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::onHeadRender(Lnet/minecraft/world/BlockRenderView;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/client/render/VertexConsumer;Lnet/minecraft/block/BlockState;Lnet/minecraft/fluid/FluidState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing HeldItemRendererMixin from fabric-item-api-v1.client.mixins.json into net.minecraft.client.render.item.HeldItemRenderer
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.client.mixins.json:HeldItemRendererMixin from mod fabric-item-api-v1->@Inject::modifyProgressAnimation(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.client.mixins.json:HeldItemRendererMixin from mod fabric-item-api-v1->@Inject::modifyProgressAnimation(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.client.mixins.json:HeldItemRendererMixin from mod fabric-item-api-v1->@Inject::modifyProgressAnimation(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ParticleManagerMixin from fabric-particles-v1.client.mixins.json into net.minecraft.client.particle.ParticleManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ParticleManagerAccessor from fabric-particles-v1.client.mixins.json into net.minecraft.client.particle.ParticleManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ParticleManagerMixin from fabric-registry-sync-v0.client.mixins.json into net.minecraft.client.particle.ParticleManager
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-particles-v1.client.mixins.json:ParticleManagerMixin from mod fabric-particles-v1->@Inject::onRegisterDefaultFactories(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-particles-v1.client.mixins.json:ParticleManagerMixin from mod fabric-particles-v1->@Inject::onRegisterDefaultFactories(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-particles-v1.client.mixins.json:ParticleManagerMixin from mod fabric-particles-v1->@Inject::onRegisterDefaultFactories(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ParticleManagerMixin from mod fabric-registry-sync-v0->@Inject::onInit(Lnet/minecraft/client/world/ClientWorld;Lnet/minecraft/client/texture/TextureManager;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ParticleManagerMixin from mod fabric-registry-sync-v0->@Inject::onInit(Lnet/minecraft/client/world/ClientWorld;Lnet/minecraft/client/texture/TextureManager;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ParticleManagerMixin from mod fabric-registry-sync-v0->@Inject::onInit(Lnet/minecraft/client/world/ClientWorld;Lnet/minecraft/client/texture/TextureManager;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.block.particle.BlockDustParticleMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.particle.BlockDustParticle
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockDustParticleMixin from fabric-particles-v1.client.mixins.json into net.minecraft.client.particle.BlockDustParticle
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ParticleManagerAccessor$SimpleSpriteProviderAccessor from fabric-particles-v1.client.mixins.json into net.minecraft.client.particle.ParticleManager$SimpleSpriteProvider
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.block.particle.BlockMarkerParticleMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.particle.BlockMarkerParticle
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing GameRendererMixin from fabric-screen-api-v1.mixins.json into net.minecraft.client.render.GameRenderer
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing GuiRendererMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.gui.render.GuiRenderer
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:GuiRendererMixin from mod fabric-rendering-v1->@Inject::mutableSpecialElementRenderers(Lnet/minecraft/client/gui/render/state/GuiRenderState;Lnet/minecraft/client/render/VertexConsumerProvider$Immediate;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:GuiRendererMixin from mod fabric-rendering-v1->@Inject::mutableSpecialElementRenderers(Lnet/minecraft/client/gui/render/state/GuiRenderState;Lnet/minecraft/client/render/VertexConsumerProvider$Immediate;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:GuiRendererMixin from mod fabric-rendering-v1->@Inject::mutableSpecialElementRenderers(Lnet/minecraft/client/gui/render/state/GuiRenderState;Lnet/minecraft/client/render/VertexConsumerProvider$Immediate;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.block.particle.InGameOverlayRendererMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.gui.hud.InGameOverlayRenderer
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.block.particle.InGameOverlayRendererMixin from mod fabric-renderer-api-v1->@Inject::onReturnGetInWallBlockState(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.render.WorldRenderer
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing WorldRendererMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.render.WorldRenderer
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$beforeClouds$0()V to md3f1c3d$fabric-rendering-v1$lambda$beforeClouds$0$0 in fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRender(Lnet/minecraft/client/util/ObjectAllocator;Lnet/minecraft/client/render/RenderTickCounter;ZLnet/minecraft/client/render/Camera;Lorg/joml/Matrix4f;Lorg/joml/Matrix4f;Lcom/mojang/blaze3d/buffers/GpuBufferSlice;Lorg/joml/Vector4f;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRender(Lnet/minecraft/client/util/ObjectAllocator;Lnet/minecraft/client/render/RenderTickCounter;ZLnet/minecraft/client/render/Camera;Lorg/joml/Matrix4f;Lorg/joml/Matrix4f;Lcom/mojang/blaze3d/buffers/GpuBufferSlice;Lorg/joml/Vector4f;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRender(Lnet/minecraft/client/util/ObjectAllocator;Lnet/minecraft/client/render/RenderTickCounter;ZLnet/minecraft/client/render/Camera;Lorg/joml/Matrix4f;Lorg/joml/Matrix4f;Lcom/mojang/blaze3d/buffers/GpuBufferSlice;Lorg/joml/Vector4f;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSetup(Lnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/Frustum;ZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSetup(Lnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/Frustum;ZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSetup(Lnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/Frustum;ZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeTerrainSolid(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeTerrainSolid(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeTerrainSolid(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSolid(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSolid(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSolid(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRenderOutline(Lnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/VertexConsumerProvider$Immediate;Lnet/minecraft/client/util/math/MatrixStack;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRenderOutline(Lnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/VertexConsumerProvider$Immediate;Lnet/minecraft/client/util/math/MatrixStack;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRenderOutline(Lnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/VertexConsumerProvider$Immediate;Lnet/minecraft/client/util/math/MatrixStack;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onDrawBlockOutline(Lnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/VertexConsumerProvider$Immediate;Lnet/minecraft/client/util/math/MatrixStack;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeDebugRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeDebugRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeDebugRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onFinishWritingFramebuffer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onFinishWritingFramebuffer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onFinishWritingFramebuffer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onReload(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onReload(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onReload(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::renderWeather(Lnet/minecraft/client/render/FrameGraphBuilder;Lnet/minecraft/util/math/Vec3d;FLcom/mojang/blaze3d/buffers/GpuBufferSlice;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::renderCloud(Lnet/minecraft/client/render/FrameGraphBuilder;Lnet/minecraft/client/option/CloudRenderMode;Lnet/minecraft/util/math/Vec3d;FIFLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::renderSky(Lnet/minecraft/client/render/FrameGraphBuilder;Lnet/minecraft/client/render/Camera;FLcom/mojang/blaze3d/buffers/GpuBufferSlice;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing InGameHudMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.gui.hud.InGameHud
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapSubtitlesHud$23(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapSubtitlesHud$23$0 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapPlayerList$22(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapPlayerList$22$1 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapChat$21(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapChat$21$2 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapTitleAndSubtitle$20(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapTitleAndSubtitle$20$3 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapOverlayMessage$19(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapOverlayMessage$19$4 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapScoreboardSidebar$18(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapScoreboardSidebar$18$5 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapDebugHud$17(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapDebugHud$17$6 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapDemoTimer$16(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapDemoTimer$16$7 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapSleepOverlay$15(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapSleepOverlay$15$8 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapBossBarHud$14(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapBossBarHud$14$9 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapStatusEffectOverlay$13(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapStatusEffectOverlay$13$a in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapRenderSpectatorHud$12(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/SpectatorHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapRenderSpectatorHud$12$b in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapHeldItemTooltip$11(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapHeldItemTooltip$11$c in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapExperienceLevel$10(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/font/TextRenderer;ILnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapExperienceLevel$10$d in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapRenderBar$9(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/bar/Bar;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapRenderBar$9$e in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapMountHealth$8(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapMountHealth$8$f in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapAirBar$7(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/entity/player/PlayerEntity;IIILnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapAirBar$7$10 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapFoodBar$6(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/entity/player/PlayerEntity;IILnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapFoodBar$6$11 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapHealthBar$5(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/entity/player/PlayerEntity;IIIIFIIIZLnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapHealthBar$5$12 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapArmorBar$4(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/entity/player/PlayerEntity;IIIILnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapArmorBar$4$13 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapHotbar$3(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapHotbar$3$14 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapSpectatorMenu$2(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/SpectatorHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapSpectatorMenu$2$15 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapCrosshair$1(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapCrosshair$1$16 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$wrapMiscOverlays$0(Lcom/llamalad7/mixinextras/injector/wrapoperation/Operation;Lnet/minecraft/client/gui/hud/InGameHud;Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;)V to md3f1c3d$fabric-rendering-v1$lambda$wrapMiscOverlays$0$17 in fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1->@Inject::render(Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1->@Inject::render(Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1->@Inject::render(Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/client/render/RenderTickCounter;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing DebugHudMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.gui.hud.DebugHud
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:DebugHudMixin from mod fabric-rendering-v1->@Inject::getLeftText(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:DebugHudMixin from mod fabric-rendering-v1->@Inject::getLeftText(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:DebugHudMixin from mod fabric-rendering-v1->@Inject::getRightText(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:DebugHudMixin from mod fabric-rendering-v1->@Inject::getRightText(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ResourceMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.Resource
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MessageHandlerMixin from fabric-message-api-v1.client.mixins.json into net.minecraft.client.network.message.MessageHandler
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:MessageHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_onSignedChatMessage(Lnet/minecraft/network/message/MessageType$Parameters;Lnet/minecraft/network/message/SignedMessage;Lnet/minecraft/text/Text;Lcom/mojang/authlib/GameProfile;ZLjava/time/Instant;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:MessageHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_onFilteredSignedChatMessage(Lnet/minecraft/network/message/MessageType$Parameters;Lnet/minecraft/network/message/SignedMessage;Lnet/minecraft/text/Text;Lcom/mojang/authlib/GameProfile;ZLjava/time/Instant;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:MessageHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_onProfilelessChatMessage(Lnet/minecraft/network/message/MessageType$Parameters;Lnet/minecraft/text/Text;Ljava/time/Instant;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:MessageHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_allowGameMessage(Lnet/minecraft/text/Text;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing DrawContextMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.gui.DrawContext
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:DrawContextMixin from mod fabric-rendering-v1->@Inject::drawStackOverlay(Lnet/minecraft/client/font/TextRenderer;Lnet/minecraft/item/ItemStack;IILjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:DrawContextMixin from mod fabric-rendering-v1->@Inject::drawStackOverlay(Lnet/minecraft/client/font/TextRenderer;Lnet/minecraft/item/ItemStack;IILjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:DrawContextMixin from mod fabric-rendering-v1->@Inject::drawStackOverlay(Lnet/minecraft/client/font/TextRenderer;Lnet/minecraft/item/ItemStack;IILjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:DrawContextMixin from mod fabric-rendering-v1->@Inject::drawStackOverlay(Lnet/minecraft/client/font/TextRenderer;Lnet/minecraft/item/ItemStack;IILjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:DrawContextMixin from mod fabric-rendering-v1->@Inject::drawStackOverlay(Lnet/minecraft/client/font/TextRenderer;Lnet/minecraft/item/ItemStack;IILjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:DrawContextMixin from mod fabric-rendering-v1->@Inject::drawStackOverlay(Lnet/minecraft/client/font/TextRenderer;Lnet/minecraft/item/ItemStack;IILjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[20:04:03] [Render thread/INFO] (net.minecraft.resource.ReloadableResourceManagerImpl) Reloading ResourceManager: vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing SimpleResourceReloadMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.SimpleResourceReload
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing modification.DynamicRegistryManagerImmutableImplMixin from fabric-biome-api-v1.mixins.json into net.minecraft.registry.DynamicRegistryManager$ImmutableImpl
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.sprite.SpriteLoaderStitchResultMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.texture.SpriteLoader$StitchResult
[20:04:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.sprite.SpriteAtlasManagerAtlasPreparationMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.model.SpriteAtlasManager$AtlasPreparation
[20:04:03] [Worker-Main-7/INFO] (net.minecraft.client.font.UnihexFont) Found unifont_all_no_pua-16.0.03.hex, loading
[20:04:03] [Worker-Main-3/INFO] (net.minecraft.client.font.UnihexFont) Found unifont_pua-16.0.03.hex, loading
[20:04:03] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) Mixing EntityModelsMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.render.entity.model.EntityModels
[20:04:03] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:EntityModelsMixin from mod fabric-rendering-v1->@Inject::registerExtraModelData(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[20:04:03] [Render thread/DEBUG] (com.mojang.blaze3d.systems.RenderSystem) Growing IndexBuffer: Old limit 0, new limit 60.
[20:04:04] [Worker-Main-6/DEBUG] (FabricLoader/Mixin) Mixing JsonUnbakedModelAccessor from fabric-model-loading-api-v1.mixins.json into net.minecraft.client.render.model.json.JsonUnbakedModel
[20:04:04] [Worker-Main-6/DEBUG] (FabricLoader/Mixin) Mixing JsonUnbakedModelMixin from fabric-model-loading-api-v1.mixins.json into net.minecraft.client.render.model.json.JsonUnbakedModel
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) Mixing RenderMixins.VillagerEntityRenderStateMixin from ai_villagers.client.mixins.json into net.minecraft.client.render.entity.state.VillagerEntityRenderState
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) ai_villagers.client.mixins.json:RenderMixins.VillagerEntityRenderStateMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) Mixing FisherMixins.ItemHolderEntityRenderStateMixin from ai_villagers.client.mixins.json into net.minecraft.client.render.entity.state.ItemHolderEntityRenderState
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) ai_villagers.client.mixins.json:FisherMixins.ItemHolderEntityRenderStateMixin from mod ai_villagers: Class version 67 required is higher than the class version supported by the current version of Mixin (JAVA_22 supports class version 66)
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) ai_villagers.client.mixins.json:FisherMixins.ItemHolderEntityRenderStateMixin from mod ai_villagers->@Inject::captureEntity(Lnet/minecraft/entity/LivingEntity;Lnet/minecraft/client/render/entity/state/ItemHolderEntityRenderState;Lnet/minecraft/client/item/ItemModelManager;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) Mixing EntityModelLayersMixin from fabric-object-builder-v1.client.mixins.json into net.minecraft.client.render.entity.model.EntityModelLayers
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$createHangingSign$0(Lnet/minecraft/client/render/block/entity/HangingSignBlockEntityRenderer$AttachmentType;Ljava/lang/String;)Ljava/lang/String; to md3f1c3d$fabric-object-builder-api-v1$lambda$createHangingSign$0$0 in fabric-object-builder-v1.client.mixins.json:EntityModelLayersMixin from mod fabric-object-builder-api-v1
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) Mixing EntityModelLayersAccessor from fabric-rendering-v1.mixins.json into net.minecraft.client.render.entity.model.EntityModelLayers
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getLayers()Ljava/util/Set; to getLayers$fabric-rendering-v1_$md$3f1c3d$1 in fabric-rendering-v1.mixins.json:EntityModelLayersAccessor from mod fabric-rendering-v1
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.client.mixins.json:EntityModelLayersMixin from mod fabric-object-builder-api-v1->@Inject::createStandingSign(Lnet/minecraft/block/WoodType;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.client.mixins.json:EntityModelLayersMixin from mod fabric-object-builder-api-v1->@Inject::createWallSign(Lnet/minecraft/block/WoodType;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:04] [Worker-Main-5/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.client.mixins.json:EntityModelLayersMixin from mod fabric-object-builder-api-v1->@Inject::createHangingSign(Lnet/minecraft/block/WoodType;Lnet/minecraft/client/render/block/entity/HangingSignBlockEntityRenderer$AttachmentType;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[20:04:04] [Worker-Main-7/INFO] (net.minecraft.client.font.UnihexFont) Found unifont_jp_patch-16.0.03.hex, loading
[20:04:05] [Worker-Main-4/DEBUG] (FabricLoader/Mixin) Mixing client.block.model.MultipartBlockStateModelMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.model.MultipartBlockStateModel
[20:04:05] [Worker-Main-4/DEBUG] (FabricLoader/Mixin) Mixing client.block.model.WeightedBlockStateModelMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.model.WeightedBlockStateModel
