/tick rate 120
/time set 8400
/tick rate 120
/tick rate 20
/time set 8400
/tick rate 120
/time set 8400
/tick rate 20
/data get entity @e[type=minecraft:villager,limit=1,sort=nearest] Inventory
/time set 1000
/time set 8400
/time set 1000
/time set 8500
/data get entity @e[type=minecraft:villager,limit=1,sort=nearest] Inventory
/time set 1000
/tick rate 240
/data get entity @e[type=minecraft:villager,limit=1,sort=nearest] Inventory
/tick rate 240
/time set 1000
/data get entity @e[type=minecraft:villager,limit=1,sort=nearest] Inventory
/tick rate 20
/time set 1000
/data get entity @e[type=minecraft:villager,limit=1,sort=nearest] Inventory
/tick rate 120
/tick rate 20
/time set 1000
/tick rate 120
/tick rate 20
/data get entity @e[type=minecraft:villager,limit=1,sort=nearest] Inventory
/time set day
/tick rate 120
/tick rate 20
/data get entity @e[type=minecraft:villager,limit=1,sort=nearest] Inventory
/time set 1000
/weather clear
/time set day
/time set 8400
/time set 1000
/time set 8400
/time set day
/tick rate 120
/tick rate 20
/tick rate 120
/tick rate 20
/tick rate 120
/tick rate 20
/time set day
/tick rate 120
/time set 1000
/tick rate 120
